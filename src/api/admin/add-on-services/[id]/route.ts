import type { MedusaRequest, MedusaResponse } from "@camped-ai/framework";
import { z } from "zod";
import {
  ADD_ON_SERVICE,
  AddOnServiceLevel,
} from "../../../../modules/hotel-management/add-on-service";
import { registerAddOnServiceModule } from "../../hotel-management/add-on-services/register-module";

// Multi-currency price schema for updates
const CurrencyPriceSchema = z.object({
  adult_price: z.number().optional(),
  child_price: z.number().optional(),
  package_price: z.number().optional(),
  per_day_adult_price: z.number().optional(),
  per_day_child_price: z.number().optional(),
});

// Validation schema for updating an add-on service
const UpdateAddOnServiceSchema = z.object({
  name: z.string().optional(),
  description: z.string().optional(),
  type: z.string().optional(),
  service_level: z.nativeEnum(AddOnServiceLevel).optional(),
  hotel_id: z.union([z.string(), z.array(z.string())]).optional(),
  destination_id: z.union([z.string(), z.array(z.string())]).optional(),
  category_id: z.string().optional(),
  is_active: z.boolean().optional(),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  max_capacity: z.number().optional(),

  // Pricing type
  pricing_type: z.enum(["per_person", "package", "usage_based"]).optional(),

  // Multi-currency pricing
  prices: z.record(z.string(), CurrencyPriceSchema).optional(),
  default_currency: z.string().optional(),

  // Legacy single-currency pricing (for backward compatibility)
  adult_price: z.number().optional(),
  child_price: z.number().optional(),
  package_price: z.number().optional(),
  per_day_adult_price: z.number().optional(),
  per_day_child_price: z.number().optional(),
  currency_code: z.string().optional(),

  images: z.array(z.string()).optional(),
  metadata: z.record(z.any()).optional(),
});

// GET /admin/add-on-services/:id
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Register the add-on service module
    registerAddOnServiceModule(req.scope);

    // Check if the service is registered
    if (!req.scope.hasRegistration(ADD_ON_SERVICE)) {
      return res.status(500).json({
        message: "Add-on service module is not registered",
      });
    }

    const { id } = req.params;
    const { currency_code } = req.query;
    const addOnServiceService: any = req.scope.resolve(ADD_ON_SERVICE);

    // Get add-on service with optional currency context
    const addOnService = await addOnServiceService.retrieveAddOnService(id, currency_code as string);

    res.json({
      add_on_service: addOnService,
    });
  } catch (error) {
    console.error(`Error retrieving add-on service ${req.params.id}:`, error);
    res.status(404).json({
      message: "Add-on service not found",
      error: error.message,
    });
  }
}

// PUT /admin/add-on-services/:id
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Register the add-on service module
    registerAddOnServiceModule(req.scope);

    // Check if the service is registered
    if (!req.scope.hasRegistration(ADD_ON_SERVICE)) {
      return res.status(500).json({
        message: "Add-on service module is not registered",
      });
    }

    const { id } = req.params;
    const addOnServiceService: any = req.scope.resolve(ADD_ON_SERVICE);

    // Get the existing service
    const existingService = await addOnServiceService.retrieveAddOnService(id);
    console.log(
      `Updating service ${id} with level ${existingService.service_level}`
    );

    // Validate request body
    const validatedData = UpdateAddOnServiceSchema.parse(req.body);

    // Handle destination_id - keep array format for service module
    if (
      validatedData.service_level === AddOnServiceLevel.DESTINATION &&
      Array.isArray(validatedData.destination_id) &&
      validatedData.destination_id.length > 0
    ) {
      console.log(
        `API received destination_id as array:`,
        validatedData.destination_id
      );
      console.log(`Keeping destination_id as array for service module`);
    }

    // Ensure service_level is not changed
    if (
      validatedData.service_level &&
      validatedData.service_level !== existingService.service_level
    ) {
      return res.status(400).json({
        message: "Cannot change service level",
      });
    }

    // Handle hotel_id - keep as array for multi-select functionality
    if (
      validatedData.service_level === AddOnServiceLevel.HOTEL &&
      Array.isArray(validatedData.hotel_id) &&
      validatedData.hotel_id.length > 0
    ) {
      console.log(`API received hotel_id as array:`, validatedData.hotel_id);
      console.log(`Keeping hotel_id as array for service module`);
    }

    // Log the data being sent to the service
    console.log("Updating add-on service with data:", {
      id,
      name: validatedData.name,
      service_level: validatedData.service_level,
      hotel_id: validatedData.hotel_id,
      destination_id: validatedData.destination_id,
    });

    // Update add-on service
    const addOnService = await addOnServiceService.updateAddOnService(
      id,
      validatedData
    );

    res.json({
      add_on_service: addOnService,
    });
  } catch (error) {
    console.error(`Error updating add-on service ${req.params.id}:`, error);
    if (error instanceof z.ZodError) {
      res.status(400).json({
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      res.status(500).json({
        message: "Failed to update add-on service",
        error: error.message,
      });
    }
  }
}

// DELETE /admin/add-on-services/:id
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    // Register the add-on service module
    registerAddOnServiceModule(req.scope);

    // Check if the service is registered
    if (!req.scope.hasRegistration(ADD_ON_SERVICE)) {
      return res.status(500).json({
        message: "Add-on service module is not registered",
      });
    }

    const { id } = req.params;
    const addOnServiceService: any = req.scope.resolve(ADD_ON_SERVICE);

    // Get the service to verify it exists
    try {
      const existingService = await addOnServiceService.retrieveAddOnService(
        id
      );
      console.log(
        `Found add-on service with ID ${id} and level ${existingService.service_level}`
      );
    } catch (error) {
      console.error(`Service with ID ${id} not found:`, error.message);
      return res.status(404).json({
        message: "Add-on service not found",
        error: error.message,
      });
    }

    // Delete add-on service
    await addOnServiceService.deleteAddOnService(id);

    res.status(204).end();
  } catch (error) {
    console.error(`Error deleting add-on service ${req.params.id}:`, error);
    res.status(500).json({
      message: "Failed to delete add-on service",
      error: error.message,
    });
  }
}
