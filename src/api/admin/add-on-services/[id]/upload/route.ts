import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { MedusaError } from "@camped-ai/framework/utils";
import { uploadFilesWorkflow } from "@camped-ai/medusa/core-flows";
import { ADD_ON_SERVICE } from "../../../../../modules/hotel-management/add-on-service";
import { registerAddOnServiceModule } from "../../../../admin/hotel-management/add-on-services/register-module";

export async function POST(req: MedusaRequest, res: MedusaResponse) {
  const addOnServiceId = req.params.id;
  //@ts-ignore
  const files = req.files as Express.Multer.File[];

  if (!files?.length) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "No files were uploaded"
    );
  }

  // Process each file (frontend sends one at a time, but we can handle multiple)
  if (files.length === 0) {
    throw new MedusaError(
      MedusaError.Types.INVALID_DATA,
      "No files provided for upload"
    );
  }

  try {
    // Register the add-on service module
    registerAddOnServiceModule(req.scope);

    // Get the add-on service
    const addOnServiceService: any = req.scope.resolve(ADD_ON_SERVICE);

    // Verify the add-on service exists
    const existingService = await addOnServiceService.retrieveAddOnService(
      addOnServiceId
    );
    if (!existingService) {
      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Add-on service with id ${addOnServiceId} not found`
      );
    }

    // Upload the file using the file module
    console.log("Uploading image for add-on service...");
    const { result } = await uploadFilesWorkflow(req.scope).run({
      input: {
        files: files.map((f) => ({
          filename: f.originalname,
          mimeType: f.mimetype,
          content: f.buffer.toString("binary"),
          access: "public",
        })),
      },
    });

    console.log("Upload result:", result);

    if (!result || result.length === 0) {
      throw new MedusaError(
        MedusaError.Types.UNEXPECTED_STATE,
        "File upload failed"
      );
    }

    const uploadedFile = result[0];

    // Update the add-on service with the new image (appends to existing images)
    const updatedService = await addOnServiceService.uploadImage(
      addOnServiceId,
      uploadedFile.url
    );

    res.status(200).json({
      add_on_service: updatedService,
      uploaded_image: {
        url: uploadedFile.url,
        filename: files[0].originalname,
      },
    });
  } catch (error) {
    console.error("Error uploading image for add-on service:", error);

    if (error instanceof MedusaError) {
      throw error;
    }

    throw new MedusaError(
      MedusaError.Types.UNEXPECTED_STATE,
      `Failed to upload image: ${error.message}`
    );
  }
}
