import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { PostAdminUpdateRoomConfig } from "../validators";
import { Modules } from "@camped-ai/framework/utils";

type PostAdminUpdateRoomConfigType = z.infer<typeof PostAdminUpdateRoomConfig>;

export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(
      `=== GET /admin/hotel-management/room-configs/${req.params.id} ===`
    );
    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const roomConfigId = req.params.id;

    // Get the product that represents the room configuration
    const product = await productModuleService.retrieveProduct(roomConfigId, {
      relations: ["categories"],
    });

    if (!product) {
      console.error(`Room config with ID: ${roomConfigId} not found`);
      return res.status(404).json({
        message: `Room configuration with id "${roomConfigId}" not found`,
      });
    }

    // Transform the product to a room configuration
    const roomConfig = {
      id: product.id,
      name: product.title,
      type: product.metadata?.type || "standard",
      description: product.description,
      room_size: product.metadata?.room_size,
      bed_type: product.metadata?.bed_type,
      max_extra_beds: product.metadata?.max_extra_beds || 0,
      max_cots: product.metadata?.max_cots || 0,
      max_adults: product.metadata?.max_adults || 1,
      max_adults_beyond_capacity:
        product.metadata?.max_adults_beyond_capacity || 0,
      max_children: product.metadata?.max_children || 0,
      max_infants: product.metadata?.max_infants || 0,
      max_occupancy: product.metadata?.max_occupancy || 1,
      amenities: product.metadata?.amenities || [],
      hotel_id: product.metadata?.hotel_id,
    };

    console.log(`Found room config:`, roomConfig);
    res.json({ roomConfig });
  } catch (error) {
    console.error("Error fetching room config:", error);
    res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to fetch room configuration",
    });
  }
};

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateRoomConfigType>,
  res: MedusaResponse
) => {
  try {
    console.log(
      `=== PUT /admin/hotel-management/room-configs/${req.params.id} ===`
    );
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    const roomConfigId = req.params.id;
    console.log(
      `Received update request for room config with ID: ${roomConfigId}`
    );

    // Debug: Log the incoming request data
    console.log("DEBUG: Request body fields:", {
      max_extra_beds: req.body.max_extra_beds,
      max_cots: req.body.max_cots,
      max_adults_beyond_capacity: req.body.max_adults_beyond_capacity,
    });

    // Get the product that represents the room configuration
    const product = await productModuleService.retrieveProduct(roomConfigId, {
      relations: ["categories"],
    });

    if (!product) {
      console.error(`Room config with ID: ${roomConfigId} not found`);
      return res.status(404).json({
        message: `Room configuration with id "${roomConfigId}" not found`,
      });
    }

    // Update the product
    const updatedProduct = await productModuleService.updateProducts(
      roomConfigId,
      {
        title: req.body.name,
        description: req.body.description,
        metadata: {
          ...product.metadata,
          type: req.body.type,
          room_size: req.body.room_size,
          bed_type: req.body.bed_type,
          max_extra_beds: req.body.max_extra_beds,
          max_cots: req.body.max_cots,
          max_adults: req.body.max_adults,
          max_adults_beyond_capacity: req.body.max_adults_beyond_capacity,
          max_children: req.body.max_children,
          max_infants: req.body.max_infants,
          max_occupancy: req.body.max_occupancy,
          amenities: req.body.amenities,
          hotel_id: req.body.hotel_id || product.metadata?.hotel_id,
        },
      }
    );

    // Debug: Log the updated product metadata
    console.log("DEBUG: Updated product metadata:", {
      max_extra_beds: updatedProduct.metadata?.max_extra_beds,
      max_cots: updatedProduct.metadata?.max_cots,
      max_adults_beyond_capacity:
        updatedProduct.metadata?.max_adults_beyond_capacity,
    });

    // Transform the product to a room configuration
    const roomConfig = {
      id: updatedProduct.id,
      name: updatedProduct.title,
      type: updatedProduct.metadata?.type || "standard",
      description: updatedProduct.description,
      room_size: updatedProduct.metadata?.room_size,
      bed_type: updatedProduct.metadata?.bed_type,
      max_extra_beds: updatedProduct.metadata?.max_extra_beds || 0,
      max_cots: updatedProduct.metadata?.max_cots || 0,
      max_adults: updatedProduct.metadata?.max_adults || 1,
      max_adults_beyond_capacity:
        updatedProduct.metadata?.max_adults_beyond_capacity || 0,
      max_children: updatedProduct.metadata?.max_children || 0,
      max_infants: updatedProduct.metadata?.max_infants || 0,
      max_occupancy: updatedProduct.metadata?.max_occupancy || 1,
      amenities: updatedProduct.metadata?.amenities || [],
      hotel_id: updatedProduct.metadata?.hotel_id,
    };

    console.log(
      "Product update successful, returning room config:",
      roomConfig
    );
    res.json({ roomConfig });
  } catch (error) {
    console.error("Error updating room config:", error);

    // Check if it's a not found error
    if (error instanceof Error && error.message.includes("not found")) {
      return res.status(404).json({
        message: error.message,
      });
    }

    res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to update room configuration",
    });
  }
};

export const DELETE = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log(
      `=== DELETE /admin/hotel-management/room-configs/${req.params.id} ===`
    );
    const productModuleService = req.scope.resolve(Modules.PRODUCT);

    const roomConfigId = req.params.id;
    console.log(
      `Received delete request for room config with ID: ${roomConfigId}`
    );

    // First check if the product exists
    try {
      const product = await productModuleService.retrieveProduct(roomConfigId);
      console.log(`Found product with ID ${roomConfigId}:`, product.title);

      // Delete the product
      console.log(`Attempting to delete product with ID ${roomConfigId}`);

      // Try to use the correct method based on what's available
      if (typeof productModuleService.deleteProducts === "function") {
        console.log("Using deleteProducts method");
        await productModuleService.deleteProducts([roomConfigId]);
      } else {
        // If we can't find the right method, try a more direct approach
        console.log("Using direct API call to delete product");
        const response = await fetch(`/admin/products/${roomConfigId}`, {
          method: "DELETE",
          credentials: "include",
          headers: {
            "Content-Type": "application/json",
          },
        });

        if (!response.ok) {
          throw new Error(`Failed to delete product: ${response.statusText}`);
        }
      }

      console.log(`Successfully deleted product with ID ${roomConfigId}`);
      return res.json({ id: roomConfigId, deleted: true });
    } catch (error) {
      console.error(`Error deleting product with ID ${roomConfigId}:`, error);
      return res.status(404).json({
        message: `Room configuration with id "${roomConfigId}" not found: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      });
    }
  } catch (error) {
    console.error("Error deleting room config:", error);

    // Check if it's a not found error
    if (error instanceof Error && error.message.includes("not found")) {
      return res.status(404).json({
        message: error.message,
      });
    }

    return res.status(400).json({
      message:
        error instanceof Error
          ? error.message
          : "Failed to delete room configuration",
    });
  }
};
