import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for bulk seasonal pricing
export const PostAdminBulkSeasonalPricing = z.object({
  currency_code: z.string(),
  name: z.string(),
  start_date: z.string(),
  end_date: z.string(),
  weekday_rules: z.array(
    z.object({
      occupancy_type_id: z.string(),
      meal_plan_id: z.string(),
      weekday_prices: z.object({
        mon: z.number(),
        tue: z.number(),
        wed: z.number(),
        thu: z.number(),
        fri: z.number(),
        sat: z.number(),
        sun: z.number(),
      }),
    })
  ),
});

export type PostAdminBulkSeasonalPricingType = z.infer<typeof PostAdminBulkSeasonalPricing>;

/**
 * POST /admin/hotel-management/room-configs/:id/seasonal-pricing/bulk
 *
 * Bulk create or update seasonal pricing rules for a room configuration
 */
export const POST = async (req: MedusaRequest<PostAdminBulkSeasonalPricingType>, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    // Validate the request body
    const { currency_code, name, start_date, end_date, weekday_rules } = req.body;

    console.log(`Bulk creating seasonal pricing for room config: ${roomConfigId}`);
    console.log(`Season: ${name}, ${start_date} to ${end_date}`);
    console.log(`Rules: ${weekday_rules.length}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // Create the seasonal price rules
    const createdRules = [];

    try {
      // Get all existing base price rules for this room configuration filtered by currency
      const existingBasePriceRules = await hotelPricingService.listBasePriceRules({
        room_config_id: roomConfigId,
        currency_code: currency_code,
      });

      console.log(`Found ${existingBasePriceRules.length} base price rules for ${currency_code} currency`);

      // First, find any existing seasonal overrides instead of deleting them
      // We'll compare them with the new data and only update if there are changes
      const existingOverrides: Record<string, any[]> = {};

      // Also track all overlapping seasonal price rules that need to be made inactive
      const overlappingRules: Record<string, any[]> = {};

      try {
        // For each base price rule, we'll check if there are existing overrides
        for (const basePriceRule of existingBasePriceRules) {
          // Find existing overrides with the same parameters
          const overrides = await hotelPricingService.listSeasonalPriceRules({
            base_price_rule_id: basePriceRule.id,
          });

          // Filter to match the exact date range, name, and currency (for direct updates)
          const matchingOverrides = overrides.filter(override => {
            const overrideStartDate = override.start_date.toISOString().split('T')[0];
            const overrideEndDate = override.end_date.toISOString().split('T')[0];
            const requestStartDate = new Date(start_date).toISOString().split('T')[0];
            const requestEndDate = new Date(end_date).toISOString().split('T')[0];
            const overrideCurrency = override.currency_code || basePriceRule.currency_code || 'USD';

            return overrideStartDate === requestStartDate &&
                   overrideEndDate === requestEndDate &&
                   override.description === name &&
                   overrideCurrency === currency_code;
          });

          if (matchingOverrides && matchingOverrides.length > 0) {
            // Store existing overrides by base price rule ID
            existingOverrides[basePriceRule.id] = matchingOverrides;
          }

          // Find any other seasonal rules that overlap with the new date range
          // These will need to be made inactive
          const startDateObj = new Date(start_date);
          const endDateObj = new Date(end_date);

          const otherOverlappingRules = overrides.filter(override => {
            // Only consider overrides for the same currency
            const overrideCurrency = override.currency_code || basePriceRule.currency_code || 'USD';
            if (overrideCurrency !== currency_code) {
              return false;
            }

            // Skip the exact matches we already found
            const overrideStartDate = override.start_date.toISOString().split('T')[0];
            const overrideEndDate = override.end_date.toISOString().split('T')[0];
            const requestStartDate = new Date(start_date).toISOString().split('T')[0];
            const requestEndDate = new Date(end_date).toISOString().split('T')[0];

            const isExactMatch = overrideStartDate === requestStartDate &&
                               overrideEndDate === requestEndDate &&
                               override.description === name;

            if (isExactMatch) return false;

            // Check for date range overlap
            const ruleStart = override.start_date;
            const ruleEnd = override.end_date;

            // Check if the date ranges overlap
            const hasOverlap = (
              (startDateObj <= ruleEnd && startDateObj >= ruleStart) || // New start date is within existing rule
              (endDateObj <= ruleEnd && endDateObj >= ruleStart) ||     // New end date is within existing rule
              (startDateObj <= ruleStart && endDateObj >= ruleEnd)      // New rule completely contains existing rule
            );

            return hasOverlap;
          });

          if (otherOverlappingRules && otherOverlappingRules.length > 0) {
            overlappingRules[basePriceRule.id] = otherOverlappingRules;
          }
        }

        console.log(`Found ${Object.keys(existingOverrides).length} existing seasonal override groups`);
        console.log(`Found ${Object.values(overlappingRules).flat().length} overlapping seasonal rules that need to be updated`);
      } catch (error) {
        console.warn("Error finding existing seasonal overrides:", error);
        // Continue with creating new overrides
      }

      // For each weekday rule, find the corresponding base price rule and create a seasonal override
      for (const rule of weekday_rules) {
        try {
          // Find the base price rule for this occupancy type and meal plan
          const basePriceRule = existingBasePriceRules.find(
            (bpr) =>
              bpr.occupancy_type_id === rule.occupancy_type_id &&
              bpr.meal_plan_id === rule.meal_plan_id
          );

          if (!basePriceRule) {
            console.warn(`No base price rule found for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id} in ${currency_code}`);
            continue;
          }

          // Check if there's an existing override for this base price rule
          let savedOverride: any;
          const existingRuleOverrides = existingOverrides[basePriceRule.id] || [];

          // Try to find an existing override for this base price rule with matching dates and name
          console.log(`Looking for existing override: base_price_rule_id=${basePriceRule.id}, name=${name}, start_date=${start_date}, end_date=${end_date}`);
          console.log(`Available overrides for this base price rule:`, existingRuleOverrides.map(o => `${o.id}: ${o.description}, ${o.start_date} to ${o.end_date}`));

          const existingOverride = existingRuleOverrides.find((override: any) => {
            const matchesRule = override.base_price_rule_id === basePriceRule.id;
            const matchesName = override.description === name;
            const matchesStartDate = new Date(override.start_date).toISOString() === new Date(start_date).toISOString();
            const matchesEndDate = new Date(override.end_date).toISOString() === new Date(end_date).toISOString();

            console.log(`Checking override ${override.id}: rule=${matchesRule}, name=${matchesName}, start=${matchesStartDate}, end=${matchesEndDate}`);
            return matchesRule && matchesName && matchesStartDate && matchesEndDate;
          });

          console.log(`Found existing override:`, existingOverride ? existingOverride.id : 'none');

          if (existingOverride) {
            // Check if there are any changes
            const currentAmount = existingOverride.amount;
            const newAmount = rule.weekday_prices.mon;
            const currentWeekdayPrices = existingOverride.metadata?.weekday_prices || {};
            const hasWeekdayPriceChanges = Object.keys(rule.weekday_prices).some(
              day => rule.weekday_prices[day] !== currentWeekdayPrices[day]
            );

            if (currentAmount !== newAmount || hasWeekdayPriceChanges) {
              console.log(`Updating existing seasonal override for base price rule ${basePriceRule.id}`);

              try {
                // Update the existing override using the SeasonalPriceRule entity
                // Increase the priority to ensure this updated rule takes precedence
                // Use a simpler priority scheme - current date in YYYYMMDD format
                const now = new Date();
                const priorityValue = parseInt(
                  `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`
                );
                const existingMetadata = existingOverride.metadata || {};

                const updatedOverrides = await hotelPricingService.updateSeasonalPriceRules([{
                  id: existingOverride.id,
                  amount: rule.weekday_prices.mon,
                  priority: priorityValue, // Update priority using date-based value
                  metadata: {
                    ...existingMetadata,
                    weekday_prices: rule.weekday_prices,
                    updated_at: new Date().toISOString(),
                    is_active: true // Ensure it's marked as active
                  }
                }]);

                savedOverride = updatedOverrides[0];

                // Also handle any overlapping rules by setting them to inactive
                const overlappingRulesForBasePriceRule = overlappingRules[basePriceRule.id] || [];

                if (overlappingRulesForBasePriceRule.length > 0) {
                  console.log(`Setting ${overlappingRulesForBasePriceRule.length} overlapping rules to inactive for base price rule ${basePriceRule.id}`);

                  // Update each overlapping rule to be inactive
                  for (const overlappingRule of overlappingRulesForBasePriceRule) {
                    try {
                      // Skip the rule we just updated
                      if (overlappingRule.id === existingOverride.id) continue;

                      // Keep the original metadata but update the is_active flag
                      const updatedMetadata = {
                        ...(overlappingRule.metadata || {}),
                        is_active: false,
                        deactivated_at: new Date().toISOString(),
                        deactivated_by: savedOverride.id // Reference to the rule that replaced this one
                      };

                      // Update the rule to be inactive with a lower priority
                      // Set priority to a lower value (1) for inactive rules
                      await hotelPricingService.updateSeasonalPriceRules([{
                        id: overlappingRule.id,
                        priority: 1, // Set to a low priority value for inactive rules
                        metadata: updatedMetadata
                      }]);

                      console.log(`Set rule ${overlappingRule.id} to inactive`);
                    } catch (error) {
                      console.error(`Error updating overlapping rule ${overlappingRule.id}:`, error);
                    }
                  }
                }
              } catch (error) {
                console.error(`Error updating seasonal override: ${error}`);
                // Fall back to using the existing override
                savedOverride = existingOverride;
              }
            } else {
              console.log(`No changes for seasonal override ${existingOverride.id}, skipping update`);
              savedOverride = existingOverride;
            }
          } else {
            console.log(`Creating new seasonal override for base price rule ${basePriceRule.id}`);

            // Set a higher priority for the new rule
            // Use a simpler priority scheme - current date in YYYYMMDD format
            // This ensures newer rules have higher priority without using the full timestamp
            const now = new Date();
            const priorityValue = parseInt(
              `${now.getFullYear()}${String(now.getMonth() + 1).padStart(2, '0')}${String(now.getDate()).padStart(2, '0')}`
            );

            // Create a new seasonal override with priority
            const savedOverrides = await hotelPricingService.createSeasonalPriceRules([{
              base_price_rule_id: basePriceRule.id,
              start_date: new Date(start_date),
              end_date: new Date(end_date),
              amount: rule.weekday_prices.mon, // Use Monday's price as the base amount
              currency_code,
              description: name,
              priority: priorityValue, // Use date-based priority to ensure newer rules have higher priority
              metadata: {
                weekday_prices: rule.weekday_prices,
                created_at: new Date().toISOString(),
                is_active: true
              },
            }]);

            // Get the first saved override
            savedOverride = savedOverrides[0];

            // Now handle any overlapping rules by setting them to inactive
            const overlappingRulesForBasePriceRule = overlappingRules[basePriceRule.id] || [];

            if (overlappingRulesForBasePriceRule.length > 0) {
              console.log(`Setting ${overlappingRulesForBasePriceRule.length} overlapping rules to inactive for base price rule ${basePriceRule.id}`);

              // Update each overlapping rule to be inactive
              for (const overlappingRule of overlappingRulesForBasePriceRule) {
                try {
                  // Keep the original metadata but update the is_active flag
                  const updatedMetadata = {
                    ...(overlappingRule.metadata || {}),
                    is_active: false,
                    deactivated_at: new Date().toISOString(),
                    deactivated_by: savedOverride.id // Reference to the rule that replaced this one
                  };

                  // Update the rule to be inactive with a lower priority
                  // Set priority to a lower value (1) for inactive rules
                  await hotelPricingService.updateSeasonalPriceRules([{
                    id: overlappingRule.id,
                    priority: 1, // Set to a low priority value for inactive rules
                    metadata: updatedMetadata
                  }]);

                  console.log(`Set rule ${overlappingRule.id} to inactive`);
                } catch (error) {
                  console.error(`Error updating overlapping rule ${overlappingRule.id}:`, error);
                }
              }
            }
          }

          createdRules.push({
            id: savedOverride.id,
            base_price_rule_id: basePriceRule.id,
            occupancy_type_id: rule.occupancy_type_id,
            meal_plan_id: rule.meal_plan_id,
            start_date,
            end_date,
            name,
            amount: rule.weekday_prices.mon,
            currency_code,
            weekday_prices: rule.weekday_prices,
          });
        } catch (error) {
          console.error(`Error creating seasonal override for occupancy type ${rule.occupancy_type_id} and meal plan ${rule.meal_plan_id}:`, error);
          // Continue with other rules
        }
      }
    } catch (error) {
      console.error("Error in bulk create operation:", error);
      return res.status(500).json({
        message: "An error occurred while bulk creating seasonal pricing rules",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Return the created rules
    res.status(201).json({
      seasonal_rules: createdRules,
    });
  } catch (error) {
    console.error("Error processing request:", error);
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to create seasonal pricing rules",
    });
  }
};
