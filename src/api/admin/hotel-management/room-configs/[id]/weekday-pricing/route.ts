import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";
import { Modules } from "@camped-ai/framework/utils";
import { CurrencyValidationService } from "../../../../../../modules/hotel-management/hotel-pricing/services/currency-validation";

// Validation schema for weekday pricing
export const PostAdminWeekdayPricing = z.object({
  occupancy_type_id: z.string(),
  meal_plan_id: z.string().optional(),
  weekday_prices: z.object({
    mon: z.number().min(0, "Price must be non-negative"),
    tue: z.number().min(0, "Price must be non-negative"),
    wed: z.number().min(0, "Price must be non-negative"),
    thu: z.number().min(0, "Price must be non-negative"),
    fri: z.number().min(0, "Price must be non-negative"),
    sat: z.number().min(0, "Price must be non-negative"),
    sun: z.number().min(0, "Price must be non-negative"),
  }),
  currency_code: z.string().optional().refine(
    (code) => !code || CurrencyValidationService.isValidCurrencyFormat(code),
    { message: "Currency code must be a valid 3-letter ISO 4217 code" }
  ),
});

export type PostAdminWeekdayPricingType = z.infer<typeof PostAdminWeekdayPricing>;

/**
 * GET /admin/hotel-management/room-configs/:id/weekday-pricing
 *
 * Get weekday pricing rules for a room configuration
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;

    if (!roomConfigId) {
      return res.status(400).json({ message: "Room configuration ID is required" });
    }

    console.log(`Fetching weekday pricing for room config: ${roomConfigId}`);

   // Get the hotel pricing service for occupancy and meal plan details
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    // Get all base price rules for this room configuration
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
    });

    // Format the response to include weekday_prices
    const formattedRules = basePriceRules.map(rule => ({
      id: rule.id,
      occupancy_type_id: rule.occupancy_type_id,
      meal_plan_id: rule.meal_plan_id,
      amount:rule.amount,
      room_config_id: rule.room_config_id,
      weekday_prices: {
        mon: rule.monday_price || rule.amount,
        tue: rule.tuesday_price || rule.amount,
        wed: rule.wednesday_price || rule.amount,
        thu: rule.thursday_price || rule.amount,
        fri: rule.friday_price || rule.amount,
        sat: rule.saturday_price || rule.amount,
        sun: rule.sunday_price || rule.amount,
      },
      currency_code: rule.currency_code,
      created_at: rule.created_at,
      updated_at: rule.updated_at,
    }));

   

    // Get occupancy types and meal plans for each rule
    const rulesWithDetails = await Promise.all(formattedRules.map(async (rule) => {
      try {
        // Get the occupancy type
        const occupancyConfig = await hotelPricingService.retrieveOccupancyConfig(rule.occupancy_type_id);

        // Get the meal plan if it exists
        let mealPlan = null;
        if (rule.meal_plan_id) {
          mealPlan = await hotelPricingService.retrieveMealPlan(rule.meal_plan_id);
        }

        return {
          ...rule,
          occupancy_type: occupancyConfig,
          meal_plan: mealPlan,
        };
      } catch (error) {
        console.error(`Error fetching details for rule ${rule.id}:`, error);
        return rule;
      }
    }));

    res.json({
      weekday_rules: rulesWithDetails,
    });
  } catch (error) {
    console.error("Error fetching weekday pricing:", error);
    res.status(500).json({
      message: "An error occurred while fetching weekday pricing",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};


