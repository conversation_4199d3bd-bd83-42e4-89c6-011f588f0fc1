import {
  AuthenticatedMedusaRequest,
  MedusaRequest,
  MedusaResponse,
} from "@camped-ai/framework/http";
import { z } from "zod";
import {
  PostAdminCreateDestination,
  PostAdminDeleteDestination,
  PostAdminUpdateDestination,
} from "./validators";
import { CreateDestinationWorkflow } from "src/workflows/hotel-management/destination/create-destination";
import { UpdateDestinationWorkflow } from "src/workflows/hotel-management/destination/update-destination";
import { DeleteDestinationWorkflow } from "src/workflows/hotel-management/destination/delete-destination";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

type PostAdminCreateDestinationType = z.infer<typeof PostAdminCreateDestination>;
type PostAdminDeleteDestinationType = z.infer<typeof PostAdminDeleteDestination>;
type PostAdminUpdateDestinationType = z.infer<typeof PostAdminUpdateDestination>;

export const GET = async (
  req: AuthenticatedMedusaRequest,
  res: MedusaResponse
) => {
  const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
  const { limit = 20, offset = 0, is_featured, is_active } = req.query || {};
  const filters: Record<string, any> = {};

  if (is_featured !== undefined) {
    filters.is_featured = is_featured;
  }
  if (is_active !== undefined) {
    filters.is_active = is_active === "true";
  }
  const {
    data: destinations,
    metadata: { count, take, skip },
  } = await query.graph({
    entity: "destination",
    fields: ["*"],
    filters,
    pagination: {
      skip: Number(offset),
      take: Number(limit),
    },
  });
  res.json({
    destinations,
    count,
    limit: take,
    offset: skip,
  });
};

export const POST = async (
  req: MedusaRequest<PostAdminCreateDestinationType>,
  res: MedusaResponse
) => {
  const { result } = await CreateDestinationWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });
  res.json({ destination: result });
};

export const PUT = async (
  req: MedusaRequest<PostAdminUpdateDestinationType>,
  res: MedusaResponse
) => {
  const { result } = await UpdateDestinationWorkflow(req.scope).run({
    //@ts-ignore
    input: req.body,
  });
  res.json({ destination: result });
};

export const DELETE = async (
  req: MedusaRequest<PostAdminDeleteDestinationType>,
  res: MedusaResponse
) => {
  const { ids } = req.body;
  const { result } = await DeleteDestinationWorkflow(req.scope).run({
    input: { ids: ids.split(",") },
  });
  res.json({ ids: result });
};
