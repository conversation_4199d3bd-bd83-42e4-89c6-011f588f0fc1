import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination"

export async function GET(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const destinationId = req.params.id

  try {
    const destinationService = req.scope.resolve(DESTINATION_MODULE)
    const images = await destinationService.getDestinationImages(destinationId)

    res.status(200).json({
      images: images.map(image => ({
        id: image.id,
        url: image.url,
        isThumbnail: image.isThumbnail
      }))
    })
  } catch (error) {
    console.error("Error fetching destination images:", error)
    res.status(500).json({
      message: "Failed to fetch destination images",
      error: error instanceof Error ? error.message : "Unknown error"
    })
  }
}