import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http"
import { DESTINATION_MODULE } from "src/modules/hotel-management/destination"

export async function POST(
  req: MedusaRequest,
  res: MedusaResponse
) {
  const destinationId = req.params.id
  //@ts-ignore
  const { image_id } = req.body

  // Validate that image_id is provided
  if (!image_id || image_id.trim() === "") {
    return res.status(400).json({
      type: "invalid_data",
      message: "image_id is required and cannot be empty"
    });
  }

  const destinationService = req.scope.resolve(DESTINATION_MODULE)
  const thumbnailImage = await destinationService.setDestinationThumbnail(destinationId, image_id)

  res.status(200).json({ thumbnail: thumbnailImage })
}