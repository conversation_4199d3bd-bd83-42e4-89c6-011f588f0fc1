import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "src/modules/notification-template/service";
import NotificationTemplateService from "src/modules/notification-template/service";
import { HOTEL_PRICING_MODULE } from "src/modules/hotel-management/hotel-pricing";
import HotelPricingService from "src/modules/hotel-management/hotel-pricing/service";
import * as pdf from "html-pdf";

// Helper function to format currency
function currencyFormatter(
  amount: number,
  currencyCode: string = "USD"
): string {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: currencyCode.toUpperCase(),
  }).format(amount / 100); // Assuming amounts are in cents
}

// Helper function to format dates
function dateFormatter(date: string | Date): string {
  return new Intl.DateTimeFormat("en-US", {
    year: "numeric",
    month: "long",
    day: "numeric",
  }).format(new Date(date));
}

// Enhanced function to replace placeholders in template content
function replacePlaceholders(
  content: string,
  dataContext: Record<string, any>
): string {
  let processedContent = content;

  // First, normalize the template by removing line breaks within placeholders
  const normalizePlaceholder = (placeholder: string): string => {
    return placeholder
      .replace(/\s*\n\s*/g, " ")
      .replace(/\s+/g, " ")
      .trim();
  };

  // Find all double-brace placeholders and normalize them
  let normalizedTemplate = processedContent.replace(
    /{{\s*[^{}]*?}}/g,
    (match) => normalizePlaceholder(match)
  );

  // Find all triple-brace placeholders and normalize them
  normalizedTemplate = normalizedTemplate.replace(
    /{{{\s*[^{}]*?}}}/g,
    (match) => normalizePlaceholder(match)
  );

  processedContent = normalizedTemplate;

  // Handle default values with the pipe syntax
  const defaultValueRegex =
    /{{\s*([a-zA-Z0-9_.]+)\s*\|\s*default:\s*(?:['"]([^'"]*)['"]|''([^']*)'')\s*}}/g;
  processedContent = processedContent.replace(
    defaultValueRegex,
    (_, path, defaultValue1, defaultValue2) => {
      const defaultValue = defaultValue1 || defaultValue2 || "";
      const parts = path.trim().split(".");

      let value = dataContext;
      for (const part of parts) {
        value = value?.[part];
        if (value === undefined || value === null) {
          return defaultValue;
        }
      }

      return value.toString();
    }
  );

  // Replace placeholders recursively
  function replaceInObject(obj: any, prefix: string = ""): void {
    for (const [key, value] of Object.entries(obj)) {
      const placeholder = prefix ? `{{${prefix}.${key}}}` : `{{${key}}}`;

      if (
        typeof value === "object" &&
        value !== null &&
        !Array.isArray(value)
      ) {
        // Recursively handle nested objects
        replaceInObject(value, prefix ? `${prefix}.${key}` : key);
      } else {
        // Replace the placeholder with the actual value
        const regex = new RegExp(
          placeholder.replace(/[.*+?^${}()|[\]\\]/g, "\\$&"),
          "g"
        );
        processedContent = processedContent.replace(regex, String(value || ""));
      }
    }
  }

  replaceInObject(dataContext);

  // Handle items_html_block specifically
  if (dataContext.items_html_block) {
    processedContent = processedContent.replace(
      /{{{\s*items_html_block\s*}}}/g,
      dataContext.items_html_block
    );
  }

  return processedContent;
}

/**
 * Download invoice for a booking (Admin API)
 * Admins can download invoices for any booking
 */
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  const { id: bookingId } = req.params;
  const INVOICE_TEMPLATE_EVENT_NAME = "booking.invoice.default";

  if (!bookingId) {
    return res.status(400).json({ message: "Booking ID is required." });
  }

  try {
    const orderService: any = req.scope.resolve(Modules.ORDER);
    const notificationTemplateService: NotificationTemplateService =
      req.scope.resolve(NOTIFICATION_TEMPLATE_SERVICE);

    // 1. Fetch the Order
    const order = await orderService.retrieveOrder(bookingId, {
      relations: ["billing_address", "items"],
    });

    if (!order) {
      return res.status(404).json({ message: "Booking not found." });
    }

    // 2. Get the invoice template
    let invoiceTemplate = null;
    try {
      const filters = {
        event_name: INVOICE_TEMPLATE_EVENT_NAME,
        is_active: true,
        channel: "pdf",
      };
      const config = { take: 1 };

      const templates =
        await notificationTemplateService.listNotificationTemplates(
          filters,
          config
        );

      if (templates && templates.length > 0) {
        invoiceTemplate = templates[0];
      } else {
        console.warn(
          `No active PDF template found for event: ${INVOICE_TEMPLATE_EVENT_NAME}`
        );
      }
    } catch (error) {
      console.error("Error fetching invoice template:", error);
    }

    // 3. Use fallback template if no template found
    if (!invoiceTemplate) {
      invoiceTemplate = {
        content: `
          <!DOCTYPE html>
          <html>
          <head>
            <title>Invoice</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 40px; }
              .header { text-align: center; margin-bottom: 30px; }
              .invoice-details { margin-bottom: 30px; }
              .customer-details { margin-bottom: 30px; }
              .booking-details { margin-bottom: 30px; }
              .items-table { width: 100%; border-collapse: collapse; margin-bottom: 30px; }
              .items-table th, .items-table td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              .items-table th { background-color: #f2f2f2; }
              .total-section { text-align: right; }
              .total-line { margin: 5px 0; }
              .total-amount { font-weight: bold; font-size: 1.2em; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>INVOICE</h1>
            </div>

            <div class="invoice-details">
              <p><strong>Invoice #:</strong> {{order.display_id}}</p>
              <p><strong>Date:</strong> {{order.created_at_formatted}}</p>
              <p><strong>Status:</strong> {{order.status}}</p>
            </div>

            <div class="customer-details">
              <h3>Bill To:</h3>
              <p>{{customer.first_name}} {{customer.last_name}}</p>
              <p>{{customer.email}}</p>
            </div>

            <div class="booking-details">
              <h3>Booking Details:</h3>
              <p><strong>Hotel:</strong> {{booking.hotel_name}}</p>
              <p><strong>Room Type:</strong> {{booking.room_config_name}}</p>
              <p><strong>Check-in:</strong> {{booking.check_in_date}}</p>
              <p><strong>Check-out:</strong> {{booking.check_out_date}}</p>
              <p><strong>Guests:</strong> {{booking.number_of_guests}}</p>
            </div>

            <table class="items-table">
              <thead>
                <tr>
                  <th>Description</th>
                  <th>Quantity</th>
                  <th>Unit Price</th>
                  <th>Total</th>
                </tr>
              </thead>
              <tbody>
                {{items_html_block}}
              </tbody>
            </table>

            <div class="total-section">
              <div class="total-line">Room Charges: {{booking.room_total_amount}}</div>
              {{#if booking.add_on_total_amount}}
              <div class="total-line">Add-on Services: {{booking.add_on_total_amount}}</div>
              {{/if}}
              <div class="total-line">Tax: {{order.tax_total_formatted}}</div>
              <div class="total-line total-amount">Total: {{booking.total_amount}}</div>
            </div>
          </body>
          </html>
        `,
      };
    }

    // 4. Fetch hotel details for rules and cancellation policy
    const metadata = order.metadata || {};
    let hotelDetails = null;
    let cancellationPolicyText = "";
    let propertyRulesText = "";

    if (metadata.hotel_id) {
      try {
        const query = req.scope.resolve("query");

        // Fetch hotel details
        const { data: hotels } = await query.graph({
          entity: "hotel",
          filters: { id: metadata.hotel_id },
          fields: [
            "id",
            "name",
            "address",
            "location",
            "phone_number",
            "email",
            "rules",
            "destination_id",
          ],
        });

        if (hotels && hotels.length > 0) {
          hotelDetails = hotels[0];
          console.log("hotelDetails", hotelDetails);

          // Format property rules into bullet list
          if (hotelDetails.rules && Array.isArray(hotelDetails.rules)) {
            propertyRulesText =
              "<ul>" +
              hotelDetails.rules
                .map((rule: string) => `<li>${rule}</li>`)
                .join("") +
              "</ul>";
          }

          // Fetch destination name if destination_id exists
          if (hotelDetails.destination_id) {
            try {
              const { data: destinations } = await query.graph({
                entity: "destination",
                filters: { id: hotelDetails.destination_id },
                fields: ["id", "name"],
              });

              if (destinations && destinations.length > 0) {
                hotelDetails.destination_name = destinations[0].name;
              }
            } catch (error) {
              console.error("Error fetching destination details:", error);
            }
          }
        }

        // Fetch cancellation policies
        const { data: cancellationPolicies } = await query.graph({
          entity: "cancellation_policy",
          filters: {
            hotel_id: metadata.hotel_id,
            is_active: true,
          },
          fields: [
            "name",
            "description",
            "days_before_checkin",
            "refund_type",
            "refund_amount",
          ],
        });

        if (cancellationPolicies && cancellationPolicies.length > 0) {
          // Format cancellation policies into bullet list
          cancellationPolicyText =
            "<ul>" +
            cancellationPolicies
              .map((policy) => {
                let policyText = policy.description || policy.name;
                if (policy.days_before_checkin > 0) {
                  policyText += ` (${policy.days_before_checkin} days before check-in)`;
                }
                if (
                  policy.refund_type === "PERCENTAGE" &&
                  policy.refund_amount > 0
                ) {
                  policyText += ` - ${policy.refund_amount}% refund`;
                } else if (policy.refund_type === "NO_REFUND") {
                  policyText += ` - No refund`;
                }
                return `<li>${policyText}</li>`;
              })
              .join("") +
            "</ul>";
        }
      } catch (error) {
        console.error("Error fetching hotel details:", error);
      }
    }

    // 4.5. Fetch meal plan label for board basis
    let boardBasisLabel = "Room Only"; // Default fallback

    if (metadata.hotel_id && (order.meal_plan || metadata.meal_plan)) {
      try {
        const hotelPricingService: HotelPricingService =
          req.scope.resolve(HOTEL_PRICING_MODULE);
        const mealPlanType = order.meal_plan || metadata.meal_plan;

        console.log(
          `Fetching meal plan label for hotel ${metadata.hotel_id} and type ${mealPlanType}`
        );

        // Get meal plans for this hotel with the specific type
        const mealPlans = await hotelPricingService.listMealPlans({
          hotel_id: metadata.hotel_id,
          type: mealPlanType,
        });

        if (mealPlans && mealPlans.length > 0) {
          boardBasisLabel = mealPlans[0].name;
          console.log(`Found meal plan label: ${boardBasisLabel}`);
        } else {
          console.log(
            `No meal plan found for type ${mealPlanType}, using default: ${boardBasisLabel}`
          );
        }
      } catch (error) {
        console.error("Error fetching meal plan label:", error);
        // Keep the default fallback value
      }
    }

    // 5. Create data context for placeholder replacement

    // Extract add-ons from metadata if they exist
    const addOns = metadata.add_ons || [];

    // Calculate add-ons total amount (convert from cents)
    const addOnTotalAmount = (metadata.add_on_total_amount || 0) / 100;

    // Calculate room-only total (total amount minus add-ons)
    const totalAmount = (metadata.total_amount || 0) + addOnTotalAmount;
    const roomOnlyAmount = totalAmount - addOnTotalAmount;

    // Helper function to get guest name by type and index
    function getGuestNameByIndex(
      guestType: string,
      guestIndex: number,
      travelers: any
    ): string {
      if (!travelers) {
        return "Guest";
      }

      // Map guest types to correct plural forms
      const guestTypeMap: { [key: string]: string } = {
        adult: "adults",
        child: "children", // Fix: "child" should map to "children", not "childs"
        infant: "infants",
      };

      const guestListKey = guestTypeMap[guestType] || guestType + "s";
      const guestList = travelers[guestListKey];

      if (!guestList || !Array.isArray(guestList)) {
        return "Guest";
      }

      if (guestIndex >= 0 && guestIndex < guestList.length) {
        const guest = guestList[guestIndex];
        return guest.name || "Guest";
      }

      return "Guest";
    }

    // Create detailed add-ons list for display
    const addOnsDetailed =
      addOns.length > 0
        ? addOns
            .map((addOn: any) => {
              let addOnDisplay = `<div class="add-on-item"><strong>${
                addOn.name || "Service"
              }</strong>`;

              // Handle different pricing types
              if (
                addOn.pricing_type === "usage_based" &&
                addOn.guest_usage &&
                addOn.guest_usage.length > 0
              ) {
                // Usage-based pricing: show guest name and service dates
                addOn.guest_usage.forEach((usage: any) => {
                  const guestName = getGuestNameByIndex(
                    usage.guest_type,
                    usage.guest_index,
                    metadata.travelers
                  );
                  const dates = usage.usage_dates.join(", ");
                  addOnDisplay += `<br>&nbsp;&nbsp;${guestName}<br>&nbsp;&nbsp;&nbsp;&nbsp;<span class="add-on-dates">Dates: ${dates}</span>`;
                });
              } else if (addOn.pricing_type === "per_person") {
                // Per-person pricing: show guest names based on quantities
                const guests = [];

                // Add adults
                if (addOn.adult_quantity > 0) {
                  for (let i = 0; i < addOn.adult_quantity; i++) {
                    const guestName = getGuestNameByIndex(
                      "adult",
                      i,
                      metadata.travelers
                    );
                    guests.push(guestName);
                  }
                }

                // Add children
                if (addOn.child_quantity > 0) {
                  for (let i = 0; i < addOn.child_quantity; i++) {
                    const guestName = getGuestNameByIndex(
                      "child",
                      i,
                      metadata.travelers
                    );
                    guests.push(guestName);
                  }
                }

                guests.forEach((guestName) => {
                  addOnDisplay += `<br>&nbsp;&nbsp;${guestName}`;
                });
              } else if (addOn.pricing_type === "package") {
                // Package pricing: just show the service name
                addOnDisplay += `<br>&nbsp;&nbsp;Package service`;
              }

              addOnDisplay += "</div>";
              return addOnDisplay;
            })
            .join("")
        : "No add-on services selected";

    // Create add-ons pricing breakdown with hierarchical structure
    const addOnsPricing =
      addOns.length > 0
        ? addOns
            .map((addOn: any) => {
              let rows = "";

              if (addOn.pricing_type === "per_person") {
                // Main add-on row
                rows += `<tr class="addon-main has-subitems">
                  <td>${addOn.name || "Service"}</td>
                  <td></td>
                  <td></td>
                  <td>${currencyFormatter(
                    (addOn.total_price || 0) * 100,
                    order.currency_code
                  )}</td>
                </tr>`;

                // Adult sub-rows
                if (addOn.adult_quantity > 0) {
                  const isLastSubItem = addOn.child_quantity === 0; // Last if no children
                  rows += `<tr class="addon-sub${
                    isLastSubItem ? " addon-sub-last" : ""
                  }">
                    <td>Adult</td>
                    <td>${addOn.adult_quantity}</td>
                    <td>${currencyFormatter(
                      (addOn.adult_price || 0) * 100,
                      order.currency_code
                    )}</td>
                    <td>${currencyFormatter(
                      (addOn.adult_price || 0) * addOn.adult_quantity * 100,
                      order.currency_code
                    )}</td>
                  </tr>`;
                }

                // Child sub-rows
                if (addOn.child_quantity > 0) {
                  rows += `<tr class="addon-sub addon-sub-last">
                    <td>Child</td>
                    <td>${addOn.child_quantity}</td>
                    <td>${currencyFormatter(
                      (addOn.child_price || 0) * 100,
                      order.currency_code
                    )}</td>
                    <td>${currencyFormatter(
                      (addOn.child_price || 0) * addOn.child_quantity * 100,
                      order.currency_code
                    )}</td>
                  </tr>`;
                }
              } else if (addOn.pricing_type === "usage_based") {
                // Main add-on row
                rows += `<tr class="addon-main has-subitems">
                  <td>${addOn.name || "Service"}</td>
                  <td></td>
                  <td></td>
                  <td>${currencyFormatter(
                    (addOn.total_price || 0) * 100,
                    order.currency_code
                  )}</td>
                </tr>`;

                // Calculate days from guest_usage
                if (addOn.guest_usage && addOn.guest_usage.length > 0) {
                  const adultUsage = addOn.guest_usage.filter(
                    (usage: any) => usage.guest_type === "adult"
                  );
                  const childUsage = addOn.guest_usage.filter(
                    (usage: any) => usage.guest_type === "child"
                  );

                  if (adultUsage.length > 0) {
                    const totalAdultDays = adultUsage.reduce(
                      (sum: number, usage: any) =>
                        sum +
                        (usage.usage_dates ? usage.usage_dates.length : 0),
                      0
                    );

                    // Create detailed breakdown with guest names and days
                    const adultBreakdown = adultUsage
                      .map((usage: any) => {
                        const guestName = getGuestNameByIndex(
                          usage.guest_type,
                          usage.guest_index,
                          metadata.travelers
                        );
                        const days = usage.usage_dates
                          ? usage.usage_dates.length
                          : 0;
                        const dayText = days === 1 ? "day" : "days";
                        return `${days} ${dayText} × ${guestName}`;
                      })
                      .join(" + ");

                    // Calculate total quantity as people × days
                    const totalAdultQuantity =
                      adultUsage.length * (totalAdultDays / adultUsage.length);
                    const isLastSubItem = childUsage.length === 0; // Last if no children
                    rows += `<tr class="addon-sub${
                      isLastSubItem ? " addon-sub-last" : ""
                    }">
                      <td>Adult<br><span style="font-size: 0.85em; color: #888;">${adultBreakdown}</span></td>
                      <td>${totalAdultQuantity}</td>
                      <td>${currencyFormatter(
                        (addOn.per_day_adult_price || 0) * 100,
                        order.currency_code
                      )}</td>
                      <td>${currencyFormatter(
                        (addOn.per_day_adult_price || 0) *
                          totalAdultQuantity *
                          100,
                        order.currency_code
                      )}</td>
                    </tr>`;
                  }

                  if (childUsage.length > 0) {
                    const totalChildDays = childUsage.reduce(
                      (sum: number, usage: any) =>
                        sum +
                        (usage.usage_dates ? usage.usage_dates.length : 0),
                      0
                    );

                    // Create detailed breakdown with guest names and days
                    const childBreakdown = childUsage
                      .map((usage: any) => {
                        const guestName = getGuestNameByIndex(
                          usage.guest_type,
                          usage.guest_index,
                          metadata.travelers
                        );
                        const days = usage.usage_dates
                          ? usage.usage_dates.length
                          : 0;
                        const dayText = days === 1 ? "day" : "days";
                        return `${days} ${dayText} × ${guestName}`;
                      })
                      .join(" + ");

                    // Calculate total quantity as people × days
                    const totalChildQuantity =
                      childUsage.length * (totalChildDays / childUsage.length);
                    rows += `<tr class="addon-sub addon-sub-last">
                      <td>Child<br><span style="font-size: 0.85em; color: #888;">${childBreakdown}</span></td>
                      <td>${totalChildQuantity}</td>
                      <td>${currencyFormatter(
                        (addOn.per_day_child_price || 0) * 100,
                        order.currency_code
                      )}</td>
                      <td>${currencyFormatter(
                        (addOn.per_day_child_price || 0) *
                          totalChildQuantity *
                          100,
                        order.currency_code
                      )}</td>
                    </tr>`;
                  }
                }
              } else if (addOn.pricing_type === "package") {
                // Package pricing - single row
                rows += `<tr class="addon-main">
                  <td>${addOn.name || "Service"}</td>
                  <td>1</td>
                  <td>${currencyFormatter(
                    (addOn.total_price || 0) * 100,
                    order.currency_code
                  )}</td>
                  <td>${currencyFormatter(
                    (addOn.total_price || 0) * 100,
                    order.currency_code
                  )}</td>
                </tr>`;
              } else {
                // Fallback for unknown pricing types
                rows += `<tr class="addon-main">
                  <td>${addOn.name || "Service"}</td>
                  <td>1</td>
                  <td>${currencyFormatter(
                    (addOn.total_price || 0) * 100,
                    order.currency_code
                  )}</td>
                  <td>${currencyFormatter(
                    (addOn.total_price || 0) * 100,
                    order.currency_code
                  )}</td>
                </tr>`;
              }

              return rows;
            })
            .join("")
        : "";

    // Create travelers list for display
    const travelers = metadata.travelers || {};
    let travelersList = "";

    if (travelers.adults && travelers.adults.length > 0) {
      travelersList += `<div class="guest-item"><strong>Adults:</strong>`;
      travelers.adults.forEach((adult: any) => {
        travelersList += `<br>&nbsp;&nbsp;${adult.name}`;
      });
      travelersList += `</div>`;
    }

    if (travelers.children && travelers.children.length > 0) {
      travelersList += `<div class="guest-item"><strong>Children:</strong>`;
      travelers.children.forEach((child: any) => {
        travelersList += `<br>&nbsp;&nbsp;${child.name} (Age: ${
          child.age || ""
        })`;
      });
      travelersList += `</div>`;
    }

    if (travelers.infants && travelers.infants.length > 0) {
      travelersList += `<div class="guest-item"><strong>Infants:</strong>`;
      travelers.infants.forEach((infant: any) => {
        travelersList += `<br>&nbsp;&nbsp;${infant.name} (Age: ${
          infant.age || ""
        })`;
      });
      travelersList += `</div>`;
    }

    // If no travelers list, create a fallback with primary guest
    if (!travelersList) {
      const firstName =
        order.customer?.first_name || metadata.guest_name?.split(" ")[0] || "";
      const lastName =
        order.customer?.last_name ||
        metadata.guest_name?.split(" ").slice(1).join(" ") ||
        "";
      travelersList = `<div class="guest-item"><strong>Primary Guest:</strong> ${firstName} ${lastName}</div>`;
    }

    // Create special requests section conditionally
    const hasSpecialRequests = !!(
      metadata.special_requests &&
      metadata.special_requests.trim() &&
      metadata.special_requests.trim() !== "None"
    );

    const specialRequestsSection = hasSpecialRequests
      ? `<div class="section">
          <h3>
            <svg class="section-icon" viewBox="0 0 24 24">
              <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-3 15l-3-3 1.41-1.41L11 14.17l4.59-4.58L17 11l-6 6z"/>
            </svg>
            Special Requests
          </h3>
          <div class="section-item">
            <span class="section-value">${metadata.special_requests}</span>
          </div>
        </div>`
      : "";

    // Calculate number of nights
    const checkInDate = new Date(metadata.check_in_date);
    const checkOutDate = new Date(metadata.check_out_date);
    const numberOfNights = Math.ceil(
      (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    const dataContext: Record<string, any> = {
      order: {
        id: order.id,
        display_id: order.display_id || order.id,
        created_at_formatted: dateFormatter(order.created_at),
        subtotal_formatted: currencyFormatter(
          order.subtotal || 0,
          order.currency_code
        ),
        shipping_total_formatted: currencyFormatter(
          order.shipping_total || 0,
          order.currency_code
        ),
        tax_total_formatted: currencyFormatter(
          order.tax_total || 0,
          order.currency_code
        ),
        total_formatted: currencyFormatter(
          order.total || 0,
          order.currency_code
        ),
        currency_code: order.currency_code,
        status: order.status,
        notes: order.notes || "",
        payment_method_name: order.payment_method_name || "N/A",
      },
      customer: {
        email: order.customer?.email || order.email || "N/A",
        guest_phone: metadata.guest_phone || "N/A",
        first_name:
          order.customer?.first_name ||
          metadata.guest_name?.split(" ")[0] ||
          "",
        last_name:
          order.customer?.last_name ||
          metadata.guest_name?.split(" ").slice(1).join(" ") ||
          "",
      },
      booking: {
        hotel_name: metadata.hotel_name || hotelDetails?.name || "Hotel",
        destination_name:
          hotelDetails?.destination_name ||
          metadata.destination_name ||
          "Destination",
        room_config_name:
          metadata.room_config_name || metadata.room_type || "Standard Room",
        check_in_date: metadata.check_in_date
          ? dateFormatter(metadata.check_in_date)
          : "N/A",
        check_out_date: metadata.check_out_date
          ? dateFormatter(metadata.check_out_date)
          : "N/A",
        check_in_time: metadata.check_in_time || "12:00",
        check_out_time: metadata.check_out_time || "12:00",
        number_of_guests: metadata.number_of_guests || 1,
        number_of_nights: numberOfNights,
        special_requests: metadata.special_requests || "",
        has_special_requests: !!(
          metadata.special_requests &&
          metadata.special_requests.trim() &&
          metadata.special_requests.trim() !== "None"
        ),
        board_basis: boardBasisLabel,
        hotel_address:
          hotelDetails?.location ||
          metadata.hotel_address ||
          "Address not available",
        hotel_phone:
          hotelDetails?.phone_number ||
          metadata.hotel_phone ||
          "Contact not available",
        hotel_email:
          hotelDetails?.email || metadata.hotel_email || "Email not available",
        cancellation_policy:
          cancellationPolicyText || metadata.cancellation_policy || "",
        property_rules: propertyRulesText || metadata.property_rules || "",
        total_amount: currencyFormatter(
          metadata.total_amount * 100 || 0,
          order.currency_code
        ),
        room_total_amount: currencyFormatter(
          roomOnlyAmount * 100 || 0,
          order.currency_code
        ),
        total: currencyFormatter(totalAmount * 100 || 0, order.currency_code),
        add_on_total_amount:
          addOnTotalAmount > 0
            ? currencyFormatter(addOnTotalAmount * 100, order.currency_code)
            : null,
      },
      // Add-ons information
      add_ons: addOns.map((addOn: any) => ({
        name: addOn.name || "Service",
      })),
      add_ons_detailed: addOnsDetailed,
      add_ons_pricing: addOnsPricing,
      travelers_list: travelersList,
      special_requests_section: specialRequestsSection,
      items_html_block: `<tr>
        <td>${metadata.room_config_name || "Hotel Booking"}</td>
        <td style="text-align: center;">${numberOfNights}</td>
        <td style="text-align: right;">${currencyFormatter(
          (roomOnlyAmount / numberOfNights) * 100 || 0,
          order.currency_code
        )}</td>
        <td style="text-align: right;">${currencyFormatter(
          roomOnlyAmount * 100 || 0,
          order.currency_code
        )}</td>
      </tr>`,
    };

    // 6. Replace placeholders in the template content
    const processedHtmlContent = replacePlaceholders(
      invoiceTemplate.content,
      dataContext
    );

    // 7. Generate PDF
    const options: pdf.CreateOptions = {
      format: "A4",
      orientation: "portrait",
      border: { top: "0.5in", right: "0.5in", bottom: "0.5in", left: "0.5in" },
    };

    pdf.create(processedHtmlContent, options).toBuffer((err, buffer) => {
      if (err) {
        console.error("Error generating PDF with html-pdf:", err);
        return res.status(500).json({
          message: "Failed to generate PDF.",
          errorDetail: err.message,
        });
      }

      res.setHeader("Content-Type", "application/pdf");
      res.setHeader(
        "Content-Disposition",
        `attachment; filename="invoice-${order.display_id || order.id}.pdf"`
      );
      res.send(buffer);
    });
  } catch (error: any) {
    console.error("Error in GET /admin/bookings/[id]/invoice route:", error);
    let statusCode = 500;
    let message = "Failed to process invoice request.";

    if (
      error.message?.toLowerCase().includes("not found") ||
      error.name === "NotFoundError" ||
      error.type === "not_found"
    ) {
      statusCode = 404;
      message = error.message || "Booking not found.";
    }

    res.status(statusCode).json({
      message,
      errorDetail: error.message,
    });
  }
}
