import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import { UpdateRoomPricingWorkflow } from "src/workflows/hotel-management/room/update-room-pricing";
import { z } from "zod";
import { CurrencyValidationService } from "../../../../../../modules/hotel-management/hotel-pricing/services/currency-validation";

// Validation schema for updating room pricing
export const PostAdminUpdateRoomPricing = z.object({
  base_price: z.number().min(0, "Base price must be non-negative"),
  currency_code: z.string().refine(
    (code) => CurrencyValidationService.isValidCurrencyFormat(code),
    { message: "Currency code must be a valid 3-letter ISO 4217 code" }
  ),
  start_date: z.string().optional(),
  end_date: z.string().optional(),
  min_nights: z.number().min(1, "Minimum nights must be at least 1").optional(),
  max_nights: z.number().min(1, "Maximum nights must be at least 1").optional(),
  customer_group_id: z.string().optional(),
});

export type PostAdminUpdateRoomPricingType = z.infer<typeof PostAdminUpdateRoomPricing>;

// GET endpoint to retrieve room pricing
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomId = req.params.id;
    const productModuleService = req.scope.resolve(Modules.PRODUCT);
    const pricingModuleService = req.scope.resolve(Modules.PRICING);
    
    // Get the product variant (room)
    const variant = await productModuleService.retrieveProductVariant(roomId);
    
    if (!variant) {
      return res.status(404).json({ message: "Room not found" });
    }
    
    // Get base pricing
    const basePrices = await productModuleService.listMoneyAmounts({
      variant_id: roomId,
      price_list_id: null, // Base prices have no price list
    });
    
    // Get seasonal pricing (from price lists)
    const query = req.scope.resolve("query");
    const { data: priceLists } = await query.graph({
      entity: "price_list",
      fields: ["id", "name", "description", "starts_at", "ends_at", "status"],
    });
    
    // For each price list, check if it has prices for this room
    const seasonalPrices = [];
    
    for (const priceList of priceLists) {
      const { data: priceListItems } = await query.graph({
        entity: "price_list_price",
        filters: {
          price_list_id: priceList.id,
          variant_id: roomId,
        },
        fields: ["id", "amount", "currency_code", "min_quantity", "max_quantity"],
      });
      
      if (priceListItems && priceListItems.length > 0) {
        for (const item of priceListItems) {
          seasonalPrices.push({
            price_list_id: priceList.id,
            price_list_name: priceList.name,
            price_list_item_id: item.id,
            start_date: priceList.starts_at,
            end_date: priceList.ends_at,
            amount: item.amount,
            currency_code: item.currency_code,
            min_nights: item.min_quantity,
            max_nights: item.max_quantity,
            status: priceList.status,
          });
        }
      }
    }
    
    res.json({
      room: {
        id: variant.id,
        title: variant.title,
        product_id: variant.product_id,
      },
      base_prices: basePrices,
      seasonal_prices: seasonalPrices,
    });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to retrieve room pricing",
    });
  }
};

// POST endpoint to update room pricing
export const POST = async (req: MedusaRequest<PostAdminUpdateRoomPricingType>, res: MedusaResponse) => {
  try {
    const roomId = req.params.id;

    // Get the query service for currency validation
    const query = req.scope.resolve("query");

    // Validate and normalize currency code
    const validatedCurrencyCode = await CurrencyValidationService.validateAndNormalizeCurrency(
      req.body.currency_code,
      query
    );

    // Run the workflow to update room pricing
    const { result } = await UpdateRoomPricingWorkflow(req.scope).run({
      input: {
        room_id: roomId,
        ...req.body,
        currency_code: validatedCurrencyCode,
      },
    });

    res.json({ pricing: result });
  } catch (error) {
    res.status(400).json({
      message: error instanceof Error ? error.message : "Failed to update room pricing",
    });
  }
};
