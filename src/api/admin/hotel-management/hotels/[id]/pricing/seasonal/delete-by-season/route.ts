import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for deleting seasonal pricing by season
export const PostAdminDeleteSeasonalPricingBySeason = z.object({
  name: z.string().min(1, "Season name is required"),
  start_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "Start date must be in YYYY-MM-DD format"),
  end_date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "End date must be in YYYY-MM-DD format"),
});

export type PostAdminDeleteSeasonalPricingBySeasonType = z.infer<typeof PostAdminDeleteSeasonalPricingBySeason>;

/**
 * POST /admin/hotel-management/hotels/:id/pricing/seasonal/delete-by-season
 * 
 * Delete all seasonal pricing rules for a specific season (by name and date range)
 */
export const POST = async (req: MedusaRequest<PostAdminDeleteSeasonalPricingBySeasonType>, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    if (!hotelId) {
      return res.status(400).json({ message: "Hotel ID is required" });
    }

    // Validate the request body
    const validationResult = PostAdminDeleteSeasonalPricingBySeason.safeParse(req.body);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid request data",
        errors: validationResult.error.errors,
      });
    }

    const { name, start_date, end_date } = validationResult.data;

    console.log(`Deleting seasonal pricing rules for hotel: ${hotelId}`);
    console.log(`Season: ${name}, ${start_date} to ${end_date}`);

    // Get the hotel pricing service
    const hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);

    // First, get all base price rules for this hotel
    const basePriceRules = await hotelPricingService.listBasePriceRules({
      hotel_id: hotelId,
    });

    console.log(`Found ${basePriceRules.length} base price rules for hotel ${hotelId}`);

    let deletedCount = 0;
    const deletedRuleIds = [];

    // For each base price rule, find and delete matching seasonal overrides
    for (const basePriceRule of basePriceRules) {
      try {
        // Get all seasonal overrides for this base price rule
        const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
          base_price_rule_id: basePriceRule.id,
        });

        // Filter overrides that match the season criteria
        const matchingOverrides = seasonalOverrides.filter(override => {
          // Check if the override matches the season name and date range
          const overrideStartDate = new Date(override.start_date).toISOString().split('T')[0];
          const overrideEndDate = new Date(override.end_date).toISOString().split('T')[0];
          
          // Match by name (from description field) and exact date range
          const nameMatches = override.description === name || 
                             (override.metadata && override.metadata.season_name === name);
          const dateMatches = overrideStartDate === start_date && overrideEndDate === end_date;
          
          return nameMatches && dateMatches;
        });

        console.log(`Found ${matchingOverrides.length} matching seasonal overrides for base rule ${basePriceRule.id}`);

        // Delete each matching override
        for (const override of matchingOverrides) {
          try {
            await hotelPricingService.deleteSeasonalPriceRules([override.id]);
            deletedCount++;
            deletedRuleIds.push(override.id);
            console.log(`Deleted seasonal price rule: ${override.id}`);
          } catch (deleteError) {
            console.error(`Failed to delete seasonal price rule ${override.id}:`, deleteError);
            // Continue with other rules even if one fails
          }
        }
      } catch (error) {
        console.error(`Error processing base price rule ${basePriceRule.id}:`, error);
        // Continue with other base price rules even if one fails
      }
    }

    console.log(`Successfully deleted ${deletedCount} seasonal pricing rules for season "${name}"`);

    res.json({
      message: `Successfully deleted ${deletedCount} seasonal pricing rules for season "${name}"`,
      deleted_count: deletedCount,
      deleted_rule_ids: deletedRuleIds,
      season: {
        name,
        start_date,
        end_date,
      },
    });
  } catch (error) {
    console.error("Error deleting seasonal pricing rules:", error);
    res.status(500).json({
      message: "An error occurred while deleting seasonal pricing rules",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
