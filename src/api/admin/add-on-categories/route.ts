import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { CreateLookupWorkflow } from "../../../workflows/lookup/create-lookup";
import { UpdateLookupWorkflow } from "../../../workflows/lookup/update-lookup";

// Validation schema for creating/updating an add-on category
const AddOnCategorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
});

export type AddOnCategoryType = z.infer<typeof AddOnCategorySchema>;

// GET /admin/add-on-categories
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    const { limit = 50, offset = 0 } = req.query || {};

    const {
      data: categories,
      metadata: { count, take, skip },
    } = await query.graph({
      entity: "lookup",
      filters: {
        entity_name: "add_on_category",
      },
      fields: ["*"],
      pagination: {
        skip: Number(offset),
        take: Number(limit),
      },
    });

    // Transform lookup data to category format
    const formattedCategories = categories.map((lookup: any) => {
      const categoryData = JSON.parse(lookup.value || "{}");
      return {
        id: lookup.id,
        name: categoryData.name || lookup.value,
        description: categoryData.description || "",
        is_active: categoryData.is_active === true,
        created_at: lookup.created_at,
        updated_at: lookup.updated_at,
      };
    });

    // Sort by name
    formattedCategories.sort((a, b) => a.name.localeCompare(b.name));

    res.json({
      categories: formattedCategories,
      count,
      limit: take,
      offset: skip,
    });
  } catch (error) {
    console.error("Error listing add-on categories:", error);
    res.status(500).json({
      message: "Failed to list add-on categories",
      error: error.message,
    });
  }
}

// POST /admin/add-on-categories
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Validate request body
    const validatedData = AddOnCategorySchema.parse(req.body);

    // Check if category with same name already exists
    const existingCategories = await query.graph({
      entity: "lookup",
      filters: {
        entity_name: "add_on_category",
      },
      fields: ["*"],
    });

    const nameExists = existingCategories.data.some((lookup: any) => {
      const categoryData = JSON.parse(lookup.value || "{}");
      const existingName = categoryData.name || lookup.value;
      return existingName.toLowerCase() === validatedData.name.toLowerCase();
    });

    if (nameExists) {
      return res.status(400).json({
        message: "Category with this name already exists",
      });
    }

    // Create category data
    const categoryData = {
      name: validatedData.name,
      description: validatedData.description || "",
      is_active: validatedData.is_active,
    };

    // Create lookup entry using the workflow
    const { result: newCategory } = await CreateLookupWorkflow(req.scope).run({
      input: {
        entity_name: "add_on_category",
        value: JSON.stringify(categoryData),
      },
    });

    console.log("Create result:", newCategory);

    // Format response
    const formattedCategory = {
      id: (newCategory as any)?.id || "unknown",
      name: categoryData.name,
      description: categoryData.description,
      is_active: categoryData.is_active,
      created_at: (newCategory as any)?.created_at || new Date().toISOString(),
      updated_at: (newCategory as any)?.updated_at || new Date().toISOString(),
    };

    res.status(201).json({
      category: formattedCategory,
    });
  } catch (error) {
    console.error("Error creating add-on category:", error);

    if (error instanceof z.ZodError) {
      res.status(400).json({
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      res.status(500).json({
        message: "Failed to create add-on category",
        error: error.message,
      });
    }
  }
}

// PUT /admin/add-on-categories (bulk update)
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Expect an array of categories to update
    const categories = Array.isArray(req.body) ? req.body : [req.body];
    const updatedCategories = [];

    for (const categoryUpdate of categories) {
      const { id, ...updateData } = categoryUpdate;

      if (!id) {
        continue; // Skip entries without ID
      }

      // Validate update data
      const validatedData = AddOnCategorySchema.partial().parse(updateData);

      // Get existing category
      const existingCategory = await query.graph({
        entity: "lookup",
        filters: { id },
        fields: ["*"],
      });

      if (!existingCategory.data || existingCategory.data.length === 0) {
        continue; // Skip if category doesn't exist
      }

      const currentData = JSON.parse(existingCategory.data[0].value || "{}");

      // Merge with existing data
      const updatedData = {
        name: validatedData.name || currentData.name,
        description:
          validatedData.description !== undefined
            ? validatedData.description
            : currentData.description,
        is_active:
          validatedData.is_active !== undefined
            ? validatedData.is_active
            : currentData.is_active,
      };

      // Update lookup entry using the workflow
      const { result: updated } = await UpdateLookupWorkflow(req.scope).run({
        input: {
          id,
          value: JSON.stringify(updatedData),
        },
      });

      updatedCategories.push({
        id: (updated as any)?.id || id,
        name: updatedData.name,
        description: updatedData.description,
        is_active: updatedData.is_active,
        created_at: (updated as any)?.created_at || new Date().toISOString(),
        updated_at: (updated as any)?.updated_at || new Date().toISOString(),
      });
    }

    res.json({
      categories: updatedCategories,
    });
  } catch (error) {
    console.error("Error updating add-on categories:", error);

    if (error instanceof z.ZodError) {
      res.status(400).json({
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      res.status(500).json({
        message: "Failed to update add-on categories",
        error: error.message,
      });
    }
  }
}
