import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";
import { UpdateLookupWorkflow } from "../../../../workflows/lookup/update-lookup";
import { DeleteLookupWorkflow } from "../../../../workflows/lookup/delete-lookup";

// Validation schema for updating an add-on category
const AddOnCategoryUpdateSchema = z.object({
  name: z.string().min(1, "Name is required").optional(),
  description: z.string().optional(),
  is_active: z.boolean().optional(),
});

// GET /admin/add-on-categories/:id
export async function GET(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    const result = await query.graph({
      entity: "lookup",
      filters: { id },
      fields: ["*"],
    });

    if (!result.data || result.data.length === 0) {
      return res.status(404).json({
        message: "Add-on category not found",
      });
    }

    const lookup = result.data[0];

    // Verify it's an add-on category
    if (lookup.entity_name !== "add_on_category") {
      return res.status(404).json({
        message: "Add-on category not found",
      });
    }

    const categoryData = JSON.parse(lookup.value || "{}");

    const formattedCategory = {
      id: lookup.id,
      name: categoryData.name || lookup.value,
      description: categoryData.description || "",
      is_active: categoryData.is_active === true,
      created_at: lookup.created_at,
      updated_at: lookup.updated_at,
    };

    res.json({
      category: formattedCategory,
    });
  } catch (error) {
    console.error("Error retrieving add-on category:", error);
    res.status(500).json({
      message: "Failed to retrieve add-on category",
      error: error.message,
    });
  }
}

// PUT /admin/add-on-categories/:id
export async function PUT(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Validate request body
    const validatedData = AddOnCategoryUpdateSchema.parse(req.body);

    // Get existing category
    const existingResult = await query.graph({
      entity: "lookup",
      filters: { id },
      fields: ["*"],
    });

    if (!existingResult.data || existingResult.data.length === 0) {
      return res.status(404).json({
        message: "Add-on category not found",
      });
    }

    const existingLookup = existingResult.data[0];

    // Verify it's an add-on category
    if (existingLookup.entity_name !== "add_on_category") {
      return res.status(404).json({
        message: "Add-on category not found",
      });
    }

    const currentData = JSON.parse(existingLookup.value || "{}");

    // Check if name is being changed and if it conflicts with existing categories
    if (validatedData.name && validatedData.name !== currentData.name) {
      const allCategories = await query.graph({
        entity: "lookup",
        filters: {
          entity_name: "add_on_category",
        },
        fields: ["*"],
      });

      const nameExists = allCategories.data.some((lookup: any) => {
        if (lookup.id === id) return false; // Skip current category
        const categoryData = JSON.parse(lookup.value || "{}");
        const existingName = categoryData.name || lookup.value;
        return existingName.toLowerCase() === validatedData.name.toLowerCase();
      });

      if (nameExists) {
        return res.status(400).json({
          message: "Category with this name already exists",
        });
      }
    }

    // Merge with existing data
    const updatedData = {
      name:
        validatedData.name !== undefined
          ? validatedData.name
          : currentData.name,
      description:
        validatedData.description !== undefined
          ? validatedData.description
          : currentData.description,
      is_active:
        validatedData.is_active !== undefined
          ? validatedData.is_active
          : currentData.is_active,
    };

    // Update lookup entry using the workflow
    const { result: updated } = await UpdateLookupWorkflow(req.scope).run({
      input: {
        id,
        value: JSON.stringify(updatedData),
      },
    });

    const formattedCategory = {
      id: (updated as any)?.id || id,
      name: updatedData.name,
      description: updatedData.description,
      is_active: updatedData.is_active,
      created_at: (updated as any)?.created_at || new Date().toISOString(),
      updated_at: (updated as any)?.updated_at || new Date().toISOString(),
    };

    res.json({
      category: formattedCategory,
    });
  } catch (error) {
    console.error("Error updating add-on category:", error);

    if (error instanceof z.ZodError) {
      res.status(400).json({
        message: "Validation error",
        errors: error.errors,
      });
    } else {
      res.status(500).json({
        message: "Failed to update add-on category",
        error: error.message,
      });
    }
  }
}

// DELETE /admin/add-on-categories/:id
export async function DELETE(req: MedusaRequest, res: MedusaResponse) {
  try {
    const { id } = req.params;
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);

    // Check if category exists
    const existingResult = await query.graph({
      entity: "lookup",
      filters: { id },
      fields: ["*"],
    });

    if (!existingResult.data || existingResult.data.length === 0) {
      return res.status(404).json({
        message: "Add-on category not found",
      });
    }

    const existingLookup = existingResult.data[0];

    // Verify it's an add-on category
    if (existingLookup.entity_name !== "add_on_category") {
      return res.status(404).json({
        message: "Add-on category not found",
      });
    }

    // TODO: Check if category is being used by any add-on services
    // For now, we'll allow deletion but in production you might want to prevent
    // deletion of categories that are in use

    // Delete the lookup entry using the workflow
    await DeleteLookupWorkflow(req.scope).run({
      input: { ids: [id] },
    });

    res.json({
      message: "Add-on category deleted successfully",
      id,
    });
  } catch (error) {
    console.error("Error deleting add-on category:", error);
    res.status(500).json({
      message: "Failed to delete add-on category",
      error: error.message,
    });
  }
}
