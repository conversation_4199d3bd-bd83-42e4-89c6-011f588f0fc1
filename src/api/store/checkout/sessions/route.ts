import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import {
  ContainerRegistrationKeys,
  remoteQueryObjectFromString,
} from "@camped-ai/framework/utils";
import { createPaymentCollectionForCartWorkflow } from "@camped-ai/medusa/core-flows";
import { STRIPE_CHECKOUT_SERVICE } from "../../../../modules/stripe-checkout";

/**
 * Store API endpoint for creating Stripe checkout sessions
 * Takes a cart_id and creates a Stripe Checkout Session
 * Returns the checkout URL for frontend redirection
 */
export async function POST(req: MedusaRequest, res: MedusaResponse) {
  try {
    console.log("Creating Stripe checkout session");

    // Check if req.scope exists
    if (!req.scope) {
      console.error("req.scope is undefined in checkout session creation");
      return res.status(400).json({
        message: "Scope is not available, cannot create checkout session",
      });
    }

    // Extract data from request body
    const { cart_id, success_url, cancel_url } = req.body as {
      cart_id: string;
      success_url: string;
      cancel_url: string;
    };

    // Validate required fields
    if (!cart_id) {
      return res.status(400).json({
        message: "cart_id is required",
      });
    }

    if (!success_url) {
      return res.status(400).json({
        message: "success_url is required",
      });
    }

    if (!cancel_url) {
      return res.status(400).json({
        message: "cancel_url is required",
      });
    }

    console.log(`Creating checkout session for cart: ${cart_id}`);

    // Get the cart with line items and metadata
    const queryService = req.scope.resolve(
      ContainerRegistrationKeys.REMOTE_QUERY
    );

    const [cart] = await queryService(
      remoteQueryObjectFromString({
        entryPoint: "cart",
        variables: { filters: { id: cart_id } },
        fields: [
          "id",
          "total",
          "subtotal",
          "tax_total",
          "currency_code",
          "email",
          "metadata",
          "items.*",
          "items.variant.*",
          "items.product.*",
        ],
      })
    );

    if (!cart) {
      return res.status(404).json({
        message: "Cart not found",
      });
    }

    console.log("Cart found:", {
      id: cart.id,
      total: cart.total,
      currency_code: cart.currency_code,
      items_count: cart.items?.length || 0,
    });

    // Check if cart has items
    if (!cart.items || cart.items.length === 0) {
      return res.status(400).json({
        message: "Cart is empty",
      });
    }

    // Check if cart total is valid
    if (!cart.total || cart.total <= 0) {
      return res.status(400).json({
        message: "Cart total must be greater than 0",
      });
    }

    // Create payment collection for the cart if it doesn't exist
    console.log("Creating payment collection for cart...");
    try {
      await createPaymentCollectionForCartWorkflow(req.scope).run({
        input: {
          cart_id: cart.id,
          metadata: {
            cart_id: cart.id,
            cart_total: cart.total,
            checkout_session: true,
          },
        },
      });
      console.log("Payment collection created successfully");
    } catch (error) {
      // If payment collection already exists, that's fine
      if (error.message?.includes("already has a payment collection")) {
        console.log("Payment collection already exists for cart");
      } else {
        console.error("Error creating payment collection:", error);
        return res.status(500).json({
          message: "Failed to create payment collection",
          error: error instanceof Error ? error.message : "Unknown error",
        });
      }
    }

    // Prepare line items for Stripe
    const lineItems = [];

    // Process regular cart items (rooms, etc.)
    cart.items.forEach((item: any) => {
      const unitAmount = Math.round((item.unit_price || 0) * 100); // Convert to cents

      // Validate that the unit amount is a valid number
      if (isNaN(unitAmount) || unitAmount <= 0) {
        console.warn(
          `Invalid unit amount for cart item: ${unitAmount}, skipping item ${item.id}`
        );
        return; // Skip this item
      }

      const productName = item.title || item.product?.title || "Hotel Booking";

      // Extract hotel and room information from metadata
      const hotelName =
        item.metadata?.hotel_name || cart.metadata?.hotel_name || "Hotel";
      const roomConfigName =
        item.metadata?.room_config_name ||
        cart.metadata?.room_config_name ||
        "Room";
      const checkInDate =
        item.metadata?.check_in_date || cart.metadata?.check_in_date;
      const checkOutDate =
        item.metadata?.check_out_date || cart.metadata?.check_out_date;

      let description = `${hotelName} - ${roomConfigName}`;
      if (checkInDate && checkOutDate) {
        const checkIn = new Date(checkInDate).toLocaleDateString();
        const checkOut = new Date(checkOutDate).toLocaleDateString();
        description += ` (${checkIn} - ${checkOut})`;
      }

      lineItems.push({
        price_data: {
          currency: cart.currency_code.toLowerCase(),
          product_data: {
            name: productName,
            description: description,
          },
          unit_amount: unitAmount,
        },
        quantity: item.quantity || 1,
      });
    });

    // Add extra beds as separate line items if they exist
    if (cart.metadata?.extra_bed_details && cart.metadata?.extra_beds > 0) {
      const extraBedDetails = cart.metadata.extra_bed_details;
      const extraBedQuantity =
        cart.metadata.extra_beds || extraBedDetails.number_of_beds || 1;

      // Calculate total price from extra bed details or use extra_bed_total from cart metadata
      let extraBedTotalPrice = cart.metadata?.extra_bed_total || 0;

      // If no extra_bed_total in metadata, try to get from extra bed details
      if (!extraBedTotalPrice && extraBedDetails?.total_price) {
        extraBedTotalPrice = extraBedDetails.total_price;
      }

      // Calculate unit amount for extra beds (price per bed for the entire stay)
      const extraBedUnitAmount = Math.round(
        (extraBedTotalPrice / extraBedQuantity) * 100
      ); // Convert to cents

      // Validate that the unit amount is a valid number
      if (isNaN(extraBedUnitAmount) || extraBedUnitAmount <= 0) {
        console.warn(
          `Invalid extra bed unit amount: ${extraBedUnitAmount}, skipping extra bed line item`
        );
      } else {
        const hotelName = cart.metadata?.hotel_name || "Hotel";
        const roomName = extraBedDetails.room_name || "Room";
        const checkInDate =
          cart.metadata?.check_in_date || extraBedDetails.check_in;
        const checkOutDate =
          cart.metadata?.check_out_date || extraBedDetails.check_out;
        const nights = extraBedDetails.nights || 1;

        let extraBedDescription = `${hotelName} - Extra Bed for ${roomName}`;
        if (checkInDate && checkOutDate) {
          const checkIn = new Date(checkInDate).toLocaleDateString();
          const checkOut = new Date(checkOutDate).toLocaleDateString();
          extraBedDescription += ` (${checkIn} - ${checkOut}, ${nights} night${
            nights > 1 ? "s" : ""
          })`;
        }

        lineItems.push({
          price_data: {
            currency: cart.currency_code.toLowerCase(),
            product_data: {
              name: "Extra Bed",
              description: extraBedDescription,
            },
            unit_amount: extraBedUnitAmount,
          },
          quantity: extraBedQuantity,
        });

        console.log(`Added extra bed line item:`, {
          quantity: extraBedQuantity,
          unit_amount: extraBedUnitAmount,
          total_amount: extraBedUnitAmount * extraBedQuantity,
          description: extraBedDescription,
        });
      }
    }

    // Add cots as separate line items if they exist
    if (cart.metadata?.cot_details && cart.metadata?.cots > 0) {
      const cotDetails = cart.metadata.cot_details;
      const cotQuantity = cart.metadata.cots || cotDetails.number_of_cots || 1;

      // Calculate total price from daily breakdown or use cot_total from cart metadata
      let cotTotalPrice = cart.metadata?.cot_total || 0;

      // If no cot_total in metadata, calculate from daily breakdown
      if (
        !cotTotalPrice &&
        cotDetails.daily_breakdown &&
        Array.isArray(cotDetails.daily_breakdown)
      ) {
        cotTotalPrice = cotDetails.daily_breakdown.reduce(
          (total: number, day: any) => {
            return total + (day.price_per_cot || 0);
          },
          0
        );
      }

      // Calculate unit amount for cots (price per cot for the entire stay)
      const cotUnitAmount = Math.round((cotTotalPrice / cotQuantity) * 100); // Convert to cents

      // Validate that the unit amount is a valid number
      if (isNaN(cotUnitAmount) || cotUnitAmount <= 0) {
        console.warn(
          `Invalid cot unit amount: ${cotUnitAmount}, skipping cot line item`
        );
      } else {
        const hotelName = cart.metadata?.hotel_name || "Hotel";
        const roomName = cotDetails.room_name || "Room";
        const checkInDate = cart.metadata?.check_in_date || cotDetails.check_in;
        const checkOutDate =
          cart.metadata?.check_out_date || cotDetails.check_out;
        const nights = cotDetails.nights || 1;

        let cotDescription = `${hotelName} - Cot for ${roomName}`;
        if (checkInDate && checkOutDate) {
          const checkIn = new Date(checkInDate).toLocaleDateString();
          const checkOut = new Date(checkOutDate).toLocaleDateString();
          cotDescription += ` (${checkIn} - ${checkOut}, ${nights} night${
            nights > 1 ? "s" : ""
          })`;
        }

        lineItems.push({
          price_data: {
            currency: cart.currency_code.toLowerCase(),
            product_data: {
              name: "Cot",
              description: cotDescription,
            },
            unit_amount: cotUnitAmount,
          },
          quantity: cotQuantity,
        });

        console.log(`Added cot line item:`, {
          quantity: cotQuantity,
          unit_amount: cotUnitAmount,
          total_amount: cotUnitAmount * cotQuantity,
          description: cotDescription,
        });
      }
    }

    // Note: Extra adults beyond capacity are already handled as cart line items
    // The cart API adds them as line items, so they'll be processed in the cart.items.forEach loop above
    // No need to add them again here to avoid duplicates

    // Add add-ons as separate line items if they exist
    if (cart.metadata?.add_ons && Array.isArray(cart.metadata.add_ons)) {
      cart.metadata.add_ons.forEach((addon: any, index: number) => {
        if (addon.total && addon.total > 0) {
          const addonUnitAmount = Math.round(addon.total * 100); // Convert to cents

          // Validate that the unit amount is a valid number
          if (isNaN(addonUnitAmount) || addonUnitAmount <= 0) {
            console.warn(
              `Invalid add-on unit amount: ${addonUnitAmount}, skipping add-on ${
                addon.name || index
              }`
            );
            return; // Skip this add-on
          }
          const hotelName = cart.metadata?.hotel_name || "Hotel";
          const checkInDate = cart.metadata?.check_in_date;
          const checkOutDate = cart.metadata?.check_out_date;

          let addonDescription = `${hotelName} - ${
            addon.name || "Add-on Service"
          }`;
          if (checkInDate && checkOutDate) {
            const checkIn = new Date(checkInDate).toLocaleDateString();
            const checkOut = new Date(checkOutDate).toLocaleDateString();
            addonDescription += ` (${checkIn} - ${checkOut})`;
          }

          // Add pricing details to description
          if (addon.pricing_type === "per_person") {
            const adults = addon.adult_quantity || 0;
            const children = addon.child_quantity || 0;
            if (adults > 0 || children > 0) {
              addonDescription += ` - ${adults} adult${
                adults !== 1 ? "s" : ""
              }`;
              if (children > 0) {
                addonDescription += `, ${children} child${
                  children !== 1 ? "ren" : ""
                }`;
              }
            }
          } else if (addon.pricing_type === "package") {
            addonDescription += ` - ${addon.package_quantity || 1} package${
              (addon.package_quantity || 1) !== 1 ? "s" : ""
            }`;
          } else if (addon.pricing_type === "usage_based") {
            const totalDays =
              addon.usage_details?.reduce(
                (sum: number, usage: any) => sum + (usage.days || 0),
                0
              ) || 0;
            addonDescription += ` - ${totalDays} day${
              totalDays !== 1 ? "s" : ""
            } usage`;
          }

          lineItems.push({
            price_data: {
              currency: cart.currency_code.toLowerCase(),
              product_data: {
                name: addon.name || `Add-on Service ${index + 1}`,
                description: addonDescription,
              },
              unit_amount: addonUnitAmount,
            },
            quantity: 1,
          });

          console.log(`Added add-on line item:`, {
            name: addon.name,
            unit_amount: addonUnitAmount,
            pricing_type: addon.pricing_type,
            description: addonDescription,
          });
        }
      });
    }

    // Log all line items for debugging
    console.log(
      `Created ${lineItems.length} line items for Stripe checkout:`,
      lineItems.map((item, index) => ({
        index: index + 1,
        name: item.price_data.product_data.name,
        description: item.price_data.product_data.description,
        unit_amount: item.unit_amount,
        quantity: item.quantity,
        total_amount: item.price_data.unit_amount * item.quantity,
      }))
    );

    // Use the URLs provided in the request payload
    // Note: Stripe will replace {CHECKOUT_SESSION_ID} with the actual session ID in success_url
    const successUrlWithSessionId = success_url.includes(
      "{CHECKOUT_SESSION_ID}"
    )
      ? success_url
      : `${success_url}${
          success_url.includes("?") ? "&" : "?"
        }session_id={CHECKOUT_SESSION_ID}`;

    console.log("Using URLs:", {
      success_url: successUrlWithSessionId,
      cancel_url: cancel_url,
    });

    // Prepare metadata for the session
    const sessionMetadata = {
      cart_id: cart.id,
      hotel_id: cart.metadata?.hotel_id || "",
      room_config_id: cart.metadata?.room_config_id || "",
      guest_email: cart.email || cart.metadata?.guest_email || "",
      guest_name: cart.metadata?.guest_name || "",
      check_in_date: cart.metadata?.check_in_date || "",
      check_out_date: cart.metadata?.check_out_date || "",
      number_of_guests: cart.metadata?.number_of_guests?.toString() || "1",
      number_of_rooms: cart.metadata?.number_of_rooms?.toString() || "1",
      extra_beds: cart.metadata?.extra_beds?.toString() || "0",
      cots: cart.metadata?.cots?.toString() || "0",
      extra_adults_beyond_capacity:
        cart.metadata?.extra_adults_beyond_capacity?.toString() || "0",
      has_extra_beds: cart.metadata?.extra_bed_details ? "true" : "false",
      has_cots: cart.metadata?.cot_details ? "true" : "false",
      has_extra_adults_beyond_capacity: cart.metadata
        ?.extra_adults_beyond_capacity_details
        ? "true"
        : "false",
      has_add_ons:
        cart.metadata?.add_ons && cart.metadata.add_ons.length > 0
          ? "true"
          : "false",
    };

    // Get the Stripe checkout service
    const stripeCheckoutService = req.scope.resolve(STRIPE_CHECKOUT_SERVICE);

    // Create the checkout session
    const checkoutSession = await stripeCheckoutService.createCheckoutSession({
      cart_id: cart.id,
      line_items: lineItems,
      customer_email: cart.email || cart.metadata?.guest_email,
      success_url: successUrlWithSessionId,
      cancel_url: cancel_url,
      metadata: sessionMetadata,
    });

    console.log("Checkout session created successfully:", {
      session_id: checkoutSession.id,
      cart_id: cart.id,
      checkout_url: checkoutSession.url,
    });

    // Note: We'll create the payment session during cart completion when we have the payment intent data
    console.log(
      "Checkout session created, payment session will be created during completion"
    );

    return res.json({
      checkout_url: checkoutSession.url,
      session_id: checkoutSession.id,
      cart_id: cart.id,
    });
  } catch (error) {
    console.error("Error creating checkout session:", error);
    return res.status(500).json({
      message: "An error occurred while creating the checkout session",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
}
