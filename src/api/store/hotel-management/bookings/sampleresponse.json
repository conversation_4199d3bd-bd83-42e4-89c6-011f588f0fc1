{"bookings": [{"id": "order_01JXHJ61E9XS0CYF6BHCWWN14G", "order_id": "order_01JXHJ61E9XS0CYF6BHCWWN14G", "customer_id": "cus_01JWQR6ES36X7JP42RC4551ENM", "guest_email": "<EMAIL>", "guest_name": "jiya pahwa", "guest_phone": "**********", "hotel_id": "01JWT8VVPSCRECG0GBSWBREST7", "hotel_name": "The Chedi Andermatt", "hotel_location": "Gotthardstrasse 4, 6490 Andermatt, Switzerland", "hotel_address": "Gotthardstrasse 4, 6490 Andermatt, Switzerland", "hotel_city": null, "hotel_country": null, "hotel_postal_code": null, "hotel_timezone": null, "destination_id": "01JSBQSX3G6W0YSMW9X4EMN97P", "destination_name": "<PERSON><PERSON><PERSON><PERSON>", "room_config_id": "prod_01JWT947JDKFZ65T20Y8BCRT0J", "room_config_name": "Grand Deluxe Suite", "room_type": "Standard", "check_in_date": "2025-06-16T00:00:00.000Z", "check_out_date": "2025-06-19T00:00:00.000Z", "check_in_time": "14:00", "check_out_time": "11:00", "number_of_guests": 5, "currency_code": "usd", "status": "canceled", "booking_status": "active", "total_amount": 12510, "created_at": "2025-06-12T07:55:29.104Z", "updated_at": "2025-06-13T10:25:00.659Z", "guest_info": {"primary_guest": {"name": "jiya pahwa", "email": "<EMAIL>", "phone": "**********"}, "customer_account": {"first_name": "jiya", "last_name": "pahwa", "email": "<EMAIL>", "phone": "+91 **********", "metadata": {"source": "otp_registration", "converted_at": "2025-06-03T08:52:21.389Z", "converted_from_guest": true}, "addresses": []}, "preferred_contact": {"full_name": "jiya pahwa", "email": "<EMAIL>", "phone": "**********"}, "travelers": {"adults": [{"id": "adult_1", "name": "jiya pahwa", "age": null, "type": "adult"}, {"id": "adult_2", "name": "<PERSON><PERSON><PERSON>", "age": 23, "type": "adult"}, {"id": "adult_3", "name": "<PERSON><PERSON>", "age": 20, "type": "adult"}], "children": [{"id": "child_1", "name": "<PERSON>", "age": 9, "type": "child"}, {"id": "child_2", "name": "Citadel", "age": 11, "type": "child"}], "infants": [], "total_count": 5, "summary": {"adults": 3, "children": 2, "infants": 0}}}, "metadata": {"adults": 3, "add_ons": [{"name": "Shuttle", "service_id": "prod_01JWZY0NJWFBW8BCV3VAYSWQW1", "adult_price": 120, "child_price": 80, "description": "Complimentary or pre-booked shuttle service between your accommodation and key transport hubs or attractions. Includes scheduled pick-up and drop-off at select locations. Ideal for seamless transfers during your stay.", "guest_usage": [], "total_price": 520, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 3, "child_quantity": 2, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Swedish Relaxation Massage", "service_id": "prod_01JX02Z2T82C8E1JD8X28RY3AR", "adult_price": 80, "child_price": 60, "description": "A timeless classic designed to melt away stress and tension. Our Swedish Relaxation Massage uses long, flowing strokes with gentle to medium pressure to improve circulation, ease muscle discomfort, and promote deep relaxation. Ideal for first-time spa-goers or those seeking pure serenity, this soothing treatment leaves your body refreshed and your mind at peace.", "guest_usage": [], "total_price": 360, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 3, "child_quantity": 2, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Wine & Mini Bar Upgrade", "service_id": "prod_01JX0360MN66E4GM8QHKBGHGWS", "adult_price": 90, "child_price": 0, "description": "Upgrade your in-room bar with a selection of premium wines, craft spirits, and gourmet snacks. Ideal for a cozy night in or a celebratory toast.", "guest_usage": [], "total_price": 270, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 3, "child_quantity": 0, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "<PERSON>", "service_id": "prod_01JX0CC3NWEQHE5J103E3X3844", "adult_price": 0, "child_price": 0, "description": "Start your stay with a touch of indulgence. Our curated Welcome Hamper includes a selection of local delicacies, fine chocolates, a mini bottle of wine, and handcrafted wellness items. Perfect for unwinding after your journey or as a surprise treat for someone special. Delivered to your room upon arrival.\n", "guest_usage": [], "total_price": 80, "usage_dates": [], "pricing_type": "package", "currency_code": "USD", "package_price": 80, "service_level": "hotel", "adult_quantity": 0, "child_quantity": 0, "number_of_days": 0, "total_occupancy": 5, "package_quantity": 1, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Skiing", "service_id": "prod_01JXA3TCV5W2ZNEX8AWQJQXDTG", "adult_price": 0, "child_price": 0, "description": "Skiing", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-16", "2025-06-17"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-17", "2025-06-18"]}, {"guest_type": "adult", "guest_index": 2, "usage_dates": ["2025-06-17", "2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-17", "2025-06-18"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-17", "2025-06-16"]}], "total_price": 160, "usage_dates": ["2025-06-16", "2025-06-17", "2025-06-18"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 6, "child_quantity": 4, "number_of_days": 3, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 20, "per_day_child_price": 10}, {"name": "Snowzone", "service_id": "prod_01JXA65E9X56GM2GV732EHZABN", "adult_price": 300, "child_price": 200, "description": "Designated snow-covered area or facility where skiing, snowboarding, and other snow-related activities take place.", "guest_usage": [], "total_price": 1300, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 3, "child_quantity": 2, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Fitness Center Access", "service_id": "prod_01JXA8K32CQ6P65X2C6BV4Z2A1", "adult_price": 0, "child_price": 0, "description": "24/7 Gym Access,\nState-of-the-art Cardio Equipment (Treadmills, Bikes, Ellipticals),\nStrength Training Machines,\nFree Weights & Resistance Bands,\nSmart Fitness Mirrors / Virtual Workout Stations,\nWellness Towels, Water, and Headphones Provided\n", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-16"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-16"]}, {"guest_type": "adult", "guest_index": 2, "usage_dates": ["2025-06-18"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-16"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-17"]}], "total_price": 260, "usage_dates": ["2025-06-16", "2025-06-17", "2025-06-18"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 3, "child_quantity": 2, "number_of_days": 3, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 60, "per_day_child_price": 40}, {"name": "Spa Essentials ", "service_id": "prod_01JXAA1VDF5QS5FTQ802VS8JRV", "adult_price": 0, "child_price": 0, "description": "Steam, Sauna & Jacuzzi Access\nInfrared Sauna Session\nHerbal Bath Soak\nJet Shower Experience\nCold Plunge Therapy\nSpa Refreshments (detox tea, infused water, etc.)\n", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-17", "2025-06-18"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-17", "2025-06-18"]}, {"guest_type": "adult", "guest_index": 2, "usage_dates": ["2025-06-17", "2025-06-18"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-17", "2025-06-18"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-17", "2025-06-18"]}], "total_price": 380, "usage_dates": ["2025-06-17", "2025-06-18"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 6, "child_quantity": 4, "number_of_days": 2, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 50, "per_day_child_price": 20}, {"name": "City sightseeing tour", "service_id": "prod_01JXCN8GNQRFX94JYZ93KS1TJ3", "adult_price": 250, "child_price": 150, "description": "Discover the best of the city with a guided sightseeing tour that highlights its most iconic landmarks, cultural spots, and hidden gems. Travel comfortably by bus or on foot as a knowledgeable local guide shares fascinating stories, historical insights, and insider tips.\n\nIncludes:\nVisits to major attractions (e.g., monuments, museums, plazas)\n\nOptional hotel pickup and drop-off\nAudio guides or multilingual commentary (in select tours)\nSmall group or private options available\nIdeal for: First-time visitors, history lovers, and anyone looking to experience the city beyond the guidebook.\nDuration: 2 to 4 hours (half-day) or full-day options available.\n\n", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-16"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-16"]}, {"guest_type": "adult", "guest_index": 2, "usage_dates": ["2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-16"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-16"]}], "total_price": 800, "usage_dates": ["2025-06-16"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "destination", "adult_quantity": 3, "child_quantity": 2, "number_of_days": 1, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 200, "per_day_child_price": 100}, {"name": "Public Transport Pass", "service_id": "prod_01JXCNRN9S4E62VPK3RDWN9V61", "adult_price": 30, "child_price": 20, "description": "Make getting around easy and affordable with a public transport pass valid throughout your stay. Enjoy unlimited access to local buses, trams, subways, or regional trains — perfect for sightseeing, shopping, and exploring nearby neighborhoods without the hassle of tickets or traffic.\nIdeal for: City explorers, business travelers, or guests planning to visit multiple attractions.\nAvailability: Available for 1-day, 3-day, or week-long durations.", "guest_usage": [], "total_price": 130, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "destination", "adult_quantity": 3, "child_quantity": 2, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Flight Services", "service_id": "prod_01JXEQMZBG51NB680RG1R5NHKF", "adult_price": 0, "child_price": 0, "description": "Now plan your entire trip in one place! With our new Flight Booking Add-On, you can seamlessly book flights while reserving your hotel — saving time, effort, and ensuring a smoother travel experience. Whether you're planning a quick getaway or a long vacation, enjoy the convenience of bundling your stay and travel together.......\nBest fares, top airlines, and full itinerary control — all in one platform.", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-18", "2025-06-16"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-18"]}, {"guest_type": "adult", "guest_index": 2, "usage_dates": ["2025-06-18", "2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-16", "2025-06-18"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-16", "2025-06-18"]}], "total_price": 7200, "usage_dates": ["2025-06-16", "2025-06-18"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "destination", "adult_quantity": 5, "child_quantity": 4, "number_of_days": 2, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 800, "per_day_child_price": 800}], "cart_id": "cart_01JXHJ4NVF09CG0TQ8WYMZKWFE", "infants": 0, "children": 2, "hotel_id": "01JWT8VVPSCRECG0GBSWBREST7", "meal_plan": "bb", "travelers": {"adults": [{"name": "jiya pahwa"}, {"age": 23, "name": "<PERSON><PERSON><PERSON>"}, {"age": 20, "name": "<PERSON><PERSON>"}], "infants": [], "children": [{"age": 9, "name": "<PERSON>"}, {"age": 11, "name": "Citadel"}]}, "guest_name": "jiya pahwa", "guest_email": "<EMAIL>", "guest_phone": "**********", "reservations": [{"id": "room_avail_01JWT9GJ5W6102189F1389BFG0", "status": "cart_reserved", "room_id": "variant_01JWT9EZC0AEHF2R88JJT6E3QM", "to_date": "2025-06-19T00:00:00.000Z", "from_date": "2025-06-18T00:00:00.000Z"}, {"id": "room_avail_01JWT9GJ5WHYBCAY25TQ0VZZE9", "status": "cart_reserved", "room_id": "variant_01JWT9EZC0AEHF2R88JJT6E3QM", "to_date": "2025-06-17T00:00:00.000Z", "from_date": "2025-06-16T00:00:00.000Z"}, {"id": "room_avail_01JWT9GJ5WTV5GRZT6TBEVX5P0", "status": "cart_reserved", "room_id": "variant_01JWT9EZC0AEHF2R88JJT6E3QM", "to_date": "2025-06-18T00:00:00.000Z", "from_date": "2025-06-17T00:00:00.000Z"}], "total_amount": 1050, "check_in_date": "2025-06-16T00:00:00.000Z", "check_in_time": "14:00", "currency_code": "usd", "check_out_date": "2025-06-19T00:00:00.000Z", "check_out_time": "11:00", "payment_status": "paid", "room_config_id": "prod_01JWT947JDKFZ65T20Y8BCRT0J", "number_of_rooms": 1, "number_of_guests": 5, "room_config_name": "Grand Deluxe Suite", "add_on_total_amount": 1146000, "payment_completed_at": "2025-06-12T07:55:36.011Z", "stripe_payment_intent_id": "pi_3RZ5yhAQUNgZJQ2K0ZRa0SNK", "stripe_checkout_session_id": "cs_test_b1lrySJ8T6YPnKtGb6EVBC81C5p6iDNEqFXaCEfTQWC09Dn0gLYc49HSRx"}}], "count": 1, "total_bookings_count": 38, "upcoming_trips_count": 7, "active_trips_count": 1, "completed_trips_count": 30, "limit": 6, "offset": 0, "has_more": false}