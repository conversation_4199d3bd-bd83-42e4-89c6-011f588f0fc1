import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";

// Extended request type to include JWT token data
interface AuthenticatedRequest extends MedusaRequest {
  user?: {
    customer_id?: string;
    actor_id?: string;
    actor_type?: string;
    app_metadata?: {
      customer_id?: string;
    };
  };
}

/**
 * Get customer's bookings with optimized pagination and filtering
 *
 * Query Parameters:
 * - limit: Number of items per page (default: 50, max: 100)
 * - offset: Number of items to skip (default: 0)
 * - page: Page number (alternative to offset, 1-based)
 * - hotel_id: Filter by specific hotel ID
 * - status: Filter by booking status
 * - payment_status: Filter by payment status
 * - booking_status: Filter by booking status (upcoming, active, or completed)
 * - sort_by: Field to sort by (default: created_at)
 * - sort_order: Sort direction - 'asc' or 'desc' (default: desc)
 *
 * Response Structure:
 * {
 *   "bookings": [
 *     {
 *       "hotel_name": "Grand Hotel",
 *       "hotel_location": "City Center",
 *       "hotel_address": "123 Main Street",
 *       "hotel_city": "Paris",
 *       "hotel_country": "France",
 *       "hotel_postal_code": "75001",
 *       "hotel_timezone": "Europe/Paris",
 *       "destination_name": "Paris",
 *       "room_config_name": "Grand Deluxe Suite",
 *       ...
 *     }
 *   ],
 *   "count": 77,                    // Total number of bookings matching filters
 *   "total_bookings_count": 100,    // Total number of all bookings (unfiltered)
 *   "upcoming_trips_count": 12,     // Number of upcoming trips (check-in date > today)
 *   "active_trips_count": 4,        // Number of active trips (currently staying)
 *   "completed_trips_count": 65,    // Number of completed trips (check-out date < today)
 *   "limit": 6,                     // Items per page
 *   "offset": 0,                    // Current offset
 *   "has_more": true                // Whether there are more items available
 * }
 *
 * Examples:
 * - GET /bookings?limit=10&page=2
 * - GET /bookings?hotel_id=hotel_123&status=confirmed
 * - GET /bookings?booking_status=active
 * - GET /bookings?booking_status=upcoming&sort_by=check_in_date&sort_order=asc
 */
export async function GET(req: AuthenticatedRequest, res: MedusaResponse) {
  try {
    console.log("req.headers:", req.headers);

    // Try to get customer ID from different possible sources
    let customerId = req.session?.customer_id;

    // If not in session directly, try to get from JWT token data
    if (!customerId && req.user) {
      // Check if customer_id is in user object directly
      customerId = req.user.customer_id;

      // Or check if it's in the app_metadata
      if (!customerId && req.user.app_metadata?.customer_id) {
        customerId = req.user.app_metadata.customer_id;
      }

      // Or check if actor_id is a customer ID
      if (
        !customerId &&
        req.user.actor_id &&
        req.user.actor_type === "customer"
      ) {
        customerId = req.user.actor_id;
      }
    }

    // Try to extract from Authorization header (Bearer token)
    if (!customerId && req.headers.authorization) {
      try {
        const authHeader = req.headers.authorization;

        // Check if it's a Bearer token
        if (authHeader.startsWith("Bearer ")) {
          const token = authHeader.substring(7); // Remove 'Bearer ' prefix
          console.log("Found JWT token in Authorization header:", token);

          // If the token is JWT format, try to decode it
          if (token.includes(".")) {
            const parts = token.split(".");
            if (parts.length === 3) {
              try {
                // Decode the payload (middle part)
                const payload = JSON.parse(
                  Buffer.from(parts[1], "base64").toString()
                );
                console.log("Decoded JWT payload:", payload);

                // Extract customer ID from payload
                if (payload.actor_type === "customer" && payload.actor_id) {
                  customerId = payload.actor_id;
                } else if (payload.app_metadata?.customer_id) {
                  customerId = payload.app_metadata.customer_id;
                } else if (payload.customer_id) {
                  customerId = payload.customer_id;
                }
              } catch (e) {
                console.error("Error decoding JWT:", e);
              }
            }
          }
        }
      } catch (e) {
        console.error(
          "Error extracting customer ID from Authorization header:",
          e
        );
      }
    }

    // Try to extract from x-customer-id header directly
    if (!customerId && req.headers["x-customer-id"]) {
      customerId = req.headers["x-customer-id"] as string;
      console.log("Found customer ID in x-customer-id header:", customerId);
    }

    // Fallback: Try to extract from cookies if still no customer ID
    if (!customerId && req.headers.cookie) {
      try {
        // Find the JWT token in the cookies
        const cookies = req.headers.cookie.split(";").map((c) => c.trim());
        const jwtCookie = cookies.find((c) => c.startsWith("connect.sid="));

        if (jwtCookie) {
          const token = jwtCookie.split("=")[1];
          console.log("Found JWT token in cookies:", token);

          // If the token is JWT format, try to decode it
          if (token.includes(".")) {
            const parts = token.split(".");
            if (parts.length === 3) {
              try {
                // Decode the payload (middle part)
                const payload = JSON.parse(
                  Buffer.from(parts[1], "base64").toString()
                );
                console.log("Decoded JWT payload:", payload);

                // Extract customer ID from payload
                if (payload.actor_type === "customer" && payload.actor_id) {
                  customerId = payload.actor_id;
                } else if (payload.app_metadata?.customer_id) {
                  customerId = payload.app_metadata.customer_id;
                }
              } catch (e) {
                console.error("Error decoding JWT:", e);
              }
            }
          }
        }
      } catch (e) {
        console.error("Error extracting customer ID from cookies:", e);
      }
    }

    console.log("Customer ID extracted:", customerId);

    if (!customerId) {
      return res.status(401).json({
        message:
          "Unauthorized. Please provide a valid customer ID via one of the following methods: Authorization header (Bearer token), x-customer-id header, or session cookie.",
        supportedAuthMethods: [
          "Authorization: Bearer <jwt_token>",
          "x-customer-id: <customer_id>",
          "Cookie: connect.sid=<session_cookie>",
        ],
      });
    }

    console.log(`Fetching bookings for customer: ${customerId}`);

    // Get pagination and filter parameters from query
    const {
      limit = 50,
      offset = 0,
      page,
      hotel_id,
      status,
      payment_status,
      booking_status,
      sort_by = 'created_at',
      sort_order = 'desc'
    } = req.query || {};

    // Handle page-based pagination (convert to offset)
    let offsetNum = parseInt(offset as string, 10);
    const limitNum = Math.min(parseInt(limit as string, 10), 100); // Cap at 100 items per page

    if (page) {
      const pageNum = Math.max(1, parseInt(page as string, 10));
      offsetNum = (pageNum - 1) * limitNum;
    }

    console.log(`Pagination: limit=${limitNum}, offset=${offsetNum}, page=${page || 'N/A'}`);
    console.log(`Filters: hotel_id=${hotel_id}, status=${status}, payment_status=${payment_status}, booking_status=${booking_status}`);
    console.log(`Sorting: ${sort_by} ${sort_order}`);

    try {
      const startTime = Date.now();

      // Use query.graph to efficiently get orders with hotel metadata
      const query = req.scope.resolve("query");

      console.log(`Getting optimized bookings for customer: ${customerId}`);

      // Get current date for upcoming/completed comparison
      const currentDate = new Date().toISOString().split('T')[0];
      console.log("Current date for booking status:", currentDate);

      // Build database-level filters for better performance
      const dbFilters: any = {
        customer_id: customerId,
      };

      // Add status filter at database level if specified
      if (status && status !== 'all') {
        dbFilters.status = status;
      }

      // Add payment_status filter at database level if specified
      if (payment_status && payment_status !== 'all') {
        dbFilters.payment_status = payment_status;
      }

      // Add hotel_id filter at database level if specified
      if (hotel_id) {
        dbFilters["metadata.hotel_id"] = hotel_id;
      }

      // Add booking status filter
      if (booking_status) {
        // NOTE: DB filter is left as is, but we will re-filter in-memory below for exact date-only match
      }

      console.log("🔍 Database filters:", dbFilters);

      // Handle sorting for metadata fields
      let orderBy: Record<string, any> = {};
      if (sort_by === "check_in_date" || sort_by === "check_out_date") {
        // For metadata fields, we'll sort in memory after fetching
        orderBy = { created_at: sort_order };
      } else {
        orderBy = { [sort_by as string]: sort_order };
      }

      // Fetch all orders for the customer (no pagination)
      const ordersQuery = query.graph({
        entity: "order",
        filters: dbFilters,
        fields: [
          "id",
          "status",
          "email",
          "customer_id",
          "currency_code",
          "payment_status",
          "metadata",
          "created_at",
          "updated_at",
          "total"
        ],
        pagination: {
          order: orderBy
        }
      });

      // Execute query
      const { data: allOrders } = await ordersQuery;

      console.log(`Found ${allOrders.length} orders from database (no pagination)`);

      // Get unique IDs for related data
      const hotelIds = [...new Set(allOrders.map((order: any) => order.metadata?.hotel_id).filter(Boolean))];
      const roomConfigIds = [...new Set(allOrders.map((order: any) => order.metadata?.room_config_id).filter(Boolean))];
      const customerIds = [...new Set(allOrders.map((order: any) => order.customer_id).filter(Boolean))];

      // Fetch all related data and booking counts in parallel for better performance
      const [hotelDetails, roomConfigDetails, customerDetails, bookingCounts] = await Promise.all([
        // Fetch hotel details with all location fields
        hotelIds.length > 0
          ? query.graph({
              entity: "hotel",
              filters: { id: { $in: hotelIds } },
              fields: [
                "id",
                "name",
                "destination_id",
                "location",
                "address",
                "city",
                "country",
                "postal_code",
                "timezone"
              ],
            }).then(({ data: hotels }) => {
              const details = {};
              if (hotels && hotels.length > 0) {
                hotels.forEach((hotel: any) => {
                  details[hotel.id] = {
                    name: hotel.name,
                    destination_id: hotel.destination_id,
                    location: hotel.location,
                    address: hotel.address,
                    city: hotel.city,
                    country: hotel.country,
                    postal_code: hotel.postal_code,
                    timezone: hotel.timezone
                  };
                });
              }
              return details;
            })
          : Promise.resolve({}),

        // Fetch room configuration details
        roomConfigIds.length > 0
          ? query.graph({
              entity: "product",
              filters: { id: { $in: roomConfigIds } },
              fields: ["id", "title"],
            }).then(({ data: products }) => {
              const details = {};
              if (products && products.length > 0) {
                products.forEach((product: { id: string; title: string }) => {
                  details[product.id] = product.title;
                });
              }
              return details;
            })
          : Promise.resolve({}),

        // Fetch customer details
        customerIds.length > 0
          ? query.graph({
              entity: "customer",
              filters: { id: { $in: customerIds } },
              fields: [
                "id",
                "first_name",
                "last_name",
                "email",
                "phone",
                "metadata",
                "addresses.*",
              ],
            }).then(({ data: customers }) => {
              const details = {};
              if (customers && customers.length > 0) {
                customers.forEach((customer: any) => {
                  details[customer.id] = customer;
                });
              }
              return details;
            })
          : Promise.resolve({}),

        // Count all bookings (total, upcoming, completed)
        query.graph({
          entity: "order",
          filters: {
            customer_id: customerId
          },
          fields: ["id", "metadata"],
        }).then((response) => {
          console.log("Booking counts query response:", JSON.stringify(response, null, 2));
          if (response && response.data) {
            const allBookings = response.data.filter((order: any) => {
              // Must have hotel metadata to be considered a booking
              return order.metadata && order.metadata.hotel_id;
            });

            // Count upcoming trips (check-in date > today, date-only)
            const upcomingTrips = allBookings.filter((order: any) => {
              const checkInDate = toDateString(order.metadata?.check_in_date);
              return checkInDate && checkInDate > currentDate;
            });

            // Count completed trips (check-out date <= today, date-only)
            const completedTrips = allBookings.filter((order: any) => {
              const checkOutDate = toDateString(order.metadata?.check_out_date);
              return checkOutDate && checkOutDate <= currentDate;
            });

            // Count active trips (checked in but not checked out yet, date-only)
            const activeTrips = allBookings.filter((order: any) => {
              const checkInDate = toDateString(order.metadata?.check_in_date);
              const checkOutDate = toDateString(order.metadata?.check_out_date);
              return checkInDate && checkOutDate &&
                     checkInDate <= currentDate &&
                     checkOutDate >= currentDate;
            });

            // Count bookings with missing or invalid dates
            const invalidDateBookings = allBookings.filter((order: any) => {
              const checkInDate = toDateString(order.metadata?.check_in_date);
              const checkOutDate = toDateString(order.metadata?.check_out_date);
              return !checkInDate || !checkOutDate;
            });

            const counts = {
              total: allBookings.length,
              upcoming: upcomingTrips.length,
              completed: completedTrips.length,
              active: activeTrips.length,
              invalid_dates: invalidDateBookings.length
            };

            console.log("Booking counts with active separate:", counts);
            console.log("Math check - upcoming + completed + active + invalid:",
                       counts.upcoming + counts.completed + counts.active + counts.invalid_dates);

            return counts;
          }
          console.log("No orders found in response");
          return { total: 0, upcoming: 0, completed: 0, active: 0, invalid_dates: 0 };
        })
      ]);

      console.log("Final booking counts:", bookingCounts);

      // Fetch destination details after we have hotel details
      const destinationIds = [...new Set(Object.values(hotelDetails).map((hotel: any) => hotel.destination_id).filter(Boolean))];
      let destinationDetails = {};

      if (destinationIds.length > 0) {
        const { data: destinations } = await query.graph({
          entity: "destination",
          filters: { id: { $in: destinationIds } },
          fields: ["id", "name"],
        });

        if (destinations && destinations.length > 0) {
          destinations.forEach((destination: { id: string; name: string }) => {
            destinationDetails[destination.id] = destination.name;
          });
        }
      }

      console.log("Destination details:", destinationDetails);

      // Helper to normalize date to YYYY-MM-DD
      function toDateString(date: string | Date | undefined | null): string | null {
        if (!date) return null;
        try {
          return new Date(date).toISOString().split('T')[0];
        } catch {
          return null;
        }
      }

      // Transform orders to bookings with all details, then paginate after filtering
      let filteredOrders = allOrders.filter((order: any) => {
        // Must have hotel metadata
        if (!order.metadata || !order.metadata.hotel_id) return false;
        // In-memory filter for booking_status using date-only logic
        if (booking_status) {
          const checkIn = toDateString(order.metadata.check_in_date);
          const checkOut = toDateString(order.metadata.check_out_date);
          if (!checkIn || !checkOut) return false;
          if (booking_status === "upcoming") {
            return checkIn > currentDate;
          } else if (booking_status === "completed") {
            return checkOut <= currentDate;
          } else if (booking_status === "active") {
            return checkIn <= currentDate && checkOut >= currentDate;
          }
        }
        return true;
      });

      const count = filteredOrders.length;
      const paginatedOrders = filteredOrders.slice(offsetNum, offsetNum + limitNum);

      let bookingsWithDetails = paginatedOrders.map((order: any) => {
        const metadata = order.metadata || {};
        const customer = customerDetails[order.customer_id] || {};
        const travelers = metadata.travelers || {};
        const adults = travelers.adults || [];
        const children = travelers.children || [];
        const infants = travelers.infants || [];

        // Determine booking status (date-only logic)
        const checkInDate = toDateString(metadata.check_in_date);
        const checkOutDate = toDateString(metadata.check_out_date);
        let bookingStatus = "active";
        if (checkInDate && checkInDate > currentDate) {
          bookingStatus = "upcoming";
        } else if (checkOutDate && checkOutDate <= currentDate) {
          bookingStatus = "completed";
        }

        return {
          id: order.id,
          order_id: order.id,
          customer_id: order.customer_id,
          guest_email: order.email,
          guest_name: metadata.guest_name || "Guest",
          guest_phone: metadata.guest_phone,
          hotel_id: metadata.hotel_id,
          hotel_name: hotelDetails[metadata.hotel_id]?.name || "Unknown Hotel",
          hotel_location: hotelDetails[metadata.hotel_id]?.location || null,
          hotel_address: hotelDetails[metadata.hotel_id]?.address || null,
          hotel_city: hotelDetails[metadata.hotel_id]?.city || null,
          hotel_country: hotelDetails[metadata.hotel_id]?.country || null,
          hotel_postal_code: hotelDetails[metadata.hotel_id]?.postal_code || null,
          hotel_timezone: hotelDetails[metadata.hotel_id]?.timezone || null,
          destination_id: hotelDetails[metadata.hotel_id]?.destination_id || null,
          destination_name: hotelDetails[metadata.hotel_id]?.destination_id
            ? destinationDetails[hotelDetails[metadata.hotel_id].destination_id] || "Unknown Destination"
            : null,
          room_id: metadata.room_id,
          room_config_id: metadata.room_config_id,
          room_config_name: roomConfigDetails[metadata.room_config_id] || "Unknown Room Type",
          room_type: metadata.room_type || "Standard",
          check_in_date: metadata.check_in_date ? new Date(metadata.check_in_date) : null,
          check_out_date: metadata.check_out_date ? new Date(metadata.check_out_date) : null,
          check_in_time: metadata.check_in_time || "12:00",
          check_out_time: metadata.check_out_time || "12:00",
          number_of_guests: metadata.number_of_guests || 1,
          currency_code: order.currency_code,
          status: order.status,
          payment_status: order.payment_status,
          booking_status: bookingStatus,
          special_requests: metadata.special_requests,
          notes: metadata.notes,
          total_amount: order.total,
          created_at: order.created_at,
          updated_at: order.updated_at,
          guest_info: {
            primary_guest: {
              name: metadata.guest_name || "Guest",
              email: order.email,
              phone: metadata.guest_phone,
            },
            customer_account: {
              first_name: customer.first_name || null,
              last_name: customer.last_name || null,
              email: customer.email || null,
              phone: customer.phone || null,
              metadata: customer.metadata || null,
              addresses: customer.addresses || [],
            },
            preferred_contact: {
              full_name: customer.first_name && customer.last_name
                ? `${customer.first_name} ${customer.last_name}`
                : metadata.guest_name || "Guest",
              email: order.email || customer.email,
              phone: metadata.guest_phone || customer.phone,
            },
            travelers: {
              adults: adults.map((adult: any, index: number) => ({
                id: `adult_${index + 1}`,
                name: adult.name || `Adult ${index + 1}`,
                age: adult.age || null,
                type: "adult",
              })),
              children: children.map((child: any, index: number) => ({
                id: `child_${index + 1}`,
                name: child.name || `Child ${index + 1}`,
                age: child.age || null,
                type: "child",
              })),
              infants: infants.map((infant: any, index: number) => ({
                id: `infant_${index + 1}`,
                name: infant.name || `Infant ${index + 1}`,
                age: infant.age || null,
                type: "infant",
              })),
              total_count: adults.length + children.length + infants.length,
              summary: {
                adults: adults.length,
                children: children.length,
                infants: infants.length,
              },
            },
          },
          metadata: metadata,
        };
      });

      // Sort by metadata fields if needed
      if (sort_by === "check_in_date" || sort_by === "check_out_date") {
        bookingsWithDetails.sort((a, b) => {
          const dateA = a[sort_by] ? new Date(a[sort_by]).getTime() : 0;
          const dateB = b[sort_by] ? new Date(b[sort_by]).getTime() : 0;
          return sort_order === "desc" ? dateB - dateA : dateA - dateB;
        });
      }

      // Calculate simple pagination metadata
      const hasNextPage = offsetNum + limitNum < count;

      const endTime = Date.now();
      const responseTime = endTime - startTime;
      console.log(`✅ Bookings API completed in ${responseTime}ms`);

      console.log({bookingCounts});

      res.json({
        bookings: bookingsWithDetails,
        count: count,
        total_bookings_count: bookingCounts.total,
        upcoming_trips_count: bookingCounts.upcoming,
        active_trips_count: bookingCounts.active,
        completed_trips_count: bookingCounts.completed,
        limit: limitNum,
        offset: offsetNum,
        has_more: hasNextPage,
      });
    } catch (error) {
      console.error("Error fetching bookings:", error);
      return res.status(400).json({
        message: "Failed to fetch bookings",
        error: error instanceof Error ? error.message : "Unknown error",
      });
    }
  } catch (error) {
    console.error("Error in GET bookings:", error);
    res.status(400).json({
      message:
        error instanceof Error ? error.message : "Failed to get bookings",
    });
  }
}
