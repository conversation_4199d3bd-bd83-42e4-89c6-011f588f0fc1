import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import {
  ADD_ON_SERVICE,
  AddOnServiceLevel,
} from "../../../../../../modules/hotel-management/add-on-service";
import { registerAddOnServiceModule } from "../../../../../admin/hotel-management/add-on-services/register-module";

// Validation schema for destination add-ons query
export const StoreDestinationAddOnsSchema = z.object({
  currency_code: z.string().default("USD"),
  is_active: z.boolean().optional().default(true),
  limit: z.coerce.number().optional().default(50),
  offset: z.coerce.number().optional().default(0),
});

export type StoreDestinationAddOnsType = z.infer<
  typeof StoreDestinationAddOnsSchema
>;

/**
 * GET /store/hotel-management/destinations/[id]/add-ons
 *
 * Fetch available add-on services for a specific destination
 * This returns only destination-level add-ons
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Available destination-level add-on services
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const destinationId = req.params.id;
    const query = req.scope.resolve("query");

    // Validate query parameters
    const validationResult = StoreDestinationAddOnsSchema.safeParse(req.query);
    if (!validationResult.success) {
      return res.status(400).json({
        message: "Invalid query parameters",
        errors: validationResult.error.errors,
      });
    }

    const { currency_code, is_active, limit, offset } = validationResult.data;

    // Register the add-on service module
    registerAddOnServiceModule(req.scope);
    const addOnServiceService: any = req.scope.resolve(ADD_ON_SERVICE);

    // First, verify the destination exists
    const { data: destinations } = await query.graph({
      entity: "destination",
      filters: { id: destinationId },
      fields: ["id", "name"],
    });

    if (!destinations || destinations.length === 0) {
      return res.status(404).json({
        message: "Destination not found",
      });
    }

    const destination = destinations[0];

    // Get destination-level add-ons
    const [destinationAddOns] = await addOnServiceService.listAddOnServices(
      {
        service_level: AddOnServiceLevel.DESTINATION,
        destination_id: destinationId,
        is_active,
      },
      { skip: offset, take: limit },
      { currency_code } // Pass currency context
    );

    // Format add-ons for customer response
    const formattedAddOns = destinationAddOns
      .filter((addOn) => addOn.is_active !== false)
      .map((addOn) => ({
        id: addOn.id,
        name: addOn.name,
        description: addOn.description || "",
        service_type: addOn.type || "general",
        service_level: addOn.service_level,
        category_id: addOn.category_id || null,
        // Pricing information
        pricing_type: addOn.pricing_type || "per_person",
        adult_price: addOn.adult_price ? addOn.adult_price / 100 : 0,
        child_price: addOn.child_price ? addOn.child_price / 100 : 0,
        package_price: addOn.package_price ? addOn.package_price / 100 : null,
        // Usage-based pricing
        per_day_adult_price: addOn.per_day_adult_price
          ? addOn.per_day_adult_price / 100
          : 0,
        per_day_child_price: addOn.per_day_child_price
          ? addOn.per_day_child_price / 100
          : 0,
        currency_code: addOn.currency_code || currency_code,
        max_capacity: addOn.max_capacity,
        images: addOn.metadata?.images || [],
        destination_id: addOn.destination_id,
        destination_name: addOn.destination_name,
        // Variant IDs for frontend reference
        adult_variant_id: addOn.adult_variant_id,
        child_variant_id: addOn.child_variant_id,
        package_variant_id: addOn.package_variant_id,
      }));

    // Fetch all categories for filtering/display purposes
    const categoriesResult = await query.graph({
      entity: "lookup",
      filters: {
        entity_name: "add_on_category",
      },
      fields: ["*"],
    });

    const categories = categoriesResult.data
      .map((lookup: any) => {
        const categoryData = JSON.parse(lookup.value || "{}");
        return {
          id: lookup.id,
          name: categoryData.name || lookup.value,
          description: categoryData.description || "",
          is_active: categoryData.is_active === true,
        };
      })
      .filter((cat: any) => cat.is_active); // Only return active categories

    return res.json({
      add_ons: formattedAddOns,
      categories: categories,
      count: formattedAddOns.length,
      destination: {
        id: destination.id,
        name: destination.name,
      },
    });
  } catch (error) {
    console.error("Error fetching destination add-ons:", error);
    return res.status(500).json({
      message: "Failed to fetch destination add-on services",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
