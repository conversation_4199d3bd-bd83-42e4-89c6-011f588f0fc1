import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for room config extra beds query
export const StoreRoomConfigExtraBedsSchema = z.object({
  check_in: z.string().optional(),
  check_out: z.string().optional(),
  currency_code: z.string().default("USD"),
  include_seasonal: z.boolean().default(true),
});

export type StoreRoomConfigExtraBedsType = z.infer<
  typeof StoreRoomConfigExtraBedsSchema
>;

/**
 * Store endpoint to get extra bed, cot, and extra adults beyond capacity pricing for a specific room configuration
 *
 * This endpoint returns:
 * - Extra bed, cot, and extra adults beyond capacity availability for the room
 * - Base pricing for extra beds, cots, and extra adults beyond capacity
 * - Seasonal pricing (if dates provided and include_seasonal is true)
 * - Maximum extra beds, cots, and extra adults beyond capacity allowed
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Extra bed, cot, and extra adults beyond capacity pricing information for the room configuration
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;
    const query = req.scope.resolve("query");

    // Validate query parameters
    const {
      check_in,
      check_out,
      currency_code = "USD",
      include_seasonal = true,
    } = req.query;

    console.log(`Getting extra bed pricing for room config ${roomConfigId}`);
    console.log(`Raw query object:`, req.query);
    console.log(`Extracted query parameters:`, {
      check_in,
      check_out,
      currency_code,
      include_seasonal,
    });

    // Additional debugging for undefined dates
    if (check_in === undefined || check_out === undefined) {
      console.warn(`Missing date parameters:`, {
        check_in_type: typeof check_in,
        check_out_type: typeof check_out,
        check_in_value: check_in,
        check_out_value: check_out,
        all_query_keys: Object.keys(req.query),
      });
    }

    // Get room configuration information
    const { data: roomConfig } = await query.graph({
      entity: "product",
      filters: { id: roomConfigId },
      fields: [
        "id",
        "title",
        "handle",
        "description",
        "metadata",
        "categories",
      ],
    });

    if (!roomConfig || roomConfig.length === 0) {
      return res.status(404).json({
        message: "Room configuration not found",
        room_config_id: roomConfigId,
      });
    }

    const room = roomConfig[0];

    // Get hotel ID from room configuration
    // Try multiple methods to find the hotel ID
    let hotelId = null;

    // Method 1: Check categories (first category should be hotel)
    if (room.categories && room.categories.length > 0) {
      hotelId = room.categories[0];
    }

    // Method 2: Check metadata for hotel_id
    if (!hotelId && room.metadata?.hotel_id) {
      hotelId = room.metadata.hotel_id;
    }

    console.log(`Room configuration data:`, {
      id: room.id,
      categories: room.categories,
      metadata: room.metadata,
      resolved_hotel_id: hotelId,
    });

    if (!hotelId) {
      return res.status(400).json({
        message: "Could not determine hotel for this room configuration",
        room_config_id: roomConfigId,
        debug_info: {
          categories: room.categories,
          metadata_keys: room.metadata ? Object.keys(room.metadata) : [],
        },
      });
    }

    // Get hotel pricing service
    let hotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve pricing service",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get extra bed and cot occupancy configurations for this hotel
    const occupancyConfigs = await hotelPricingService.listOccupancyConfigs({
      hotel_id: hotelId,
    });

    const extraBedConfig = occupancyConfigs.find(
      (config: any) =>
        config.type === "EXTRA_BED" ||
        config.name?.toLowerCase().includes("extra bed")
    );

    const cotConfig = occupancyConfigs.find(
      (config: any) =>
        config.type === "COT" || config.name?.toLowerCase().includes("cot")
    );

    const extraAdultBeyondCapacityConfig = occupancyConfigs.find(
      (config: any) =>
        config.type === "EXTRA_ADULT_BEYOND_CAPACITY" ||
        config.name?.toLowerCase().includes("extra adult beyond capacity")
    );

    if (!extraBedConfig && !cotConfig && !extraAdultBeyondCapacityConfig) {
      return res.json({
        room_configuration: {
          id: room.id,
          name: room.title,
          handle: room.handle,
          description: room.description,
        },
        extra_beds_available: false,
        cots_available: false,
        extra_adults_beyond_capacity_available: false,
        message:
          "Extra beds, cots, and extra adults beyond capacity are not configured for this hotel",
        max_extra_beds: 0,
        max_cots: 0,
        max_adults_beyond_capacity: 0,
        pricing: null,
      });
    }

    console.log(
      `Found configurations - Extra Bed: ${extraBedConfig?.name || "None"} (${
        extraBedConfig?.id || "N/A"
      }), Cot: ${cotConfig?.name || "None"} (${
        cotConfig?.id || "N/A"
      }), Extra Adults Beyond Capacity: ${
        extraAdultBeyondCapacityConfig?.name || "None"
      } (${extraAdultBeyondCapacityConfig?.id || "N/A"})`
    );

    // Get max extra beds, cots, and extra adults beyond capacity from room config metadata
    const maxExtraBeds = Number(room.metadata?.max_extra_beds) || 0;
    const maxCots = Number(room.metadata?.max_cots) || 0;
    const maxAdultsBeyondCapacity =
      Number(room.metadata?.max_adults_beyond_capacity) || 0;

    // Get pricing rules for extra beds, cots, and extra adults beyond capacity
    let extraBedPricingRule = null;
    let cotPricingRule = null;
    let extraAdultBeyondCapacityPricingRule = null;

    if (extraBedConfig) {
      const extraBedPricingRules = await hotelPricingService.listBasePriceRules(
        {
          room_config_id: roomConfigId,
          occupancy_type_id: extraBedConfig.id,
          currency_code: currency_code,
        }
      );
      extraBedPricingRule = extraBedPricingRules[0]; // Should only be one rule per room config + occupancy type + currency
      if (extraBedPricingRule) {
        console.log(`Found extra bed pricing rule: ${extraBedPricingRule.id} for ${currency_code}`);
      } else {
        console.log(`No extra bed pricing rule found for ${currency_code}`);
      }
    }

    if (cotConfig) {
      const cotPricingRules = await hotelPricingService.listBasePriceRules({
        room_config_id: roomConfigId,
        occupancy_type_id: cotConfig.id,
        currency_code: currency_code,
      });
      cotPricingRule = cotPricingRules[0]; // Should only be one rule per room config + occupancy type + currency
      if (cotPricingRule) {
        console.log(`Found cot pricing rule: ${cotPricingRule.id} for ${currency_code}`);
      } else {
        console.log(`No cot pricing rule found for ${currency_code}`);
      }
    }

    if (extraAdultBeyondCapacityConfig) {
      const extraAdultBeyondCapacityPricingRules =
        await hotelPricingService.listBasePriceRules({
          room_config_id: roomConfigId,
          occupancy_type_id: extraAdultBeyondCapacityConfig.id,
          currency_code: currency_code,
        });
      extraAdultBeyondCapacityPricingRule =
        extraAdultBeyondCapacityPricingRules[0]; // Should only be one rule per room config + occupancy type + currency
      if (extraAdultBeyondCapacityPricingRule) {
        console.log(
          `Found extra adults beyond capacity pricing rule: ${extraAdultBeyondCapacityPricingRule.id} for ${currency_code}`
        );
      } else {
        console.log(`No extra adults beyond capacity pricing rule found for ${currency_code}`);
      }
    }

    if (
      !extraBedPricingRule &&
      !cotPricingRule &&
      !extraAdultBeyondCapacityPricingRule
    ) {
      return res.json({
        room_configuration: {
          id: room.id,
          name: room.title,
          handle: room.handle,
          description: room.description,
        },
        extra_beds_available: false,
        cots_available: false,
        extra_adults_beyond_capacity_available: false,
        message:
          "Extra bed, cot, and extra adults beyond capacity pricing is not configured for this room type",
        max_extra_beds: maxExtraBeds,
        max_cots: maxCots,
        max_adults_beyond_capacity: maxAdultsBeyondCapacity,
        pricing: null,
      });
    }

    // Helper function to build base pricing
    const buildBasePricing = (pricingRule: any) => {
      if (!pricingRule) return null;

      const weekdayPrices = {
        monday: (pricingRule.mon_price || pricingRule.amount || 0) / 100,
        tuesday: (pricingRule.tue_price || pricingRule.amount || 0) / 100,
        wednesday: (pricingRule.wed_price || pricingRule.amount || 0) / 100,
        thursday: (pricingRule.thu_price || pricingRule.amount || 0) / 100,
        friday: (pricingRule.fri_price || pricingRule.amount || 0) / 100,
        saturday: (pricingRule.sat_price || pricingRule.amount || 0) / 100,
        sunday: (pricingRule.sun_price || pricingRule.amount || 0) / 100,
      };

      return {
        rule_id: pricingRule.id,
        currency_code,
        weekday_prices: weekdayPrices,
        average_price:
          Object.values(weekdayPrices).reduce((sum, price) => sum + price, 0) /
          7,
      };
    };

    // Build base pricing for all types
    const extraBedBasePricing = buildBasePricing(extraBedPricingRule);
    const cotBasePricing = buildBasePricing(cotPricingRule);
    const extraAdultBeyondCapacityBasePricing = buildBasePricing(
      extraAdultBeyondCapacityPricingRule
    );

    let seasonalPricing = [];
    let calculatedExtraBedPricing = null;
    let calculatedCotPricing = null;
    let calculatedExtraAdultBeyondCapacityPricing = null;

    // Helper function to calculate pricing for specific dates
    const calculatePricingForDates = async (pricingRule: any, type: string) => {
      if (!check_in || !check_out || !pricingRule) return null;

      try {
        // Parse dates
        const checkInDate = new Date(check_in as string);
        const checkOutDate = new Date(check_out as string);

        if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
          throw new Error("Invalid date format");
        }

        if (checkInDate >= checkOutDate) {
          throw new Error("Check-in date must be before check-out date");
        }

        // Calculate number of nights
        const nights = Math.ceil(
          (checkOutDate.getTime() - checkInDate.getTime()) /
            (1000 * 60 * 60 * 24)
        );

        // Get seasonal pricing rules for this base rule
        const seasonalRules = await hotelPricingService.listSeasonalPriceRules({
          base_price_rule_id: pricingRule.id,
        });

        // Helper function to get weekday key
        const getWeekdayKey = (date: Date): string => {
          const days = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];
          return days[date.getDay()];
        };

        // Helper function to get price for a specific day
        const getDayPrice = (
          date: Date
        ): { price: number; source: string; rule_name?: string } => {
          const weekdayKey = getWeekdayKey(date);

          // Check if any seasonal rule applies to this date
          const applicableSeasonalRule = seasonalRules.find((rule) => {
            const seasonStart = new Date(rule.start_date);
            const seasonEnd = new Date(rule.end_date);
            const isInDateRange = date >= seasonStart && date <= seasonEnd;
            const isActive = rule.metadata?.is_active !== false; // Default to true if not specified
            const isApplicable = isInDateRange && isActive;

            if (isInDateRange) {
              console.log(
                `Checking seasonal rule for ${
                  date.toISOString().split("T")[0]
                }:`,
                {
                  rule_id: rule.id,
                  name: rule.name || rule.description,
                  start_date: rule.start_date,
                  end_date: rule.end_date,
                  is_active: isActive,
                  is_in_date_range: isInDateRange,
                  is_applicable: isApplicable,
                }
              );
            }

            return isApplicable;
          });

          if (applicableSeasonalRule) {
            // Seasonal prices are stored in metadata.weekday_prices
            const seasonalWeekdayPrices =
              applicableSeasonalRule.metadata?.weekday_prices;
            let seasonalPrice = null;

            if (seasonalWeekdayPrices) {
              // Try different weekday key formats
              seasonalPrice =
                seasonalWeekdayPrices[weekdayKey] || // "sun", "mon", etc.
                seasonalWeekdayPrices[weekdayKey.toLowerCase()] || // lowercase
                seasonalWeekdayPrices[weekdayKey.substring(0, 3)]; // first 3 chars

              console.log(`Seasonal weekday prices for ${weekdayKey}:`, {
                weekday_prices: seasonalWeekdayPrices,
                found_price: seasonalPrice,
                tried_keys: [
                  weekdayKey,
                  weekdayKey.toLowerCase(),
                  weekdayKey.substring(0, 3),
                ],
              });
            }

            // Fallback to base pricing if no seasonal price found
            if (seasonalPrice === null || seasonalPrice === undefined) {
              seasonalPrice =
                pricingRule[`${weekdayKey}_price`] || pricingRule.amount || 0;
            }

            return {
              price: seasonalPrice / 100,
              source:
                seasonalWeekdayPrices &&
                seasonalPrice !==
                  (pricingRule[`${weekdayKey}_price`] || pricingRule.amount)
                  ? "seasonal"
                  : "base",
              rule_name:
                applicableSeasonalRule.description ||
                applicableSeasonalRule.name,
            };
          }

          // Use base pricing
          const basePrice =
            pricingRule[`${weekdayKey}_price`] || pricingRule.amount || 0;
          return {
            price: basePrice / 100,
            source: "base",
          };
        };

        // Calculate day-by-day pricing for the stay period
        const dailyPrices = [];
        let totalPrice = 0;

        // Get all days in the stay period (excluding checkout day)
        const currentDate = new Date(checkInDate);
        while (currentDate < checkOutDate) {
          const dayPricing = getDayPrice(currentDate);
          dailyPrices.push({
            date: currentDate.toISOString().split("T")[0],
            day_of_week: currentDate.toLocaleDateString("en-US", {
              weekday: "long",
            }),
            price_per_unit: dayPricing.price,
            pricing_source: dayPricing.source,
            rule_name: dayPricing.rule_name,
          });

          totalPrice += dayPricing.price;
          currentDate.setDate(currentDate.getDate() + 1);
        }

        return {
          type,
          check_in,
          check_out,
          nights,
          currency_code,
          total_price_per_unit: totalPrice,
          average_price_per_night: totalPrice / nights,
          daily_breakdown: dailyPrices,
        };
      } catch (error) {
        console.error(`Error calculating ${type} pricing for dates:`, error);
        return null;
      }
    };

    // Calculate pricing for extra beds, cots, and extra adults beyond capacity if dates are provided
    if (check_in && check_out) {
      calculatedExtraBedPricing = await calculatePricingForDates(
        extraBedPricingRule,
        "extra_bed"
      );
      calculatedCotPricing = await calculatePricingForDates(
        cotPricingRule,
        "cot"
      );
      calculatedExtraAdultBeyondCapacityPricing =
        await calculatePricingForDates(
          extraAdultBeyondCapacityPricingRule,
          "extra_adult_beyond_capacity"
        );
    }

    // If dates are provided, return response with calculated pricing
    if (
      check_in &&
      check_out &&
      (calculatedExtraBedPricing ||
        calculatedCotPricing ||
        calculatedExtraAdultBeyondCapacityPricing)
    ) {
      const response = {
        room_configuration: {
          id: room.id,
          name: room.title,
          handle: room.handle,
          description: room.description,
        },
        extra_beds_available: maxExtraBeds > 0 && !!calculatedExtraBedPricing,
        cots_available: maxCots > 0 && !!calculatedCotPricing,
        extra_adults_beyond_capacity_available:
          maxAdultsBeyondCapacity > 0 &&
          !!calculatedExtraAdultBeyondCapacityPricing,
        max_extra_beds: maxExtraBeds,
        max_cots: maxCots,
        max_adults_beyond_capacity: maxAdultsBeyondCapacity,
        extra_bed_configuration: extraBedConfig
          ? {
              id: extraBedConfig.id,
              name: extraBedConfig.name,
              type: extraBedConfig.type,
              min_age: extraBedConfig.min_age,
              max_age: extraBedConfig.max_age,
            }
          : null,
        cot_configuration: cotConfig
          ? {
              id: cotConfig.id,
              name: cotConfig.name,
              type: cotConfig.type,
              min_age: cotConfig.min_age,
              max_age: cotConfig.max_age,
            }
          : null,
        extra_adults_beyond_capacity_configuration:
          extraAdultBeyondCapacityConfig
            ? {
                id: extraAdultBeyondCapacityConfig.id,
                name: extraAdultBeyondCapacityConfig.name,
                type: extraAdultBeyondCapacityConfig.type,
                min_age: extraAdultBeyondCapacityConfig.min_age,
                max_age: extraAdultBeyondCapacityConfig.max_age,
              }
            : null,
        extra_bed_pricing: calculatedExtraBedPricing
          ? {
              check_in: calculatedExtraBedPricing.check_in,
              check_out: calculatedExtraBedPricing.check_out,
              nights: calculatedExtraBedPricing.nights,
              currency_code: calculatedExtraBedPricing.currency_code,
              total_price_per_bed:
                calculatedExtraBedPricing.total_price_per_unit,
              price_per_bed_per_night:
                calculatedExtraBedPricing.average_price_per_night,
              daily_breakdown: calculatedExtraBedPricing.daily_breakdown,
            }
          : null,
        cot_pricing: calculatedCotPricing
          ? {
              check_in: calculatedCotPricing.check_in,
              check_out: calculatedCotPricing.check_out,
              nights: calculatedCotPricing.nights,
              currency_code: calculatedCotPricing.currency_code,
              total_price_per_cot: calculatedCotPricing.total_price_per_unit,
              price_per_cot_per_night:
                calculatedCotPricing.average_price_per_night,
              daily_breakdown: calculatedCotPricing.daily_breakdown,
            }
          : null,
        extra_adults_beyond_capacity_pricing:
          calculatedExtraAdultBeyondCapacityPricing
            ? {
                check_in: calculatedExtraAdultBeyondCapacityPricing.check_in,
                check_out: calculatedExtraAdultBeyondCapacityPricing.check_out,
                nights: calculatedExtraAdultBeyondCapacityPricing.nights,
                currency_code:
                  calculatedExtraAdultBeyondCapacityPricing.currency_code,
                total_price_per_adult:
                  calculatedExtraAdultBeyondCapacityPricing.total_price_per_unit,
                price_per_adult_per_night:
                  calculatedExtraAdultBeyondCapacityPricing.average_price_per_night,
                daily_breakdown:
                  calculatedExtraAdultBeyondCapacityPricing.daily_breakdown,
              }
            : null,
        // Legacy field for backward compatibility (extra bed pricing)
        pricing: calculatedExtraBedPricing
          ? {
              check_in: calculatedExtraBedPricing.check_in,
              check_out: calculatedExtraBedPricing.check_out,
              nights: calculatedExtraBedPricing.nights,
              currency_code: calculatedExtraBedPricing.currency_code,
              price_per_bed: calculatedExtraBedPricing.total_price_per_unit,
              price_per_night:
                calculatedExtraBedPricing.average_price_per_night,
            }
          : null,
        query_parameters: {
          check_in,
          check_out,
          currency_code,
          include_seasonal,
        },
      };

      console.log(
        `Returning calculated pricing for ${check_in} to ${check_out}:`,
        calculatedExtraBedPricing
          ? `Extra beds: ${calculatedExtraBedPricing.total_price_per_unit} ${currency_code} per bed`
          : "No extra bed pricing",
        calculatedCotPricing
          ? `Cots: ${calculatedCotPricing.total_price_per_unit} ${currency_code} per cot`
          : "No cot pricing"
      );
      return res.json(response);
    }

    // If no dates provided, return full pricing information
    const response = {
      room_configuration: {
        id: room.id,
        name: room.title,
        handle: room.handle,
        description: room.description,
      },
      extra_beds_available: maxExtraBeds > 0 && !!extraBedPricingRule,
      cots_available: maxCots > 0 && !!cotPricingRule,
      max_extra_beds: maxExtraBeds,
      max_cots: maxCots,
      extra_bed_configuration: extraBedConfig
        ? {
            id: extraBedConfig.id,
            name: extraBedConfig.name,
            type: extraBedConfig.type,
            min_age: extraBedConfig.min_age,
            max_age: extraBedConfig.max_age,
          }
        : null,
      cot_configuration: cotConfig
        ? {
            id: cotConfig.id,
            name: cotConfig.name,
            type: cotConfig.type,
            min_age: cotConfig.min_age,
            max_age: cotConfig.max_age,
          }
        : null,
      extra_bed_pricing: extraBedBasePricing
        ? {
            base: extraBedBasePricing,
            seasonal: seasonalPricing,
          }
        : null,
      cot_pricing: cotBasePricing
        ? {
            base: cotBasePricing,
            seasonal: [], // TODO: Add cot seasonal pricing if needed
          }
        : null,
      // Legacy field for backward compatibility
      pricing: extraBedBasePricing
        ? {
            base: extraBedBasePricing,
            seasonal: seasonalPricing,
          }
        : null,
      query_parameters: {
        check_in,
        check_out,
        currency_code,
        include_seasonal,
      },
    };

    console.log(`Returning extra bed and cot pricing for room ${room.title}`);

    return res.json(response);
  } catch (error) {
    console.error(
      "Error getting room config extra bed and cot pricing:",
      error
    );
    return res.status(500).json({
      message: "Error getting extra bed and cot pricing",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
