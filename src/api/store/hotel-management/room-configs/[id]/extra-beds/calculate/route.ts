import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../../modules/hotel-management/hotel-pricing";
import { eachDayOfInterval, format } from "date-fns";

// Validation schema for extra bed and cot cost calculation
export const StoreRoomConfigExtraBedsCalculateSchema = z.object({
  check_in: z.string(),
  check_out: z.string(),
  extra_beds: z.coerce.number().min(0).max(10).default(0),
  cots: z.coerce.number().min(0).max(5).default(0),
  currency_code: z.string().default("USD"),
});

export type StoreRoomConfigExtraBedsCalculateType = z.infer<
  typeof StoreRoomConfigExtraBedsCalculateSchema
>;

/**
 * Store endpoint to calculate the total cost of extra beds for a specific booking
 *
 * This endpoint calculates:
 * - Day-by-day extra bed costs
 * - Total extra bed cost for the stay
 * - Breakdown by base vs seasonal pricing
 * - Average cost per night per extra bed
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Detailed extra bed cost calculation
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const roomConfigId = req.params.id;
    const query = req.scope.resolve("query");

    // Validate query parameters
    const {
      check_in,
      check_out,
      extra_beds,
      cots,
      currency_code = "USD",
    } = req.query;

    if (!check_in || !check_out) {
      return res.status(400).json({
        message: "check_in and check_out dates are required",
      });
    }

    console.log(
      `Calculating extra bed and cot cost for room config ${roomConfigId}`
    );
    console.log(`Parameters:`, {
      check_in,
      check_out,
      extra_beds,
      cots,
      currency_code,
    });

    // Parse and validate dates
    const checkInDate = new Date(check_in as string);
    const checkOutDate = new Date(check_out as string);

    if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
      return res.status(400).json({
        message: "Invalid date format. Use ISO format (YYYY-MM-DD)",
      });
    }

    if (checkInDate >= checkOutDate) {
      return res.status(400).json({
        message: "check_in date must be before check_out date",
      });
    }

    // Calculate number of nights
    const nights = Math.ceil(
      (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    // Get room configuration information
    const { data: roomConfig } = await query.graph({
      entity: "product",
      filters: { id: roomConfigId },
      fields: [
        "id",
        "title",
        "handle",
        "description",
        "metadata",
        "categories",
      ],
    });

    if (!roomConfig || roomConfig.length === 0) {
      return res.status(404).json({
        message: "Room configuration not found",
        room_config_id: roomConfigId,
      });
    }

    const room = roomConfig[0];

    // Get hotel ID from categories
    const hotelId = room.categories?.[0];
    if (!hotelId) {
      return res.status(400).json({
        message: "Could not determine hotel for this room configuration",
        room_config_id: roomConfigId,
      });
    }

    // Check max extra beds and cots limits
    const maxExtraBeds = Number(room.metadata?.max_extra_beds) || 0;
    const maxCots = Number(room.metadata?.max_cots) || 0;

    if (Number(extra_beds) > maxExtraBeds) {
      return res.status(400).json({
        message: `Maximum ${maxExtraBeds} extra beds allowed for this room type. Requested: ${extra_beds}`,
        max_extra_beds: maxExtraBeds,
        requested: Number(extra_beds),
      });
    }

    if (Number(cots) > maxCots) {
      return res.status(400).json({
        message: `Maximum ${maxCots} cots allowed for this room type. Requested: ${cots}`,
        max_cots: maxCots,
        requested: Number(cots),
      });
    }

    // Get hotel pricing service
    let hotelPricingService: any;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve pricing service",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get extra bed occupancy configuration
    const occupancyConfigs = await hotelPricingService.listOccupancyConfigs({
      hotel_id: hotelId,
    });

    const extraBedConfig = occupancyConfigs.find(
      (config: any) =>
        config.type === "EXTRA_BED" ||
        config.name?.toLowerCase().includes("extra bed")
    );

    if (!extraBedConfig) {
      return res.status(400).json({
        message: "Extra beds are not configured for this hotel",
        room_config_id: roomConfigId,
      });
    }

    // Get extra bed pricing rule for the specified currency
    const extraBedPricingRules = await hotelPricingService.listBasePriceRules({
      room_config_id: roomConfigId,
      occupancy_type_id: extraBedConfig.id,
      currency_code: currency_code,
    });

    const pricingRule = extraBedPricingRules[0];

    if (!pricingRule) {
      return res.status(400).json({
        message: "Extra bed pricing is not configured for this room type",
        room_config_id: roomConfigId,
      });
    }

    // Get seasonal pricing rules
    const seasonalRules = await hotelPricingService.listSeasonalPriceRules({
      base_price_rule_id: pricingRule.id,
    });

    // Helper function to get weekday key
    const getWeekdayKey = (date: Date): string => {
      const days = ["sun", "mon", "tue", "wed", "thu", "fri", "sat"];
      return days[date.getDay()];
    };

    // Helper function to get price for a specific day
    const getDayPrice = (
      date: Date
    ): {
      price: number;
      source: string;
      rule_id?: string;
      rule_name?: string;
    } => {
      const weekdayKey = getWeekdayKey(date);

      // Check if any seasonal rule applies to this date
      const applicableSeasonalRule = seasonalRules.find((rule: any) => {
        const seasonStart = new Date(rule.start_date);
        const seasonEnd = new Date(rule.end_date);
        return date >= seasonStart && date <= seasonEnd;
      });

      if (applicableSeasonalRule) {
        const seasonalPrice =
          applicableSeasonalRule.weekday_prices?.[weekdayKey] ||
          pricingRule[`${weekdayKey}_price`] ||
          pricingRule.amount ||
          0;
        return {
          price: seasonalPrice / 100,
          source: "seasonal",
          rule_id: applicableSeasonalRule.id,
          rule_name: applicableSeasonalRule.name,
        };
      }

      // Use base pricing
      const basePrice =
        pricingRule[`${weekdayKey}_price`] || pricingRule.amount || 0;
      return {
        price: basePrice / 100,
        source: "base",
        rule_id: pricingRule.id,
      };
    };

    // Calculate day-by-day costs
    const dailyBreakdown = [];
    let totalCost = 0;
    let basePricingDays = 0;
    let seasonalPricingDays = 0;

    // Get all days in the stay period
    const stayDays = eachDayOfInterval({
      start: checkInDate,
      end: new Date(checkOutDate.getTime() - 24 * 60 * 60 * 1000), // Exclude checkout day
    });

    for (const day of stayDays) {
      const dayPricing = getDayPrice(day);
      const dayTotal = dayPricing.price * Number(extra_beds);

      dailyBreakdown.push({
        date: format(day, "yyyy-MM-dd"),
        day_of_week: format(day, "EEEE"),
        price_per_bed: dayPricing.price,
        extra_beds_count: Number(extra_beds),
        day_total: dayTotal,
        pricing_source: dayPricing.source,
        rule_id: dayPricing.rule_id,
        rule_name: dayPricing.rule_name,
        currency_code,
      });

      totalCost += dayTotal;

      if (dayPricing.source === "base") {
        basePricingDays++;
      } else {
        seasonalPricingDays++;
      }
    }

    const response = {
      room_configuration: {
        id: room.id,
        name: room.title,
        handle: room.handle,
      },
      booking_details: {
        check_in,
        check_out,
        nights,
        extra_beds: Number(extra_beds),
        currency_code,
      },
      cost_summary: {
        total_cost: totalCost,
        cost_per_night: totalCost / nights,
        cost_per_bed_per_night: totalCost / (nights * Number(extra_beds)),
        currency_code,
        base_pricing_days: basePricingDays,
        seasonal_pricing_days: seasonalPricingDays,
      },
      daily_breakdown: dailyBreakdown,
      extra_bed_configuration: {
        id: extraBedConfig.id,
        name: extraBedConfig.name,
        type: extraBedConfig.type,
      },
      pricing_rule: {
        id: pricingRule.id,
        base_weekday_prices: {
          monday: (pricingRule.mon_price || pricingRule.amount || 0) / 100,
          tuesday: (pricingRule.tue_price || pricingRule.amount || 0) / 100,
          wednesday: (pricingRule.wed_price || pricingRule.amount || 0) / 100,
          thursday: (pricingRule.thu_price || pricingRule.amount || 0) / 100,
          friday: (pricingRule.fri_price || pricingRule.amount || 0) / 100,
          saturday: (pricingRule.sat_price || pricingRule.amount || 0) / 100,
          sunday: (pricingRule.sun_price || pricingRule.amount || 0) / 100,
        },
      },
    };

    console.log(
      `Calculated total extra bed cost: ${totalCost} ${currency_code} for ${extra_beds} beds over ${nights} nights`
    );

    return res.json(response);
  } catch (error) {
    console.error("Error calculating extra bed cost:", error);
    return res.status(500).json({
      message: "Error calculating extra bed cost",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
