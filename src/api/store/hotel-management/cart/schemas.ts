import { z } from "zod";

// Schema for individual traveler/guest details
export const TravelerSchema = z.object({
  name: z.string().min(1, "Name is required"),
  age: z.number().optional(), // Age is optional for adults, required for children/infants
});

// Schema for travelers metadata
export const TravelersMetadataSchema = z.object({
  adults: z.array(TravelerSchema).default([]),
  children: z.array(TravelerSchema).default([]),
  infants: z.array(TravelerSchema).default([]),
});

// Schema for add-on selection
export const AddOnSelectionSchema = z
  .object({
    service_id: z.string().min(1, "Add-on service ID is required"),
    pricing_type: z
      .enum(["per_person", "package", "usage_based"])
      .default("per_person"),

    // Per-person pricing fields
    adult_quantity: z
      .number()
      .min(0, "Adult quantity must be 0 or greater")
      .default(0),
    child_quantity: z
      .number()
      .min(0, "Child quantity must be 0 or greater")
      .default(0),

    // Package pricing fields
    package_quantity: z
      .number()
      .min(0, "Package quantity must be 0 or greater")
      .default(0),
    total_occupancy: z
      .number()
      .min(0, "Total occupancy must be 0 or greater")
      .optional(),

    // Usage-based pricing fields
    per_day_adult_price: z.number().min(0).optional(),
    per_day_child_price: z.number().min(0).optional(),

    // Per-guest usage selection
    guest_usage: z
      .array(
        z.object({
          guest_type: z.enum(["adult", "child"]),
          guest_index: z.number().min(0), // Index in travelers array
          usage_dates: z.array(
            z
              .string()
              .regex(
                /^\d{4}-\d{2}-\d{2}$/,
                "Usage dates must be in YYYY-MM-DD format"
              )
          ),
        })
      )
      .optional(),
  })
  .refine(
    (data) => {
      if (data.pricing_type === "package") {
        return data.package_quantity > 0;
      } else if (data.pricing_type === "usage_based") {
        // For usage-based pricing, guest_usage must be provided
        const hasGuestUsage = data.guest_usage && data.guest_usage.length > 0;
        const hasQuantities =
          data.adult_quantity > 0 || data.child_quantity > 0;

        return hasGuestUsage && hasQuantities;
      } else {
        return data.adult_quantity > 0 || data.child_quantity > 0;
      }
    },
    {
      message:
        "For per-person pricing, at least one adult or child quantity must be greater than 0. For package pricing, package quantity must be greater than 0. For usage-based pricing, guest_usage must be provided along with quantities.",
    }
  );

// Main cart creation schema
export const StoreCartCreationSchema = z
  .object({
    hotel_id: z.string().min(1, "Hotel ID is required"),
    room_id: z.string().optional(),
    room_config_id: z.string().min(1, "Room configuration ID is required"),
    room_type: z.string().optional(),
    check_in_date: z.string().min(1, "Check-in date is required"),
    check_out_date: z.string().min(1, "Check-out date is required"),
    check_in_time: z.string().default("12:00"),
    check_out_time: z.string().default("12:00"),

    // Primary guest information (backward compatibility)
    guest_name: z.string().optional(),
    guest_email: z.string().email("Valid email is required"),
    guest_phone: z.string().optional(),

    // Guest counts
    adults: z.number().min(1, "At least 1 adult is required").default(1),
    children: z.number().min(0).default(0),
    infants: z.number().min(0).default(0),
    extra_beds: z.number().min(0, "Extra beds must be non-negative").default(0),
    cots: z.number().min(0, "Cots must be non-negative").default(0),
    extra_adults_beyond_capacity: z
      .number()
      .min(0, "Extra adults beyond capacity must be non-negative")
      .default(0),

    // Multiple guest details (new feature)
    travelers: TravelersMetadataSchema.optional(),

    // Booking details
    number_of_rooms: z
      .number()
      .min(1, "At least 1 room is required")
      .default(1),
    total_amount: z.number().min(0, "Total amount must be positive"),
    currency_code: z.string().min(1, "Currency code is required"),
    region_id: z.string().min(1, "Region ID is required"),
    country_code: z.string().optional(),

    // Additional information
    special_requests: z.string().optional(),
    notes: z.string().optional(),
    metadata: z.record(z.any()).optional(),

    // Add-on services
    add_ons: z.array(AddOnSelectionSchema).optional().default([]),

    // Address information
    shipping_address: z
      .object({
        first_name: z.string().optional(),
        last_name: z.string().optional(),
        address_1: z.string().optional(),
        city: z.string().optional(),
        country_code: z.string().optional(),
        postal_code: z.string().optional(),
        phone: z.string().optional(),
      })
      .optional(),
    billing_address: z
      .object({
        first_name: z.string().optional(),
        last_name: z.string().optional(),
        address_1: z.string().optional(),
        city: z.string().optional(),
        country_code: z.string().optional(),
        postal_code: z.string().optional(),
        phone: z.string().optional(),
      })
      .optional(),

    // Customer information
    customer_id: z.string().optional(),
    sales_channel_id: z.string().optional(),
  })
  .refine(
    (data) => {
      // If travelers are provided, validate that the counts match
      // Note: guest_name/guest_email represents one adult traveler for backward compatibility
      if (data.travelers) {
        const adultsCount = data.travelers.adults?.length || 0;
        const childrenCount = data.travelers.children?.length || 0;
        const infantsCount = data.travelers.infants?.length || 0;

        // guest_name/guest_email counts as 1 adult, so travelers.adults should be (total adults - 1)
        const expectedAdultsInTravelers = Math.max(0, data.adults - 1);

        return (
          adultsCount === expectedAdultsInTravelers &&
          childrenCount === data.children &&
          infantsCount === data.infants
        );
      }
      return true;
    },
    {
      message:
        "Traveler counts must match the number of traveler details provided. Note: guest_name/guest_email represents one adult traveler.",
    }
  )
  .refine(
    (data) => {
      // Validate usage dates for usage-based add-ons fall within booking period
      if (data.add_ons && data.add_ons.length > 0) {
        const checkInDate = new Date(data.check_in_date);
        const checkOutDate = new Date(data.check_out_date);

        // Adjust checkout date to exclude the checkout day (guests check out, so service not used that day)
        const lastUsageDate = new Date(checkOutDate);
        lastUsageDate.setDate(lastUsageDate.getDate() - 1);

        for (const addon of data.add_ons) {
          if (addon.pricing_type === "usage_based") {
            // Validate guest_usage dates
            if (addon.guest_usage) {
              for (const guestUsage of addon.guest_usage) {
                for (const usageDate of guestUsage.usage_dates) {
                  const date = new Date(usageDate);
                  if (date < checkInDate || date > lastUsageDate) {
                    return false;
                  }
                }
              }
            }
          }
        }
      }
      return true;
    },
    {
      message:
        "Usage dates for add-ons must fall within the booking period (check-in date to day before check-out date).",
    }
  );

export type StoreCartCreationType = z.infer<typeof StoreCartCreationSchema>;
export type TravelerType = z.infer<typeof TravelerSchema>;
export type TravelersMetadataType = z.infer<typeof TravelersMetadataSchema>;
export type AddOnSelectionType = z.infer<typeof AddOnSelectionSchema>;
