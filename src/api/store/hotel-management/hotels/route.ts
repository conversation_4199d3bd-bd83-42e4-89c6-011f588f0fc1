import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

/**
 * Public endpoint to list hotels with filtering support
 *
 * @param req - The request object
 * @param res - The response object
 * @returns A list of hotels with filtering applied
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const query = req.scope.resolve(ContainerRegistrationKeys.QUERY);
    const {
      limit = 20,
      offset = 0,
      is_featured,
      is_active,
      is_pets_allowed,
      searchParams,
      tags, // Direct query parameter for tags
      destination_id, // Direct query parameter for destination
    } = req.query || {};

    const filters: Record<string, any> = {};
    const params =
      searchParams && typeof searchParams === "string"
        ? JSON.parse(searchParams)
        : null;

    // Add basic filters
    if (is_featured !== undefined) {
      filters.is_featured = is_featured === "true";
    }

    filters.is_active = true;
    
    if (is_pets_allowed !== undefined) {
      filters.is_pets_allowed = is_pets_allowed === "true";
    }

    // Add destination filter (direct parameter or from searchParams)
    if (destination_id) {
      filters.destination_id = destination_id;
    } else if (params && params?.destination_id) {
      filters.destination_id = params?.destination_id;
    }

    // Handle tags filtering (multiple ways to provide tags)
    let tagsToFilter: string[] = [];
    let uniqueTags: string[] = [];

    // Method 1: Direct tags parameter (comma-separated string)
    if (tags && typeof tags === "string") {
      tagsToFilter = tags
        .split(",")
        .map((tag: string) => tag.trim())
        .filter(Boolean);
    }

    // Method 2: Tags in searchParams (array or string)
    if (params && params?.tags) {
      if (Array.isArray(params.tags)) {
        tagsToFilter = [...tagsToFilter, ...params.tags];
      } else if (typeof params.tags === "string") {
        const tagsArray = params.tags
          .split(",")
          .map((tag: string) => tag.trim())
          .filter(Boolean);
        tagsToFilter = [...tagsToFilter, ...tagsArray];
      }
    }

    // Apply tags filter if we have any tags to filter by
    if (tagsToFilter.length > 0) {
      // Remove duplicates
      uniqueTags = [...new Set(tagsToFilter)];
    }
    let hotels: any, count: number, take: number, skip: number;

    if (tagsToFilter.length > 0) {
      // Use JavaScript post-filtering for tags since database filters don't work with JSON arrays
      const { data: allHotels } = await query.graph({
        entity: "hotel",
        filters,
        fields: [
          "id",
          "name",
          "handle",
          "category_id",
          "description",
          "rating",
          "address",
          "location",
          "is_featured",
          "is_active",
          "is_pets_allowed",
          "destination_id",
          "amenities",
          "rules",
          "safety_measures",
          "tags",
          "images.*",
          ...(req.validatedQuery?.fields?.split(",") || []),
        ],
        pagination: {
          skip: 0,
          take: 1000, // Get more hotels to filter from
        },
      });

      // Filter hotels by tags in JavaScript
      const filteredHotels =
        allHotels?.filter((hotel) => {
          if (!hotel.tags || !Array.isArray(hotel.tags)) {
            return false;
          }
          // Check if hotel has any of the requested tags
          return uniqueTags.some((tag: string) =>
            (hotel.tags as unknown as string[]).includes(tag)
          );
        }) || [];

      // Apply pagination to filtered results
      const startIndex = Number(offset);
      const endIndex = startIndex + Number(limit);
      hotels = filteredHotels.slice(startIndex, endIndex);
      count = filteredHotels.length;
      take = Number(limit);
      skip = Number(offset);
    } else {
      // No tags filtering, use normal query
      const { data: queryHotels, metadata: queryMetadata } = await query.graph({
        entity: "hotel",
        filters,
        fields: [
          "id",
          "name",
          "handle",
          "category_id",
          "description",
          "rating",
          "address",
          "location",
          "is_featured",
          "is_active",
          "is_pets_allowed",
          "destination_id",
          "amenities",
          "rules",
          "safety_measures",
          "tags",
          "images.*",
          ...(req.validatedQuery?.fields?.split(",") || []),
        ],
        pagination: {
          skip: Number(offset),
          take: Number(limit),
        },
      });

      hotels = queryHotels;
      count = queryMetadata.count;
      take = queryMetadata.take;
      skip = queryMetadata.skip;
    }

    res.json({
      hotels,
      count,
      limit: take,
      offset: skip,
    });
  } catch (error) {
    console.error("Error listing hotels:", error);
    return res.status(500).json({
      message: "Error listing hotels",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
