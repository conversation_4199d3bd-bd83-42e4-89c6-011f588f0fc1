import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { checkHotelAvailabilityWorkflow } from "src/workflows/hotel-management";

// Validation schema for availability check
export const StoreHotelAvailabilitySchema = z.object({
  check_in: z.string(),
  check_out: z.string(),
  adults: z.number().default(1),
  children: z.number().default(0),
  infants: z.number().default(0),

  currency_code: z.string().default("USD"),
  country_code: z.string().optional().default("CH"), // Country code for tax calculation
  province_code: z.string().optional(), // Province/state code for tax calculation
  // Additional options for detailed information
  include_price_tiers: z.boolean().optional().default(false),
  include_unavailable: z.boolean().optional().default(false),
  // New parameters
  child_ages: z
    .array(z.object({ age: z.union([z.number(), z.string()]) }))
    .optional(), // Array of child ages
  sales_channel_id: z.string().optional(), // Sales channel ID for channel-specific pricing
});

export type StoreHotelAvailabilityType = z.infer<
  typeof StoreHotelAvailabilitySchema
>;

/**
 * Endpoint to check availability and calculate prices for all room configurations in a hotel
 *
 * This endpoint supports the following options:
 * - include_price_tiers: Include price tiers for different occupancy types and meal plans
 * - include_unavailable: Include unavailable rooms in the response
 *
 * This endpoint uses the Medusa workflow system for better organization and reusability
 *
 * @param req - The request object
 * @param res - The response object
 * @returns A list of available room configurations with their prices
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;

    // Extract query parameters
    const {
      check_in,
      check_out,
      adults = 1,
      children = 0,
      infants = 0,

      currency_code = "USD",
      country_code = "US",
      province_code,
      include_price_tiers = false,
      include_unavailable = false,
      child_ages,
      sales_channel_id,
    } = req.query;

    console.log(`Checking availability for hotel ${hotelId}`);
    console.log(`Query parameters:`, {
      check_in,
      check_out,
      adults,
      children,
      infants,

      currency_code,
      country_code,
      province_code,
      include_price_tiers,
      include_unavailable,
      child_ages,
      sales_channel_id,
    });

    // Parse child_ages from query parameter if it exists
    let parsedChildAges: Array<{ age: number | string }> = [];
    if (child_ages) {
      try {
        // If child_ages is a string, try to parse it as JSON
        if (typeof child_ages === "string") {
          const parsed = JSON.parse(child_ages);
          if (Array.isArray(parsed)) {
            parsedChildAges = parsed.map((item) => {
              if (typeof item === "object" && item !== null && "age" in item) {
                return { age: item.age };
              }
              return { age: String(item) };
            });
          }
        }
      } catch (error) {
        console.error("Error parsing child_ages:", error);
        // If parsing fails, keep as empty array
      }
    }

    // Run the workflow
    const { result } = await checkHotelAvailabilityWorkflow(req.scope).run({
      input: {
        hotel_id: hotelId,
        check_in: check_in as string,
        check_out: check_out as string,
        adults: Number(adults),
        children: Number(children),
        infants: Number(infants),

        currency_code: currency_code as string,
        country_code: country_code as string,
        province_code: province_code as string,
        include_price_tiers: Boolean(include_price_tiers),
        include_unavailable: Boolean(include_unavailable),
        child_ages: parsedChildAges,
        sales_channel_id: sales_channel_id as string,
      },
    });

    // Return the workflow result
    return res.json(result);
  } catch (error) {
    console.error("Error checking hotel availability:", error);
    return res.status(500).json({
      message: "Error checking hotel availability",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
