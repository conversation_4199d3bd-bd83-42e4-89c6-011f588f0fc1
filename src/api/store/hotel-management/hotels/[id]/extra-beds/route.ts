import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { z } from "zod";
import { HOTEL_PRICING_MODULE } from "../../../../../../modules/hotel-management/hotel-pricing";

// Validation schema for extra beds query
export const StoreHotelExtraBedsSchema = z.object({
  check_in: z.string().optional(),
  check_out: z.string().optional(),
  currency_code: z.string().default("USD"),
  room_config_id: z.string().optional(), // Specific room configuration
});

export type StoreHotelExtraBedsType = z.infer<typeof StoreHotelExtraBedsSchema>;

/**
 * Store endpoint to get extra bed and cot information and pricing for a hotel
 *
 * This endpoint returns:
 * - Extra bed and cot availability per room configuration
 * - Extra bed and cot pricing (base and seasonal if dates provided)
 * - Maximum extra beds and cots allowed per room type
 * - Configuration details for extra beds and cots
 *
 * @param req - The request object
 * @param res - The response object
 * @returns Extra bed and cot information and pricing
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const hotelId = req.params.id;
    const query = req.scope.resolve("query");

    // Validate query parameters
    const {
      check_in,
      check_out,
      currency_code = "USD",
      room_config_id,
    } = req.query;

    console.log(`Getting extra bed and cot information for hotel ${hotelId}`);
    console.log(`Query parameters:`, {
      check_in,
      check_out,
      currency_code,
      room_config_id,
    });

    // Get hotel information
    const { data: hotel } = await query.graph({
      entity: "hotel",
      filters: { id: hotelId },
      fields: ["id", "name", "handle", "description"],
    });

    if (!hotel || hotel.length === 0) {
      return res.status(404).json({
        message: "Hotel not found",
        hotel_id: hotelId,
      });
    }

    // Get hotel pricing service
    let hotelPricingService;
    try {
      hotelPricingService = req.scope.resolve(HOTEL_PRICING_MODULE);
    } catch (error) {
      console.error("Failed to resolve hotelPricingService:", error);
      return res.status(500).json({
        message: "Internal server error: Could not resolve pricing service",
        error: error instanceof Error ? error.message : String(error),
      });
    }

    // Get extra bed and cot occupancy configurations
    const occupancyConfigs = await hotelPricingService.listOccupancyConfigs({
      hotel_id: hotelId,
    });

    const extraBedConfig = occupancyConfigs.find(
      (config) =>
        config.type === "EXTRA_BED" ||
        config.name?.toLowerCase().includes("extra bed")
    );

    const cotConfig = occupancyConfigs.find(
      (config) =>
        config.type === "COT" || config.name?.toLowerCase().includes("cot")
    );

    if (!extraBedConfig && !cotConfig) {
      return res.json({
        hotel: hotel[0],
        extra_beds_available: false,
        cots_available: false,
        message: "Extra beds and cots are not configured for this hotel",
        room_configurations: [],
      });
    }

    console.log(
      `Found configurations - Extra Bed: ${extraBedConfig?.name || "None"} (${
        extraBedConfig?.id || "N/A"
      }), Cot: ${cotConfig?.name || "None"} (${
        cotConfig?.id || "N/A"
      })`
    );

    // Get room configurations for this hotel
    let roomConfigFilters: any = {
      categories: [hotelId],
      metadata: {
        price_set_id: { exists: true },
      },
    };

    // If specific room config requested, filter for it
    if (room_config_id) {
      roomConfigFilters.id = room_config_id;
    }

    const { data: roomConfigs } = await query.graph({
      entity: "product",
      filters: roomConfigFilters,
      fields: ["id", "title", "handle", "description", "metadata"],
    });

    if (!roomConfigs || roomConfigs.length === 0) {
      return res.json({
        hotel: hotel[0],
        extra_beds_available: false,
        message: "No room configurations found for this hotel",
        room_configurations: [],
      });
    }

    console.log(`Found ${roomConfigs.length} room configurations`);

    // Get pricing rules for extra beds and cots
    let extraBedPricingRules: any[] = [];
    let cotPricingRules: any[] = [];

    if (extraBedConfig) {
      extraBedPricingRules = await hotelPricingService.listBasePriceRules({
        room_config_id: roomConfigs.map((rc) => rc.id),
        occupancy_type_id: extraBedConfig.id,
        currency_code: currency_code,
      });
      console.log(
        `Found ${extraBedPricingRules.length} extra bed pricing rules for ${currency_code}`
      );
    }

    if (cotConfig) {
      cotPricingRules = await hotelPricingService.listBasePriceRules({
        room_config_id: roomConfigs.map((rc) => rc.id),
        occupancy_type_id: cotConfig.id,
        currency_code: currency_code,
      });
      console.log(`Found ${cotPricingRules.length} cot pricing rules for ${currency_code}`);
    }

    // Process each room configuration
    const roomConfigurationsWithExtraBeds = await Promise.all(
      roomConfigs.map(async (roomConfig) => {
        try {
          // Get max extra beds and cots from room config metadata
          const maxExtraBeds = roomConfig.metadata?.max_extra_beds || 0;
          const maxCots = roomConfig.metadata?.max_cots || 0;

          // Find pricing rules for this room config
          const extraBedPricingRule = extraBedPricingRules.find(
            (rule) => rule.room_config_id === roomConfig.id
          );

          const cotPricingRule = cotPricingRules.find(
            (rule) => rule.room_config_id === roomConfig.id
          );

          // Helper function to create pricing object
          const createPricingObject = (pricingRule: any) => {
            if (!pricingRule) return null;

            const basePricing = {
              currency_code,
              weekday_prices: {
                monday:
                  (pricingRule.mon_price || pricingRule.amount || 0) / 100,
                tuesday:
                  (pricingRule.tue_price || pricingRule.amount || 0) / 100,
                wednesday:
                  (pricingRule.wed_price || pricingRule.amount || 0) / 100,
                thursday:
                  (pricingRule.thu_price || pricingRule.amount || 0) / 100,
                friday:
                  (pricingRule.fri_price || pricingRule.amount || 0) / 100,
                saturday:
                  (pricingRule.sat_price || pricingRule.amount || 0) / 100,
                sunday:
                  (pricingRule.sun_price || pricingRule.amount || 0) / 100,
              },
            };

            return {
              base: basePricing,
              seasonal: [], // Will be populated if dates are provided
            };
          };

          let extraBedPricing = createPricingObject(extraBedPricingRule);
          let cotPricing = createPricingObject(cotPricingRule);

          // Helper function to add seasonal pricing
          const addSeasonalPricing = async (
            pricing: any,
            pricingRule: any,
            type: string
          ) => {
            if (!pricing || !pricingRule || !check_in || !check_out) return;

            try {
              const seasonalRules =
                await hotelPricingService.listSeasonalPriceRules({
                  base_price_rule_id: pricingRule.id,
                });

              // Filter seasonal rules that overlap with the requested dates
              const checkInDate = new Date(check_in as string);
              const checkOutDate = new Date(check_out as string);

              const applicableSeasonalRules = seasonalRules.filter((rule) => {
                const seasonStart = new Date(rule.start_date);
                const seasonEnd = new Date(rule.end_date);

                // Check if the requested dates overlap with this seasonal period
                return (
                  (checkInDate >= seasonStart && checkInDate <= seasonEnd) ||
                  (checkOutDate >= seasonStart && checkOutDate <= seasonEnd) ||
                  (checkInDate <= seasonStart && checkOutDate >= seasonEnd)
                );
              });

              pricing.seasonal = applicableSeasonalRules.map((rule) => ({
                id: rule.id,
                name: rule.name,
                start_date: rule.start_date,
                end_date: rule.end_date,
                currency_code,
                weekday_prices: {
                  monday:
                    (rule.weekday_prices?.mon ||
                      pricing.base.weekday_prices.monday * 100) / 100,
                  tuesday:
                    (rule.weekday_prices?.tue ||
                      pricing.base.weekday_prices.tuesday * 100) / 100,
                  wednesday:
                    (rule.weekday_prices?.wed ||
                      pricing.base.weekday_prices.wednesday * 100) / 100,
                  thursday:
                    (rule.weekday_prices?.thu ||
                      pricing.base.weekday_prices.thursday * 100) / 100,
                  friday:
                    (rule.weekday_prices?.fri ||
                      pricing.base.weekday_prices.friday * 100) / 100,
                  saturday:
                    (rule.weekday_prices?.sat ||
                      pricing.base.weekday_prices.saturday * 100) / 100,
                  sunday:
                    (rule.weekday_prices?.sun ||
                      pricing.base.weekday_prices.sunday * 100) / 100,
                },
              }));

              console.log(
                `Found ${pricing.seasonal.length} applicable seasonal ${type} rules for room ${roomConfig.id}`
              );
            } catch (seasonalError) {
              console.error(
                `Error fetching seasonal ${type} pricing for room ${roomConfig.id}:`,
                seasonalError
              );
              // Continue with base pricing only
            }
          };

          // Add seasonal pricing for all types if dates are provided
          if (check_in && check_out) {
            await addSeasonalPricing(
              extraBedPricing,
              extraBedPricingRule,
              "extra bed"
            );
            await addSeasonalPricing(cotPricing, cotPricingRule, "cot");
          }

          return {
            id: roomConfig.id,
            name: roomConfig.title,
            handle: roomConfig.handle,
            description: roomConfig.description,
            max_extra_beds: Number(maxExtraBeds),
            max_cots: Number(maxCots),
            extra_beds_available:
              Number(maxExtraBeds) > 0 && !!extraBedPricingRule,
            cots_available: Number(maxCots) > 0 && !!cotPricingRule,
            extra_bed_pricing: extraBedPricing,
            cot_pricing: cotPricing,
            // Keep legacy pricing field for backward compatibility
            pricing: extraBedPricing,
          };
        } catch (error) {
          console.error(
            `Error processing room config ${roomConfig.id}:`,
            error
          );
          return {
            id: roomConfig.id,
            name: roomConfig.title,
            handle: roomConfig.handle,
            description: roomConfig.description,
            max_extra_beds: 0,
            max_cots: 0,
            extra_beds_available: false,
            cots_available: false,
            extra_bed_pricing: null,
            cot_pricing: null,
            pricing: null,
            error: "Failed to load extra bed and cot information",
          };
        }
      })
    );

    // Filter out room configurations that don't support extra beds or cots (unless specifically requested)
    const availableRoomConfigs = room_config_id
      ? roomConfigurationsWithExtraBeds
      : roomConfigurationsWithExtraBeds.filter(
          (rc) => rc.extra_beds_available || rc.cots_available
        );

    const response = {
      hotel: hotel[0],
      extra_beds_available: availableRoomConfigs.some(
        (rc) => rc.extra_beds_available
      ),
      cots_available: availableRoomConfigs.some((rc) => rc.cots_available),
      extra_bed_configuration: extraBedConfig
        ? {
            id: extraBedConfig.id,
            name: extraBedConfig.name,
            type: extraBedConfig.type,
            min_age: extraBedConfig.min_age,
            max_age: extraBedConfig.max_age,
          }
        : null,
      cot_configuration: cotConfig
        ? {
            id: cotConfig.id,
            name: cotConfig.name,
            type: cotConfig.type,
            min_age: cotConfig.min_age,
            max_age: cotConfig.max_age,
          }
        : null,
      room_configurations: availableRoomConfigs,
      query_parameters: {
        check_in,
        check_out,
        currency_code,
        room_config_id,
      },
    };

    console.log(
      `Returning ${availableRoomConfigs.length} room configurations with extra bed/cot support`
    );

    return res.json(response);
  } catch (error) {
    console.error("Error getting extra bed and cot information:", error);
    return res.status(500).json({
      message: "Error getting extra bed and cot information",
      error: error instanceof Error ? error.message : "Unknown error",
    });
  }
};
