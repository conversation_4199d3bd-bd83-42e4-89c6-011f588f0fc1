import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";

/**
 * GET /store/currencies
 * 
 * Returns all store currencies with their symbols and metadata
 * This endpoint provides currency information for the storefront
 */
export const GET = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    console.log("Fetching store currencies");

    // Get the query service to fetch store configuration
    const query = req.scope.resolve("query");

    try {
      // Get store with supported currencies using graph query
      const { data: stores } = await query.graph({
        entity: "store",
        fields: ["*", "supported_currencies.*"],
      });

      console.log("Found stores:", stores?.length || 0);
      const store = stores?.[0];

      if (store) {
        console.log("Store supported currencies:", store.supported_currencies?.length || 0);
        console.log("Store currencies details:", store.supported_currencies?.map((c: any) => ({
          currency_code: c.currency_code,
          is_default: c.is_default
        })) || []);
      }

      if (store && store.supported_currencies?.length > 0) {
        // Get the currency service to fetch detailed currency information
        const currencyModuleService = req.scope.resolve(Modules.CURRENCY);
        const allCurrencies = await currencyModuleService.listCurrencies();

        console.log("Available currencies from module:", allCurrencies?.map((c: any) => c.code) || []);

        // Map store currencies to detailed currency information
        const storeCurrencies = store.supported_currencies.map((storeCurrency: any) => {
          const currencyCode = storeCurrency.currency_code.toUpperCase();

          // Find detailed info from currency module
          const detailedCurrency = allCurrencies.find((c: any) =>
            c.code?.toUpperCase() === currencyCode
          );

          return {
            code: currencyCode,
            name: detailedCurrency?.name || currencyCode,
            symbol: detailedCurrency?.symbol || currencyCode,
            decimal_digits: (detailedCurrency as any)?.decimal_digits ?? 2,
            rounding: (detailedCurrency as any)?.rounding || 0,
            is_default: storeCurrency.is_default || false
          };
        });

        // Sort currencies with default currency first
        const sortedCurrencies = storeCurrencies.sort((a, b) => {
          if (a.is_default) return -1;
          if (b.is_default) return 1;
          return a.code.localeCompare(b.code);
        });

        const defaultCurrency = sortedCurrencies.find(c => c.is_default)?.code || sortedCurrencies[0]?.code || "USD";

        console.log(`Found ${sortedCurrencies.length} store currencies, default: ${defaultCurrency}`);

        return res.json({
          currencies: sortedCurrencies,
          default_currency: defaultCurrency,
          total_count: sortedCurrencies.length
        });
      }
    } catch (storeError) {
      console.log("Store query failed, trying currency module fallback:", storeError);
    }

    // Fallback: Get all available currencies from currency module
    const currencyModuleService = req.scope.resolve(Modules.CURRENCY);
    const allCurrencies = await currencyModuleService.listCurrencies();

    console.log("Fallback - Found currencies from module:", allCurrencies?.length || 0);

    if (!allCurrencies || allCurrencies.length === 0) {
      // If no currencies found, return common currencies as fallback
      const fallbackCurrencies = [
        {
          code: "USD",
          name: "US Dollar",
          symbol: "$",
          decimal_digits: 2,
          rounding: 0,
          is_default: true
        },
        {
          code: "EUR",
          name: "Euro",
          symbol: "€",
          decimal_digits: 2,
          rounding: 0,
          is_default: false
        },
        {
          code: "CHF",
          name: "Swiss Franc",
          symbol: "CHF",
          decimal_digits: 2,
          rounding: 0,
          is_default: false
        }
      ];

      console.log("No currencies found in system, returning hardcoded fallback currencies");

      return res.status(200).json({
        currencies: fallbackCurrencies,
        default_currency: "USD",
        total_count: fallbackCurrencies.length,
        message: "Using fallback currencies - please configure currencies in admin"
      });
    }

    // Process all currencies and format them
    const currencyDetails = allCurrencies.map((currency: any) => {
      return {
        code: currency.code,
        name: currency.name || currency.code,
        symbol: currency.symbol || currency.code,
        decimal_digits: currency.decimal_digits || 2,
        rounding: currency.rounding || 0,
        is_default: currency.code === "USD" // Default to USD as primary
      };
    });

    // Sort currencies with USD first, then alphabetically
    const sortedCurrencies = currencyDetails.sort((a, b) => {
      if (a.code === "USD") return -1;
      if (b.code === "USD") return 1;
      return a.code.localeCompare(b.code);
    });

    console.log(`Found ${sortedCurrencies.length} currencies`);

    res.json({
      currencies: sortedCurrencies,
      default_currency: "USD",
      total_count: sortedCurrencies.length
    });

  } catch (error) {
    console.error("Error fetching store currencies:", error);
    res.status(500).json({
      message: "An error occurred while fetching store currencies",
      error: error instanceof Error ? error.message : String(error),
    });
  }
};
