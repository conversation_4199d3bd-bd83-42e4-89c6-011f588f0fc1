import { MedusaRequest, MedusaResponse } from "@camped-ai/framework/http";
import { Modules } from "@camped-ai/framework/utils";
import orderPlaceHandler from "../../../../subscribers/order-placed";

export const POST = async (req: MedusaRequest, res: MedusaResponse) => {
  try {
    const { order_id } = req.body;

    if (!order_id) {
      return res.status(400).json({
        message: "order_id is required in request body"
      });
    }

    // Get the order to verify it exists
    const orderModuleService = req.scope.resolve(Modules.ORDER);
    const order = await orderModuleService.retrieveOrder(order_id, {
      relations: ["items"],
    });

    if (!order) {
      return res.status(404).json({
        message: "Order not found"
      });
    }

    // Manually trigger the order placed subscriber
    await orderPlaceHandler({
      event: { 
        data: { id: order_id },
        name: "order.placed"
      },
      container: req.scope
    });

    res.json({
      message: "Order placed email triggered successfully",
      order_id: order_id,
      order_email: order.email
    });

  } catch (error) {
    console.error("Error triggering order placed email:", error);
    res.status(500).json({
      message: "Failed to trigger order placed email",
      error: error.message
    });
  }
};
