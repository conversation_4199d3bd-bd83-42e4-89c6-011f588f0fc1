import { MigrationInterface, QueryRunner, TableColumn } from "typeorm";

export class AddMaxCotsToRoomConfig1734180000000 implements MigrationInterface {
  name = "AddMaxCotsToRoomConfig1734180000000";

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Check if the column already exists
    const table = await queryRunner.getTable("room_config");
    const maxCotsColumn = table?.findColumnByName("max_cots");

    if (!maxCotsColumn) {
      // Add max_cots column to room_config table
      await queryRunner.addColumn(
        "room_config",
        new TableColumn({
          name: "max_cots",
          type: "integer",
          default: 0,
          isNullable: false,
        })
      );
      console.log("Added max_cots column to room_config table");
    } else {
      console.log("max_cots column already exists in room_config table");
    }
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Check if the column exists before trying to drop it
    const table = await queryRunner.getTable("room_config");
    const maxCotsColumn = table?.findColumnByName("max_cots");

    if (maxCotsColumn) {
      await queryRunner.dropColumn("room_config", "max_cots");
      console.log("Dropped max_cots column from room_config table");
    }
  }
}
