import { useState } from "react";
import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Container,
  Heading,
  Text,
  Button,
  Table,
  Badge,
  IconButton,
  FocusModal,
  Input,
  Label,
  Textarea,
  Switch,
  toast,
  Toaster,
} from "@camped-ai/ui";
import { Plus, Pencil, Trash, Tag } from "lucide-react";
import { useForm, Controller } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import {
  useAdminAddOnCategories,
  useAdminCreateAddOnCategory,
  useAdminUpdateAddOnCategory,
  useAdminDeleteAddOnCategory,
  AddOnCategory,
} from "../../../hooks/add-on-categories/use-admin-add-on-categories";
import HideSidebarItemsWidget from "../../../widgets/hide-sidebar-items-widget";

const categorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  is_active: z.boolean().default(true),
});

type CategoryFormData = z.infer<typeof categorySchema>;

const AddOnCategoriesPage = () => {
  const [isCreateModalOpen, setIsCreateModalOpen] = useState(false);
  const [editingCategory, setEditingCategory] = useState<AddOnCategory | null>(
    null
  );

  // Queries and mutations
  const {
    data: categoriesData,
    isLoading,
    refetch,
  } = useAdminAddOnCategories();
  const createCategoryMutation = useAdminCreateAddOnCategory();
  const updateCategoryMutation = useAdminUpdateAddOnCategory(
    editingCategory?.id || ""
  );
  const deleteCategoryMutation = useAdminDeleteAddOnCategory();

  // Form setup
  const form = useForm<CategoryFormData>({
    resolver: zodResolver(categorySchema),
    defaultValues: {
      name: "",
      description: "",
      is_active: true,
    },
  });

  const categories = (categoriesData as any)?.categories || [];

  const handleCreateCategory = async (data: CategoryFormData) => {
    try {
      await createCategoryMutation.mutateAsync(data);
      toast.success("Category created successfully");
      setIsCreateModalOpen(false);
      form.reset();
      refetch();
    } catch (error) {
      toast.error("Failed to create category");
      console.error("Error creating category:", error);
    }
  };

  const handleUpdateCategory = async (data: CategoryFormData) => {
    if (!editingCategory) return;

    try {
      await updateCategoryMutation.mutateAsync(data);
      toast.success("Category updated successfully");
      setEditingCategory(null);
      form.reset();
      refetch();
    } catch (error) {
      toast.error("Failed to update category");
      console.error("Error updating category:", error);
    }
  };

  const handleDeleteCategory = async (id: string) => {
    if (!confirm("Are you sure you want to delete this category?")) return;

    try {
      await deleteCategoryMutation.mutateAsync(id);
      toast.success("Category deleted successfully");
      refetch();
    } catch (error) {
      toast.error("Failed to delete category");
      console.error("Error deleting category:", error);
    }
  };

  const openEditModal = (category: AddOnCategory) => {
    console.log("Opening edit modal for category:", category);
    console.log(
      "Category is_active value:",
      category.is_active,
      typeof category.is_active
    );
    setEditingCategory(category);
    form.reset({
      name: category.name,
      description: category.description || "",
      is_active: category.is_active,
    });
  };

  const closeModal = () => {
    setIsCreateModalOpen(false);
    setEditingCategory(null);
    form.reset();
  };

  const isModalOpen = isCreateModalOpen || !!editingCategory;

  return (
    <>
      <HideSidebarItemsWidget />
      <Container className="p-0">
        <Toaster />

        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <Heading level="h1" className="text-2xl font-semibold">
              Add-On Categories
            </Heading>
            <Text className="text-gray-600 mt-1">
              Manage categories for organizing add-on services
            </Text>
          </div>
          <Button
            onClick={() => setIsCreateModalOpen(true)}
            className="flex items-center gap-2"
          >
            <Plus size={16} />
            Add Category
          </Button>
        </div>

        {/* Categories Table */}
        <div className="p-6">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <Text>Loading categories...</Text>
            </div>
          ) : categories.length === 0 ? (
            <div className="text-center py-12">
              <div className="mb-4">
                <Tag size={48} className="mx-auto text-gray-400" />
              </div>
              <Heading level="h3" className="mb-2">
                No categories yet
              </Heading>
              <Text className="text-gray-600 mb-4">
                Create your first add-on category to get started
              </Text>
              <Button onClick={() => setIsCreateModalOpen(true)}>
                <Plus size={16} className="mr-2" />
                Add Category
              </Button>
            </div>
          ) : (
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Name</Table.HeaderCell>
                  <Table.HeaderCell>Description</Table.HeaderCell>
                  <Table.HeaderCell>Status</Table.HeaderCell>
                  <Table.HeaderCell className="text-right">
                    Actions
                  </Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {categories.map((category: AddOnCategory) => (
                  <Table.Row key={category.id}>
                    <Table.Cell>
                      <div className="font-medium">{category.name}</div>
                    </Table.Cell>
                    <Table.Cell>
                      <Text className="text-gray-600">
                        {category.description || "—"}
                      </Text>
                    </Table.Cell>
                    <Table.Cell>
                      <Badge
                        color={category.is_active ? "green" : "red"}
                        size="small"
                      >
                        {category.is_active ? "Active" : "Inactive"}
                      </Badge>
                    </Table.Cell>
                    <Table.Cell className="text-right">
                      <div className="flex items-center justify-end gap-1">
                        <IconButton
                          variant="transparent"
                          size="small"
                          onClick={() => openEditModal(category)}
                        >
                          <Pencil size={14} />
                        </IconButton>
                        <IconButton
                          variant="transparent"
                          size="small"
                          onClick={() => handleDeleteCategory(category.id)}
                        >
                          <Trash size={14} />
                        </IconButton>
                      </div>
                    </Table.Cell>
                  </Table.Row>
                ))}
              </Table.Body>
            </Table>
          )}
        </div>

        {/* Create/Edit Modal */}
        <FocusModal open={isModalOpen} onOpenChange={closeModal}>
          <FocusModal.Content>
            <FocusModal.Header>
              <Heading level="h2">
                {editingCategory ? "Edit Category" : "Create Category"}
              </Heading>
            </FocusModal.Header>

            <form
              onSubmit={form.handleSubmit(
                editingCategory ? handleUpdateCategory : handleCreateCategory
              )}
            >
              <FocusModal.Body className="space-y-4 p-6">
                <div>
                  <Label htmlFor="name">Name *</Label>
                  <Input
                    id="name"
                    {...form.register("name")}
                    placeholder="Enter category name"
                  />
                  {form.formState.errors.name && (
                    <Text className="text-red-500 text-sm mt-1">
                      {form.formState.errors.name.message}
                    </Text>
                  )}
                </div>

                <div>
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    {...form.register("description")}
                    placeholder="Enter category description (optional)"
                    rows={3}
                  />
                </div>

                <div className="flex items-center gap-2">
                  <Controller
                    control={form.control}
                    name="is_active"
                    render={({ field }) => (
                      <Switch
                        id="is_active"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    )}
                  />
                  <Label htmlFor="is_active">Active</Label>
                </div>
              </FocusModal.Body>

              <FocusModal.Footer>
                <div className="flex items-center gap-2">
                  <Button
                    type="button"
                    variant="secondary"
                    onClick={closeModal}
                  >
                    Cancel
                  </Button>
                  <Button
                    type="submit"
                    isLoading={
                      createCategoryMutation.isPending ||
                      updateCategoryMutation.isPending
                    }
                  >
                    {editingCategory ? "Update" : "Create"}
                  </Button>
                </div>
              </FocusModal.Footer>
            </form>
          </FocusModal.Content>
        </FocusModal>
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Add-On Categories",
  icon: Tag,
});

export default AddOnCategoriesPage;
