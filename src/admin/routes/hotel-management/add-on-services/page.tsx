import { defineRouteConfig } from "@camped-ai/admin-sdk";
import {
  Clock<PERSON>olid,
  PlusMini,
  MagnifyingGlass,
  Adjustments,
} from "@camped-ai/icons";
import { useState, useEffect } from "react";
import {
  Button,
  Heading,
  Text,
  Input,
  Table,
  Badge,
  FocusModal,
  IconButton,
  Tooltip,
  Container,
} from "@camped-ai/ui";
import { toast } from "sonner";
import {
  Edit,
  Trash2,
  Plus,
  Search,
  Hotel,
  Map,
  Download,
  Upload as UploadIcon,
} from "lucide-react";
import OutlineButton from "../../../components/shared/OutlineButton";
import AddOnServiceForm from "../../../components/hotel/add-on-service/add-on-service-form-simple";
import "./modal-fix.css";

interface Hotel {
  id: string;
  name: string;
  currency: string;
}

interface Destination {
  id: string;
  name: string;
}

interface AddOnService {
  id: string;
  name: string;
  description: string;
  type?: string;
  service_level: "hotel" | "destination";
  hotel_id?: string;
  hotel_name?: string;
  destination_id?: string | string[];
  destination_name?: string;
  category_id?: string;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
  max_capacity?: number;
  adult_price?: number;
  child_price?: number;
  currency_code: string;
  images?: string[];
  created_at: string;
  updated_at: string;
  metadata?: {
    destination_name?: string;
    hotel_name?: string;
    adult_price_formatted?: string;
    child_price_formatted?: string;
    per_day_adult_price_formatted?: string;
    per_day_child_price_formatted?: string;
    package_price_formatted?: string;
    pricing_type?: string;
    [key: string]: any;
  };
}



const AddOnServicesPage = () => {
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [destinations, setDestinations] = useState<Destination[]>([]);
  const [addOnServices, setAddOnServices] = useState<AddOnService[]>([]);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingService, setEditingService] = useState<AddOnService | null>(
    null
  );
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [filterHotelId, setFilterHotelId] = useState<string | null>(null);
  const [filterHotelName, setFilterHotelName] = useState<string>("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");

  // Get hotel_id from URL query parameter
  useEffect(() => {
    const params = new URLSearchParams(window.location.search);
    const hotelId = params.get("hotel_id");
    if (hotelId && hotelId !== "undefined" && hotelId !== "null") {
      setFilterHotelId(hotelId);

      // Find the hotel name for the filter
      const findHotelName = async () => {
        try {
          const response = await fetch(
            `/admin/hotel-management/hotels/${hotelId}`
          );
          if (response.ok) {
            const data = await response.json();
            if (data && data.hotel && data.hotel.name) {
              setFilterHotelName(data.hotel.name);
            }
          }
        } catch (error) {
          console.error("Error fetching hotel name:", error);
        }
      };

      findHotelName();
    }
  }, []);

  // Fetch hotels, destinations, and add-on services
  useEffect(() => {
    const fetchData = async () => {
      setIsLoading(true);
      try {
        // Fetch hotels
        const hotelsResponse = await fetch(`/admin/hotel-management/hotels`);
        if (!hotelsResponse.ok) {
          throw new Error("Failed to fetch hotels");
        }
        const hotelsData = await hotelsResponse.json();
        console.log("Hotels data:", hotelsData);
        setHotels(hotelsData.hotels || []);

        // Fetch destinations
        console.log("Fetching destinations...");
        const destinationsResponse = await fetch(
          `/admin/hotel-management/destinations`
        );
        if (!destinationsResponse.ok) {
          console.error(
            "Failed to fetch destinations:",
            await destinationsResponse.text()
          );
          throw new Error("Failed to fetch destinations");
        }
        const destinationsData = await destinationsResponse.json();
        console.log("Destinations data:", destinationsData);

        // Check if we have destinations
        if (
          !destinationsData.destinations ||
          destinationsData.destinations.length === 0
        ) {
          console.warn("No destinations found in API response");
        } else {
          console.log(
            "Found destinations:",
            destinationsData.destinations.map((d) => ({
              id: d.id,
              name: d.name,
            }))
          );
        }

        setDestinations(destinationsData.destinations || []);

        // Use the unified endpoint
        const url = filterHotelId
          ? `/admin/add-on-services?hotel_id=${filterHotelId}&service_level=hotel`
          : `/admin/add-on-services`;

        // Fetch all services
        const response = await fetch(url);
        if (!response.ok) {
          console.error("Failed to fetch add-on services");
          throw new Error("Failed to fetch add-on services");
        }

        const servicesData = await response.json();

        console.log("Services data:", servicesData);
        console.log("Add-on services array:", servicesData.add_on_services);
        console.log(
          "Total services:",
          servicesData.add_on_services?.length || 0
        );

        // Log service levels for debugging
        const hotelServices =
          servicesData.add_on_services?.filter(
            (s: any) => s.service_level === "hotel"
          ) || [];
        const destinationServices =
          servicesData.add_on_services?.filter(
            (s: any) => s.service_level === "destination"
          ) || [];
        console.log("Hotel services:", hotelServices.length);
        console.log("Destination services:", destinationServices.length);

        // Log raw data for debugging
        console.log("Raw add-on services:", servicesData.add_on_services);

        // Log destination names and metadata for debugging
        servicesData.add_on_services?.forEach((service: any) => {
          if (service.service_level === "destination") {
            console.log(`Service ${service.id} destination info:`, {
              destination_id: service.destination_id,
              destination_name: service.destination_name,
              metadata_destination_name: service.metadata?.destination_name,
              metadata: service.metadata,
            });
          }
        });

        // Check if destination names are already present in the API response
        const hasDestinationNames = servicesData.add_on_services?.some(
          (service: any) =>
            service.destination_name && service.destination_name.length > 0
        );
        console.log(
          "API response includes destination names:",
          hasDestinationNames
        );

        // Check if we have valid data
        if (
          !servicesData.add_on_services ||
          !Array.isArray(servicesData.add_on_services) ||
          servicesData.add_on_services.length === 0
        ) {
          console.error("No valid add-on services data found");
          setAddOnServices([]);
          setIsLoading(false);
          return;
        }

        // Check if services have IDs
        const hasValidIds = servicesData.add_on_services.every(
          (service: AddOnService) => service.id
        );
        if (!hasValidIds) {
          console.error("Services are missing IDs");
          toast.error("Unable to load services: Invalid data format");
          setAddOnServices([]);
          setIsLoading(false);
          return;
        }

        // Enrich services with hotel and destination names
        const enrichedServices = servicesData.add_on_services.map(
          (service: AddOnService) => {
            const hotel = service.hotel_id
              ? hotels.find((h) => h.id === service.hotel_id)
              : null;

            // Use destination_name from metadata if available
            // This should be stored in metadata by the backend
            let destinationName = service.destination_name || "";

            // If we still don't have a destination name but have metadata with it, use that
            if (!destinationName && service.metadata?.destination_name) {
              destinationName = service.metadata.destination_name;
              console.log(
                `Using destination name from metadata: ${destinationName}`
              );
            }

            // If we still don't have a name but have an ID, try to resolve it as a fallback
            if (!destinationName && service.destination_id) {
              console.log(
                `Falling back to frontend resolution for destination name`
              );

              if (Array.isArray(service.destination_id)) {
                // For array of destination IDs
                if (destinations && destinations.length > 0) {
                  // Map IDs to names if we have destination data
                  const resolvedNames = [];

                  for (const id of service.destination_id) {
                    const destination = destinations.find((d) => d.id === id);
                    resolvedNames.push(destination?.name || id);
                  }

                  destinationName = resolvedNames.join(", ");
                } else {
                  // Just join the IDs if we don't have destination data
                  destinationName = service.destination_id.join(", ");
                }
              } else {
                // For single destination ID
                if (destinations && destinations.length > 0) {
                  const destination = destinations.find(
                    (d) => d.id === service.destination_id
                  );
                  destinationName = destination?.name || service.destination_id;
                } else {
                  destinationName = service.destination_id;
                }
              }
            }

            return {
              ...service,
              hotel_name: hotel?.name || "",
              destination_name: destinationName,
            };
          }
        );

        setAddOnServices(enrichedServices);
      } catch (error) {
        console.error("Error fetching data:", error);
        toast.error("Failed to fetch required data");
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, [filterHotelId]);

  // Fetch add-on services after creating or updating
  const fetchAddOnServices = async () => {
    try {
      // Use the unified endpoint
      const url = filterHotelId
        ? `/admin/add-on-services?hotel_id=${filterHotelId}&service_level=hotel`
        : `/admin/add-on-services`;

      // Fetch all services
      const response = await fetch(url);
      if (!response.ok) {
        console.error("Failed to fetch add-on services");
        throw new Error("Failed to fetch add-on services");
      }

      const data = await response.json();

      console.log("Refreshed add-on services:", data.add_on_services);
      console.log("Total services:", data.add_on_services?.length || 0);

      // Log service levels for debugging
      const hotelServices =
        data.add_on_services?.filter((s: any) => s.service_level === "hotel") ||
        [];
      const destinationServices =
        data.add_on_services?.filter(
          (s: any) => s.service_level === "destination"
        ) || [];
      console.log("Hotel services:", hotelServices.length);
      console.log("Destination services:", destinationServices.length);

      // Log destination names and metadata for debugging
      data.add_on_services?.forEach((service: any) => {
        if (service.service_level === "destination") {
          console.log(`Refreshed: Service ${service.id} destination info:`, {
            destination_id: service.destination_id,
            destination_name: service.destination_name,
            metadata_destination_name: service.metadata?.destination_name,
            metadata: service.metadata,
          });
        }
      });

      // Check if we have valid data
      if (
        !data.add_on_services ||
        !Array.isArray(data.add_on_services) ||
        data.add_on_services.length === 0
      ) {
        console.error("No valid add-on services data found during refresh");
        setAddOnServices([]);
        return;
      }

      // Check if services have IDs
      const hasValidIds = data.add_on_services.every(
        (service: AddOnService) => service.id
      );
      if (!hasValidIds) {
        console.error("Refreshed services are missing IDs");
        toast.error("Unable to load services: Invalid data format");
        setAddOnServices([]);
        return;
      }

      // Enrich services with hotel and destination names
      const enrichedServices = data.add_on_services.map(
        (service: AddOnService) => {
          const hotel = service.hotel_id
            ? hotels.find((h) => h.id === service.hotel_id)
            : null;

          // Use destination_name from metadata if available
          // This should be stored in metadata by the backend
          let destinationName = service.destination_name || "";

          // If we still don't have a destination name but have metadata with it, use that
          if (!destinationName && service.metadata?.destination_name) {
            destinationName = service.metadata.destination_name;
            console.log(
              `Using destination name from metadata: ${destinationName}`
            );
          }

          // If we still don't have a name but have an ID, try to resolve it as a fallback
          if (!destinationName && service.destination_id) {
            console.log(
              `Falling back to frontend resolution for destination name`
            );

            if (Array.isArray(service.destination_id)) {
              // For array of destination IDs
              if (destinations && destinations.length > 0) {
                // Map IDs to names if we have destination data
                const resolvedNames = [];

                for (const id of service.destination_id) {
                  const destination = destinations.find((d) => d.id === id);
                  resolvedNames.push(destination?.name || id);
                }

                destinationName = resolvedNames.join(", ");
              } else {
                // Just join the IDs if we don't have destination data
                destinationName = service.destination_id.join(", ");
              }
            } else {
              // For single destination ID
              if (destinations && destinations.length > 0) {
                const destination = destinations.find(
                  (d) => d.id === service.destination_id
                );
                destinationName = destination?.name || service.destination_id;
              } else {
                destinationName = service.destination_id;
              }
            }
          }

          // Log the destination name resolution process
          if (service.service_level === "destination") {
            console.log(
              `Resolving destination name for service ${service.id}:`,
              {
                original_destination_name: service.destination_name,
                metadata_destination_name: service.metadata?.destination_name,
                resolved_destination_name: destinationName,
              }
            );
          }

          return {
            ...service,
            hotel_name: service.metadata?.hotel_name || hotel?.name || "",
            destination_name:
              service.metadata?.destination_name || destinationName,
          };
        }
      );

      setAddOnServices(enrichedServices);
    } catch (error) {
      console.error("Error fetching add-on services:", error);
      toast.error("Failed to fetch add-on services");
    }
  };

  // Handle form submission for creating or updating an add-on service
  const handleSubmit = async (data: any) => {
    try {
      // We no longer need to convert arrays to strings as the backend now supports arrays
      // Log the data for debugging
      if (Array.isArray(data.hotel_id)) {
        console.log(
          `Sending hotel_id as array: ${JSON.stringify(data.hotel_id)}`
        );
      }

      if (Array.isArray(data.destination_id)) {
        console.log(
          `Sending destination_id as array: ${JSON.stringify(
            data.destination_id
          )}`
        );
      }

      console.log("Submitting data to API:", data);

      if (editingService) {
        // Update existing service
        // Use the unified endpoint
        const endpoint = `/admin/add-on-services/${editingService.id}`;

        console.log(
          `Updating service ${editingService.id} with level ${editingService.service_level} using endpoint: ${endpoint}`
        );

        const response = await fetch(endpoint, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(data),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.message || "Failed to update add-on service"
          );
        }

        toast.success("Add-on service updated successfully");
      } else {
        // Create new service
        // If we're filtering by hotel, automatically set the hotel_id
        // Make sure hotel_id is an array if it's not already
        const serviceData = filterHotelId
          ? {
              ...data,
              hotel_id: Array.isArray(data.hotel_id)
                ? data.hotel_id
                : [filterHotelId],
              service_level: "hotel",
            }
          : data;

        // Use the unified endpoint
        const endpoint = "/admin/add-on-services";

        console.log(
          `Creating service with level ${serviceData.service_level} using endpoint: ${endpoint}`
        );

        const response = await fetch(endpoint, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(serviceData),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({}));
          throw new Error(
            errorData.message || "Failed to create add-on service"
          );
        }

        toast.success("Add-on service created successfully");
      }

      // Close modal and refresh data
      setIsModalOpen(false);
      setEditingService(null);
      fetchAddOnServices();
    } catch (error) {
      console.error("Error submitting form:", error);
      toast.error("Failed to save add-on service");
    }
  };

  // Handle deleting an add-on service
  const handleDelete = async (id: string) => {
    if (
      !window.confirm("Are you sure you want to delete this add-on service?")
    ) {
      return;
    }

    try {
      // Find the service to determine its level
      const service = addOnServices.find((s) => s.id === id);
      if (!service) {
        throw new Error("Service not found");
      }

      // Use the unified endpoint
      const endpoint = `/admin/add-on-services/${id}`;

      console.log(
        `Deleting service ${id} with level ${service.service_level} using endpoint: ${endpoint}`
      );

      const response = await fetch(endpoint, {
        method: "DELETE",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to delete add-on service");
      }

      toast.success("Add-on service deleted successfully");
      fetchAddOnServices();
    } catch (error: any) {
      console.error("Error deleting add-on service:", error);
      toast.error(error.message || "Failed to delete add-on service");
    }
  };

  // Filter add-on services based on search query
  const filteredServices = addOnServices.filter(
    (service) =>
      service.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      (service.hotel_name &&
        service.hotel_name.toLowerCase().includes(searchQuery.toLowerCase())) ||
      (service.destination_name &&
        service.destination_name
          .toLowerCase()
          .includes(searchQuery.toLowerCase()))
  );

  return (
    <Container className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <Heading level="h1" className="text-2xl">
            Add-on Services
          </Heading>
          <Text className="text-gray-500">
            {filterHotelName
              ? `Manage add-on services for ${filterHotelName}`
              : "Manage add-on services for hotels and destinations"}
          </Text>
          {filterHotelId && (
            <div className="mt-2 flex items-center">
              <Badge className="flex items-center gap-1 bg-gray-100 text-gray-800">
                <Hotel className="w-3 h-3" />
                {filterHotelName || "Hotel"}
                <button
                  onClick={() => {
                    setFilterHotelId(null);
                    setFilterHotelName("");
                    window.history.pushState(
                      {},
                      "",
                      "/hotel-management/add-on-services"
                    );
                  }}
                  className="ml-1 text-gray-500 hover:text-gray-700"
                >
                  ×
                </button>
              </Badge>
            </div>
          )}
        </div>

        <div className="flex gap-2">
          <Button
            variant="primary"
            size="small"
            onClick={() => {
              setEditingService(null);
              setIsModalOpen(true);
            }}
            className="shadow-sm flex items-center gap-2 px-4 py-2 rounded-md transition-all"
          >
            <PlusMini className="w-4 h-4" />
            <span>Add Service</span>
          </Button>
        </div>
      </div>

      <div className="flex flex-col md:flex-row gap-4">
        <div className="relative flex-grow">
          <MagnifyingGlass className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
          <Input
            placeholder="Search services..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-9 pl-10"
          />
        </div>

        <div className="flex gap-2">
          <div className="flex gap-2">
            <Button
              variant={viewMode === "grid" ? "primary" : "secondary"}
              size="small"
              onClick={() => setViewMode("grid")}
              className={`px-3 py-2 rounded-md shadow-sm flex items-center justify-center ${
                viewMode === "grid"
                  ? " text-white"
                  : "bg-white border border-gray-200 hover:bg-gray-50 text-gray-700"
              }`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 h-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
              </svg>
            </Button>

            <Button
              variant={viewMode === "list" ? "primary" : "secondary"}
              size="small"
              onClick={() => setViewMode("list")}
              className={`px-3 py-2 rounded-md shadow-sm flex items-center justify-center ${
                viewMode === "list"
                  ? " text-white"
                  : "bg-white border border-gray-200 hover:bg-gray-50 text-gray-700"
              }`}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="w-5 h-5"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                  clipRule="evenodd"
                />
              </svg>
            </Button>
          </div>
        </div>
      </div>

      {isLoading ? (
        <div className="text-center py-8">
          <Text>Loading add-on services...</Text>
        </div>
      ) : filteredServices.length === 0 ? (
        <div className="text-center py-8 border rounded-lg">
          <Text className="text-gray-500">
            {searchQuery
              ? "No add-on services found matching your search"
              : "No add-on services found. Create your first one!"}
          </Text>
          <Button
            variant="secondary"
            className="mt-4"
            onClick={() => {
              setEditingService(null);
              setIsModalOpen(true);
            }}
          >
            <Plus className="w-4 h-4 mr-2" />
            Add Service
          </Button>
        </div>
      ) : viewMode === "list" ? (
        <Table>
          <Table.Header>
            <Table.Row>
              <Table.HeaderCell>Name</Table.HeaderCell>
              <Table.HeaderCell>Type</Table.HeaderCell>
              {/* <Table.HeaderCell>Location</Table.HeaderCell> */}
              <Table.HeaderCell>Status</Table.HeaderCell>
              <Table.HeaderCell>Actions</Table.HeaderCell>
            </Table.Row>
          </Table.Header>
          <Table.Body>
            {filteredServices.map((service) => (
              <Table.Row key={service.id}>
                <Table.Cell>
                  <div className="font-medium">{service.name}</div>
                </Table.Cell>
                <Table.Cell>
                  <div className="flex flex-col gap-1">
                    {service.service_level === "hotel"
                      ? "Hotel"
                      : "Destination"}
                  </div>
                </Table.Cell>
                {/* <Table.Cell>
                  {service.service_level === "hotel" ? (
                    <span className="text-gray-900">
                      {service.hotel_name || "Unknown Hotel"}
                    </span>
                  ) : (
                    <span className="text-gray-900">

                      {service.metadata?.destination_name ||
                        service.destination_name ||
                        (Array.isArray(service.destination_id)
                          ? service.destination_id.join(", ")
                          : service.destination_id || "Unknown Destination")}
                    </span>
                  )}
                </Table.Cell> */}
                <Table.Cell>
                  {service.is_active ? (
                    <Badge className="bg-green-100 text-green-800">
                      Active
                    </Badge>
                  ) : (
                    <Badge className="bg-gray-100 text-gray-800">
                      Inactive
                    </Badge>
                  )}
                </Table.Cell>
                <Table.Cell>
                  <div className="flex space-x-2">
                    <Tooltip content="Edit">
                      <IconButton
                        variant="transparent"
                        size="small"
                        onClick={() => {
                          console.log("Editing service:", service);
                          console.log("Service price data:", {
                            adult_price: service.adult_price,
                            child_price: service.child_price,
                            metadata: service.metadata,
                            metadata_adult_price: service.metadata?.adult_price,
                            metadata_child_price: service.metadata?.child_price,
                            adult_price_formatted:
                              service.metadata?.adult_price_formatted,
                            child_price_formatted:
                              service.metadata?.child_price_formatted,
                          });
                          setEditingService(service);
                          setIsModalOpen(true);
                        }}
                      >
                        <Edit className="w-4 h-4" />
                      </IconButton>
                    </Tooltip>
                    <Tooltip content="Delete">
                      <IconButton
                        variant="transparent"
                        size="small"
                        onClick={() => handleDelete(service.id)}
                      >
                        <Trash2 className="w-4 h-4" />
                      </IconButton>
                    </Tooltip>
                  </div>
                </Table.Cell>
              </Table.Row>
            ))}
          </Table.Body>
        </Table>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {filteredServices.map((service) => (
            <div
              key={service.id}
              className="bg-white p-4 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="font-medium text-lg">{service.name}</h3>
                  <div className="flex items-center gap-2 mt-1">
                    {service.service_level === "hotel" ? (
                      <Badge className="bg-gray-100 text-gray-800">
                        <Hotel className="w-3 h-3 mr-1" />
                        Hotel
                      </Badge>
                    ) : (
                      <Badge className="bg-gray-100 text-gray-800">
                        <Map className="w-3 h-3 mr-1" />
                        Destination
                      </Badge>
                    )}
                    {service.is_active ? (
                      <Badge className="bg-green-100 text-green-800">
                        Active
                      </Badge>
                    ) : (
                      <Badge className="bg-gray-100 text-gray-800">
                        Inactive
                      </Badge>
                    )}
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Tooltip content="Edit">
                    <IconButton
                      variant="transparent"
                      size="small"
                      onClick={() => {
                        setEditingService(service);
                        setIsModalOpen(true);
                      }}
                    >
                      <Edit className="w-4 h-4" />
                    </IconButton>
                  </Tooltip>
                  <Tooltip content="Delete">
                    <IconButton
                      variant="transparent"
                      size="small"
                      onClick={() => handleDelete(service.id)}
                    >
                      <Trash2 className="w-4 h-4" />
                    </IconButton>
                  </Tooltip>
                </div>
              </div>

              <div className="text-sm text-gray-600 mb-3">
                {service.service_level === "hotel" ? (
                  <div className="flex items-center gap-1">
                    <Hotel className="w-3 h-3" />
                    <span>{service.hotel_name || "Unknown Hotel"}</span>
                  </div>
                ) : (
                  <div className="flex items-center gap-1">
                    <Map className="w-3 h-3" />
                    <span>
                      {service.metadata?.destination_name ||
                        service.destination_name ||
                        (Array.isArray(service.destination_id)
                          ? service.destination_id.join(", ")
                          : service.destination_id || "Unknown Destination")}
                    </span>
                  </div>
                )}
              </div>

              <div className="flex justify-between items-center mt-4">
                <div className="text-sm font-medium">
                  {getFormattedPriceDisplay(service)}
                </div>
                {service.type && (
                  <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">
                    {service.type}
                  </span>
                )}
              </div>
            </div>
          ))}
        </div>
      )}

      <FocusModal
        open={isModalOpen}
        onOpenChange={(open) => {
          if (!open) {
            setEditingService(null);
          }
          setIsModalOpen(open);
        }}
      >
        <FocusModal.Content className="max-h-[90vh] overflow-y-auto max-w-[60vw] w-full mx-auto my-8 !h-auto">
          <FocusModal.Header>
            <FocusModal.Title>
              {editingService ? "Edit Add-on Service" : "Create Add-on Service"}
            </FocusModal.Title>
            <FocusModal.Description>
              {editingService
                ? "Update the details of this add-on service"
                : "Create a new add-on service for hotels or destinations"}
            </FocusModal.Description>
          </FocusModal.Header>
          <div className="overflow-y-auto max-h-[calc(90vh-120px)]">
            <AddOnServiceForm
              initialData={editingService}
              onSubmit={handleSubmit}
              onCancel={() => {
                setIsModalOpen(false);
                setEditingService(null);
              }}
              hotelCurrency="USD"
              availableHotels={hotels}
              availableDestinations={destinations}
              hotelId={filterHotelId || undefined}
              key={`form-${editingService?.id || "new"}-${hotels.length}-${
                destinations.length
              }-${filterHotelId}`}
            />
          </div>
        </FocusModal.Content>
      </FocusModal>
    </Container>
  );
};

export const config = defineRouteConfig({
  label: "Add ons",
  icon: ClockSolid,
});

export default AddOnServicesPage;
