/* Fix for modal z-index issues */
.focus-modal-content,
[data-focus-modal-content] {
  z-index: 9999 !important;
}

.focus-modal-overlay,
[data-focus-modal-overlay] {
  z-index: 9998 !important;
}

/* Target the specific modal elements by their DOM structure */
div[role="dialog"] {
  z-index: 10000 !important; /* Higher than other elements */
}

div[role="dialog"] + div {
  z-index: 9998 !important;
}

/* Target the NativePrompt component */
.camped-ui-dialog,
[data-dialog-content] {
  z-index: 10000 !important; /* Higher than other modals */
}

.camped-ui-dialog-overlay,
[data-dialog-overlay] {
  z-index: 9999 !important; /* Higher than other modal overlays */
}

/* Target specific dialog elements */
.camped-ui-dialog-content {
  z-index: 10000 !important;
  position: relative !important;
}

/* Ensure the dialog appears above everything else */
[data-radix-popper-content-wrapper] {
  z-index: 10000 !important;
}
