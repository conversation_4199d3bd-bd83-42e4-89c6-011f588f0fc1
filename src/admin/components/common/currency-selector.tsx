import React from "react";
import { Label } from "@camped-ai/ui";
import { useAdminCurrencies } from "../../hooks/use-admin-currencies";
import { getCurrencyIcon } from "../../utils/currency-utils";

interface CurrencySelectorProps {
  value: string;
  onChange: (currencyCode: string) => void;
  label?: string;
  id?: string;
  className?: string;
  disabled?: boolean;
  showLabel?: boolean;
  placeholder?: string;
}

export const CurrencySelector: React.FC<CurrencySelectorProps> = ({
  value,
  onChange,
  label = "Currency",
  id = "currency",
  className = "",
  disabled = false,
  showLabel = true,
  placeholder = "Select currency",
}) => {
  const { currencyOptions, isLoading, defaultCurrency } = useAdminCurrencies();

  // Get the icon component for the selected currency
  const IconComponent = getCurrencyIcon(value);

  // Use default currency if no value is provided
  const currentValue = value || defaultCurrency?.currency_code || "";

  const handleChange = (event: React.ChangeEvent<HTMLSelectElement>) => {
    onChange(event.target.value);
  };

  if (isLoading) {
    return (
      <div className={className}>
        {showLabel && (
          <Label htmlFor={id} className="mb-1 block">
            {label}
          </Label>
        )}
        <div className="relative inline-block">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <div className="w-4 h-4 bg-gray-300 rounded animate-pulse" />
          </div>
          <select
            id={id}
            className="pl-10 pr-4 py-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
            disabled
          >
            <option>Loading...</option>
          </select>
          <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
            <svg
              className="w-4 h-4 text-gray-500"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              />
            </svg>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={className}>
      {showLabel && (
        <Label htmlFor={id} className="mb-1 block">
          {label}
        </Label>
      )}
      <div className="relative inline-block">
        <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
          <IconComponent className="w-4 h-4 text-gray-500" />
        </div>
        <select
          id={id}
          className="pl-10 pr-4 py-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
          value={currentValue}
          onChange={handleChange}
          disabled={disabled}
        >
          {!currentValue && (
            <option value="" disabled>
              {placeholder}
            </option>
          )}
          {currencyOptions.map((option) => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg
            className="w-4 h-4 text-gray-500"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            />
          </svg>
        </div>
      </div>
    </div>
  );
};
