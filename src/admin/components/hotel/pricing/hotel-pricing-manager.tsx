import React, { useState, useEffect } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  toast,
  Toaster,
  Tabs,
} from "@camped-ai/ui";
import { ArrowLeft } from "lucide-react";
import ComprehensivePricingTable from "./comprehensive-pricing-table";
import { useAdminHotelOccupancyConfigs } from "../../../hooks/hotel/use-admin-hotel-occupancy-configs";
import { useAdminHotelMealPlans } from "../../../hooks/hotel/use-admin-hotel-meal-plans";
import OccupancyConfigsManager from "./occupancy-configs-manager";
import MealPlansManager from "./meal-plans-manager";
import SeasonsManager from "./seasons-manager.tsx";

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type HotelPricingManagerProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  onBack?: () => void;
};

const HotelPricingManager: React.FC<HotelPricingManagerProps> = ({
  hotelId,
  roomConfigs,
  onBack,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [pricingData, setPricingData] = useState<Record<string, any>>({});
  const [seasonalPeriods, setSeasonalPeriods] = useState<SeasonalPeriod[]>([]);
  const [activeTab, setActiveTab] = useState("pricing");

  // Reset tab if it was set to a removed tab
  useEffect(() => {
    if (activeTab === "channels" || activeTab === "unified") {
      setActiveTab("pricing");
    }
  }, [activeTab]);

  const { occupancyConfigs, isLoading: isLoadingOccupancy, refetch: refetchOccupancyConfigs } = useAdminHotelOccupancyConfigs(hotelId);
  const { mealPlans, isLoading: isLoadingMealPlans, refetch: refetchMealPlans } = useAdminHotelMealPlans(hotelId);

  // Load pricing data for all room configurations
  useEffect(() => {
    const fetchPricingData = async () => {
      setIsLoading(true);

      try {
        // Fetch pricing data for each room configuration
        const fetchPromises = roomConfigs.map(async (room) => {
          try {
            // Fetch base pricing data
            const basePricingResponse = await fetch(`/admin/hotel-management/room-configs/${room.id}/weekday-pricing`);

            if (!basePricingResponse.ok) {
              throw new Error(`Failed to fetch base pricing rules: ${basePricingResponse.statusText}`);
            }

            const basePricingData = await basePricingResponse.json();

            // Fetch seasonal pricing data for all currencies (we'll filter by currency in the UI)
            const seasonalPricingResponse = await fetch(`/admin/hotel-management/room-configs/${room.id}/seasonal-pricing`);

            let seasonalPricingData = { seasonal_prices: [] };
            if (seasonalPricingResponse.ok) {
              seasonalPricingData = await seasonalPricingResponse.json();
            }

            // Group data by currency to handle multi-currency pricing
            const currencyGroups: Record<string, any> = {};

            // Group weekday rules by currency
            (basePricingData.weekday_rules || []).forEach((rule: any) => {
              const currency = rule.currency_code || "USD";
              if (!currencyGroups[currency]) {
                currencyGroups[currency] = {
                  currency_code: currency,
                  weekday_rules: [],
                  seasonal_prices: []
                };
              }
              currencyGroups[currency].weekday_rules.push(rule);
            });

            // Group seasonal prices by currency
            (seasonalPricingData.seasonal_prices || []).forEach((sp: any) => {
              // Extract currency from the weekday_rules if available
              const currency = sp.weekday_rules?.[0]?.currency_code || sp.currency_code || "USD";
              if (!currencyGroups[currency]) {
                currencyGroups[currency] = {
                  currency_code: currency,
                  weekday_rules: [],
                  seasonal_prices: []
                };
              }
              currencyGroups[currency].seasonal_prices.push(sp);
            });

            // If no data exists, create USD default
            if (Object.keys(currencyGroups).length === 0) {
              currencyGroups["USD"] = {
                currency_code: "USD",
                weekday_rules: [],
                seasonal_prices: []
              };
            }

            return {
              roomConfigId: room.id,
              data: currencyGroups
            };
          } catch (error) {
            console.error(`Error fetching pricing for room ${room.id}:`, error);
            return {
              roomConfigId: room.id,
              data: {
                "USD": {
                  currency_code: "USD",
                  weekday_rules: [],
                  seasonal_prices: [],
                }
              }
            };
          }
        });

        const results = await Promise.all(fetchPromises);

        // Convert array of results to a record keyed by room config ID
        const pricingDataRecord: Record<string, any> = {};
        const allSeasonalPeriods: SeasonalPeriod[] = [];

        results.forEach(result => {
          pricingDataRecord[result.roomConfigId] = result.data;

          // Collect all seasonal periods from all currencies
          Object.values(result.data).forEach((currencyData: any) => {
            (currencyData.seasonal_prices || []).forEach((sp: SeasonalPeriod) => {
              // Check if this seasonal period is already in the list
              const existingPeriod = allSeasonalPeriods.find(
                existing => existing.id === sp.id ||
                           (existing.name === sp.name &&
                            existing.start_date === sp.start_date &&
                            existing.end_date === sp.end_date)
              );

              if (!existingPeriod) {
                allSeasonalPeriods.push({
                  id: sp.id,
                  name: sp.name,
                  start_date: sp.start_date,
                  end_date: sp.end_date,
                });
              }
            });
          });
        });

        setPricingData(pricingDataRecord);
        setSeasonalPeriods(allSeasonalPeriods);
      } catch (error) {
        console.error("Error fetching pricing data:", error);
        toast.error("Error", {
          description: "Failed to load pricing data",
        });
      } finally {
        setIsLoading(false);
      }
    };

    if (roomConfigs.length > 0) {
      fetchPricingData();
    } else {
      setIsLoading(false);
    }
  }, [roomConfigs]);

  const handleSavePricing = (data: any) => {
    // If the data contains seasonal periods, update the state
    if (data && data.seasonal_periods) {
      setSeasonalPeriods(data.seasonal_periods);
    }

    toast.success("Success", {
      description: "Pricing saved successfully",
    });
  };

  // Refresh data when tabs change
  useEffect(() => {
    if (activeTab === "occupancy") {
      refetchOccupancyConfigs();
    } else if (activeTab === "mealplans") {
      refetchMealPlans();
    }
  }, [activeTab, refetchOccupancyConfigs, refetchMealPlans]);

  if (isLoading || isLoadingOccupancy || isLoadingMealPlans) {
    return (
      <Container>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded-md mb-4 w-1/3"></div>
          <div className="mb-6 h-4 bg-gray-200 rounded-md w-1/2"></div>

          <div className="flex justify-between mb-6">
            <div className="h-10 bg-gray-200 rounded-md w-32"></div>
            <div className="h-10 bg-gray-200 rounded-md w-28"></div>
          </div>

          <div className="rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="h-12 bg-gray-100 rounded-t-md w-full"></div>
            <div className="h-16 bg-white border-t border-gray-200 w-full"></div>
            <div className="h-16 bg-gray-50 border-t border-gray-200 w-full"></div>
            <div className="h-16 bg-white border-t border-gray-200 w-full"></div>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Toaster />

      <div className="mb-6">
        <Heading level="h1">Hotel Pricing Management</Heading>
        <Text className="text-gray-600">
          Manage occupancy types, meal plans, and pricing for all room configurations
        </Text>
      </div>

      {roomConfigs.length === 0 ? (
        <div className="bg-gray-50 p-8 rounded-lg border border-gray-200 text-center">
          <Text className="text-gray-500 mb-4">
            No room configurations found. Please add room configurations first.
          </Text>
          <Button
            variant="primary"
            onClick={() => window.location.href = `/hotel-management/hotels/${hotelId}/room-configs/new`}
          >
            Add Room Configuration
          </Button>
        </div>
      ) : (
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <Tabs.List className="border-b border-gray-200 mb-4">
            <Tabs.Trigger value="occupancy" className="px-4 py-2">Occupancy Types</Tabs.Trigger>
            <Tabs.Trigger value="mealplans" className="px-4 py-2">Meal Plans</Tabs.Trigger>
            <Tabs.Trigger value="seasons" className="px-4 py-2">Seasons</Tabs.Trigger>
            <Tabs.Trigger value="pricing" className="px-4 py-2">Pricing</Tabs.Trigger>
            {/* <Tabs.Trigger value="channel-overrides" className="px-4 py-2">Channel Overrides</Tabs.Trigger> */}
          </Tabs.List>

          <Tabs.Content value="occupancy">
            <OccupancyConfigsManager
              hotelId={hotelId}
            />
          </Tabs.Content>

          <Tabs.Content value="mealplans">
            <MealPlansManager
              hotelId={hotelId}
            />
          </Tabs.Content>

          <Tabs.Content value="seasons">
            <SeasonsManager
              hotelId={hotelId}
              seasonalPeriods={seasonalPeriods}
              setSeasonalPeriods={setSeasonalPeriods}
            />
          </Tabs.Content>

          <Tabs.Content value="pricing">
            <ComprehensivePricingTable
              hotelId={hotelId}
              roomConfigs={roomConfigs}
              occupancyConfigs={occupancyConfigs}
              mealPlans={mealPlans}
              seasonalPeriods={seasonalPeriods}
              setSeasonalPeriods={setSeasonalPeriods}
              initialPrices={pricingData}
              onSave={handleSavePricing}
            />
          </Tabs.Content>

          {/* <Tabs.Content value="channel-overrides">
            <SalesChannelPriceOverrides
              hotelId={hotelId}
              roomConfigs={roomConfigs}
              occupancyConfigs={occupancyConfigs}
              mealPlans={mealPlans}
              seasonalPeriods={seasonalPeriods}
              onSave={handleSavePricing}
            />
          </Tabs.Content> */}
        </Tabs>
      )}
    </Container>
  );
};

export default HotelPricingManager;
