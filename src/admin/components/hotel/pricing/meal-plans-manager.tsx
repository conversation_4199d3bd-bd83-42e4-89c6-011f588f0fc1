import React, { useState } from "react";
import {
  Button,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Prompt,
  Input,
  Label,
} from "@camped-ai/ui";
import {
  useAdminHotelMealPlans,
  useAdminCreateHotelMealPlan,
  useAdminUpdateHotelMealPlan,
  useAdminDeleteHotelMealPlan
} from "../../../hooks/hotel/use-admin-hotel-meal-plans";
import { PlusCircle, Edit, Trash } from "lucide-react";

enum MealPlanTypeEnum {
  NONE = "none",
  BED_BREAKFAST = "bb",
  HALF_BOARD = "hb",
  FULL_BOARD = "fb",
}

type MealPlansManagerProps = {
  hotelId: string;
};

const MealPlansManager: React.FC<MealPlansManagerProps> = ({
  hotelId,
}) => {
  const { mealPlans = [], isLoading, refetch } = useAdminHotelMealPlans(hotelId);
  const { createMealPlan } = useAdminCreateHotelMealPlan();
  const { updateMealPlan } = useAdminUpdateHotelMealPlan();
  const { deleteMealPlan } = useAdminDeleteHotelMealPlan();

  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [currentMealPlan, setCurrentMealPlan] = useState<MealPlan | null>(null);
  const [formData, setFormData] = useState({
    name: "",
    type: MealPlanTypeEnum.NONE as string, // Allow any string for custom types
    is_default: false,
  });

  const handleOpenDialog = (mealPlan?: any) => {
    if (mealPlan) {
      setCurrentMealPlan(mealPlan);
      setFormData({
        name: mealPlan.name,
        type: mealPlan.type,
        is_default: mealPlan.is_default,
      });
    } else {
      setCurrentMealPlan(null);
      setFormData({
        name: "",
        type: MealPlanTypeEnum.NONE,
        is_default: false,
      });
    }
    setIsDialogOpen(true);
  };

  const handleOpenDeleteDialog = (mealPlan: any) => {
    setCurrentMealPlan(mealPlan);
    setIsDeleteDialogOpen(true);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value, type } = e.target;
    setFormData({
      ...formData,
      [name]: type === "checkbox"
        ? (e.target as HTMLInputElement).checked
        : value,
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      if (currentMealPlan) {
        // Update existing meal plan
        await updateMealPlan(
          hotelId,
          currentMealPlan.id,
          {
            name: formData.name,
            type: formData.type,
            is_default: formData.is_default
          }
        );

        toast.success("Success", {
          description: "Meal plan updated successfully",
        });
      } else {
        // Create new meal plan
        await createMealPlan(
          hotelId,
          {
            name: formData.name,
            type: formData.type,
            is_default: formData.is_default
          }
        );

        toast.success("Success", {
          description: "Meal plan created successfully",
        });
      }

      setIsDialogOpen(false);
      refetch(); // Refresh the data
    } catch (error) {
      console.error("Error saving meal plan:", error);
      toast.error("Error", {
        description: "Failed to save meal plan",
      });
    }
  };

  const handleDelete = async () => {
    if (!currentMealPlan) return;

    try {
      console.log("Deleting meal plan:", currentMealPlan.id);
      const result = await deleteMealPlan(hotelId, currentMealPlan.id);
      console.log("Delete result:", result);

      toast.success("Success", {
        description: "Meal plan deleted successfully",
      });

      setIsDeleteDialogOpen(false);
      refetch(); // Refresh the data
    } catch (error) {
      console.error("Error deleting meal plan:", error);
      toast.error("Error", {
        description: "Failed to delete meal plan",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded-md mb-4 w-1/3"></div>
          <div className="h-64 bg-gray-100 rounded-md"></div>
        </div>
      </div>
    );
  }

  return (
    <Container>
      <Toaster />
      <div className="mb-6">
        <Heading level="h2">Meal Plans</Heading>
        <Text className="text-gray-600">
          Manage meal plans for your hotel
        </Text>
      </div>

      <div className="flex justify-end mb-4">
        <Button
          variant="primary"
          onClick={() => handleOpenDialog()}
          className="flex items-center gap-2"
        >
          <PlusCircle className="w-4 h-4" />
          Add Meal Plan
        </Button>
      </div>

      <div className="overflow-x-auto border border-gray-200 rounded-lg">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Type
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Default
              </th>
              <th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {mealPlans.map((mealPlan) => (
              <tr key={mealPlan.id} className="hover:bg-gray-50">
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                  {mealPlan.name}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {mealPlan.type}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                  {mealPlan.is_default ? "Yes" : "No"}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                  <div className="flex justify-end gap-2">
                    <Button
                      variant="secondary"
                      onClick={() => handleOpenDialog(mealPlan)}
                    >
                      <Edit className="w-4 h-4 mr-1" /> Edit
                    </Button>
                    <Button
                      variant="danger"
                      onClick={() => handleOpenDeleteDialog(mealPlan)}
                    >
                      <Trash className="w-4 h-4 mr-1" /> Delete
                    </Button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Create/Edit Dialog */}
      <Prompt open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>
              {currentMealPlan ? "Edit Meal Plan" : "Add Meal Plan"}
            </Prompt.Title>
          </Prompt.Header>
          <form onSubmit={handleSubmit}>
            <div className="space-y-4 py-4">
              <div className="grid grid-cols-1 gap-4">
                <div>
                  <Label htmlFor="name">Name</Label>
                  <Input
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                  />
                </div>
                <div>
                  <Label htmlFor="type">Type</Label>
                  <div className="grid grid-cols-1 gap-2">
                    <select
                      id="type-select"
                      className={`w-full p-2 border rounded-md ${
                        currentMealPlan
                          ? "border-gray-200 bg-gray-50 text-gray-500 cursor-not-allowed"
                          : "border-gray-300"
                      }`}
                      value={formData.type}
                      onChange={(e) => {
                        setFormData({
                          ...formData,
                          type: e.target.value,
                        });
                      }}
                      disabled={!!currentMealPlan}
                      required
                    >
                      <option value={MealPlanTypeEnum.NONE}>None (Room Only)</option>
                      <option value={MealPlanTypeEnum.BED_BREAKFAST}>Bed & Breakfast</option>
                      <option value={MealPlanTypeEnum.HALF_BOARD}>Half Board</option>
                      <option value={MealPlanTypeEnum.FULL_BOARD}>Full Board</option>
                      <option value="custom">Custom Type...</option>
                    </select>

                    {formData.type === "custom" && (
                      <Input
                        id="type-custom"
                        name="type-custom"
                        placeholder="Enter custom meal plan type"
                        onChange={(e) => {
                          setFormData({
                            ...formData,
                            type: e.target.value,
                          });
                        }}
                        disabled={!!currentMealPlan}
                        required
                      />
                    )}

                    {formData.type !== "custom" && (
                      <input type="hidden" name="type" value={formData.type} />
                    )}
                  </div>
                </div>
                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="is_default"
                    name="is_default"
                    checked={formData.is_default}
                    onChange={handleInputChange}
                    className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                  />
                  <Label htmlFor="is_default">Default</Label>
                </div>
              </div>
            </div>
            <Prompt.Footer>
              <Button variant="secondary" type="button" onClick={() => setIsDialogOpen(false)}>
                Cancel
              </Button>
              <Button variant="primary" type="submit">
                {currentMealPlan ? "Update" : "Create"}
              </Button>
            </Prompt.Footer>
          </form>
        </Prompt.Content>
      </Prompt>

      {/* Delete Confirmation Dialog */}
      <Prompt open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <Prompt.Content>
          <Prompt.Header>
            <Prompt.Title>Delete Meal Plan</Prompt.Title>
          </Prompt.Header>
          <div className="py-4">
            <p>
              Are you sure you want to delete the meal plan "{currentMealPlan?.name}"?
              This action cannot be undone.
            </p>
          </div>
          <Prompt.Footer>
            <Button variant="secondary" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancel
            </Button>
            <Button variant="danger" onClick={handleDelete}>
              Delete
            </Button>
          </Prompt.Footer>
        </Prompt.Content>
      </Prompt>
    </Container>
  );
};

export default MealPlansManager;
