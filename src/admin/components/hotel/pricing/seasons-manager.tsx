import React, { useState } from "react";
import {
  Container,
  Heading,
  Text,
  Button,
  Table,
  FocusModal,
  Input,
  Label,
  toast,
  Toaster,
} from "@camped-ai/ui";
import { PlusMini } from "@camped-ai/icons";
import { Calendar, Trash2, Loader2 } from "lucide-react";
import { format } from "date-fns";
import { useAdminDeleteSeasonalPricing } from "../../../hooks/hotel/use-admin-delete-seasonal-pricing";

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

interface SeasonsManagerProps {
  hotelId: string;
  seasonalPeriods: SeasonalPeriod[];
  setSeasonalPeriods: React.Dispatch<React.SetStateAction<SeasonalPeriod[]>>;
}

// Helper function to generate a unique ID
const generateId = () => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

const SeasonsManager: React.FC<SeasonsManagerProps> = ({
  hotelId,
  seasonalPeriods,
  setSeasonalPeriods,
}) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newSeasonName, setNewSeasonName] = useState("");
  const [newSeasonStartDate, setNewSeasonStartDate] = useState<Date>(new Date());
  const [newSeasonEndDate, setNewSeasonEndDate] = useState<Date>(() => {
    const date = new Date();
    date.setDate(date.getDate() + 7);
    return date;
  });
  const [isAddingSeasonalPeriod, setIsAddingSeasonalPeriod] = useState(false);
  const [isDeletingSeason, setIsDeletingSeason] = useState<string | null>(null);

  // Hook for deleting seasonal pricing rules from database
  const deleteSeasonalPricing = useAdminDeleteSeasonalPricing(hotelId);

  const handleOpenModal = () => {
    setNewSeasonName("");
    const today = new Date();
    today.setHours(12, 0, 0, 0);
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);
    setNewSeasonStartDate(today);
    setNewSeasonEndDate(nextWeek);
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewSeasonName("");
    const today = new Date();
    today.setHours(12, 0, 0, 0);
    const nextWeek = new Date(today);
    nextWeek.setDate(today.getDate() + 7);
    setNewSeasonStartDate(today);
    setNewSeasonEndDate(nextWeek);
  };

  const handleAddSeasonalPeriod = () => {
    // Validate inputs
    if (!newSeasonName.trim()) {
      toast.error("Error", {
        description: "Please enter a season name",
      });
      return;
    }

    // Make sure we have valid dates
    let startDate = newSeasonStartDate;
    let endDate = newSeasonEndDate;

    if (!startDate) {
      startDate = new Date();
    }

    if (!endDate) {
      endDate = new Date();
      // Set to 7 days after start date by default
      endDate.setDate(startDate.getDate() + 7);
    }

    if (startDate > endDate) {
      toast.error("Error", {
        description: "End date must be after start date",
      });
      return;
    }

    setIsAddingSeasonalPeriod(true);

    try {
      // Format dates safely
      const formatDate = (date: Date) => {
        try {
          return format(date, "yyyy-MM-dd");
        } catch (e) {
          console.error("Date formatting error:", e);
          // Fallback to ISO string and extract the date part
          return date.toISOString().split("T")[0];
        }
      };

      // Create a new seasonal period
      const newSeasonalPeriod: SeasonalPeriod = {
        id: generateId(),
        name: newSeasonName,
        start_date: formatDate(startDate),
        end_date: formatDate(endDate),
      };

      // Add the new seasonal period to the list
      setSeasonalPeriods((prevSeasons) => [...prevSeasons, newSeasonalPeriod]);

      toast.success("Success", {
        description: "Season created successfully",
      });

      // Close the modal and reset form
      handleCloseModal();
    } catch (error) {
      console.error("Error adding seasonal period:", error);
      toast.error("Error", {
        description: "Failed to create season",
      });
    } finally {
      setIsAddingSeasonalPeriod(false);
    }
  };

  const handleDelete = async (season: SeasonalPeriod) => {
    if (!confirm(`Are you sure you want to delete the season "${season.name}"? This will permanently remove all associated seasonal pricing data from the database. This action cannot be undone.`)) {
      return;
    }

    setIsDeletingSeason(season.id);

    try {
      // Format dates to ensure they're in YYYY-MM-DD format
      const formatDateForAPI = (dateStr: string) => {
        try {
          console.log("Original date string:", dateStr);
          const date = new Date(dateStr);
          const formatted = format(date, "yyyy-MM-dd");
          console.log("Formatted date:", formatted);
          return formatted;
        } catch (error) {
          console.error("Error formatting date:", error, "Original:", dateStr);
          return dateStr; // fallback to original string
        }
      };

      // First, delete all seasonal pricing rules from the database
      const result = await deleteSeasonalPricing.mutateAsync({
        name: season.name,
        start_date: formatDateForAPI(season.start_date),
        end_date: formatDateForAPI(season.end_date),
      });

      console.log(`Deleted ${result.deleted_count} seasonal pricing rules for season "${season.name}"`);

      // Then remove the season from the frontend state
      setSeasonalPeriods(prev => prev.filter(s => s.id !== season.id));

      toast.success("Success", {
        description: `Season "${season.name}" and ${result.deleted_count} associated pricing rules deleted successfully`
      });

    } catch (error) {
      console.error("Error deleting season:", error);
      toast.error("Error", {
        description: error instanceof Error ? error.message : "Failed to delete season and pricing data"
      });
    } finally {
      setIsDeletingSeason(null);
    }
  };

  return (
    <>
      <Toaster />
      <Container className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Heading level="h2" className="text-xl font-semibold text-gray-900 flex items-center">
              <Calendar className="w-5 h-5 mr-2 text-blue-600" />
              Seasonal Periods
            </Heading>
            <Text className="text-gray-600 mt-1">
              Manage seasonal periods for pricing variations throughout the year
            </Text>
          </div>
          <Button
            variant="primary"
            onClick={() => handleOpenModal()}
            className="flex items-center gap-2"
          >
            <PlusMini className="w-4 h-4" />
            Add Season
          </Button>
        </div>

        {seasonalPeriods.length === 0 ? (
          <div className="bg-gray-50 p-8 rounded-lg border border-gray-200 text-center">
            <Calendar className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <Text className="text-gray-500 mb-4">
              No seasonal periods configured yet. Create your first season to set up seasonal pricing.
            </Text>
          </div>
        ) : (
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
            <Table>
              <Table.Header>
                <Table.Row>
                  <Table.HeaderCell>Season Name</Table.HeaderCell>
                  <Table.HeaderCell>Start Date</Table.HeaderCell>
                  <Table.HeaderCell>End Date</Table.HeaderCell>
                  <Table.HeaderCell>Duration</Table.HeaderCell>
                  <Table.HeaderCell>Action</Table.HeaderCell>
                </Table.Row>
              </Table.Header>
              <Table.Body>
                {seasonalPeriods.map((season) => {
                  const startDate = new Date(season.start_date);
                  const endDate = new Date(season.end_date);
                  const duration = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
                  
                  return (
                    <Table.Row key={season.id}>
                      <Table.Cell>
                        <div className="font-medium text-gray-900">{season.name}</div>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="text-gray-700">
                          {format(startDate, "MMM dd, yyyy")}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="text-gray-700">
                          {format(endDate, "MMM dd, yyyy")}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="text-gray-600">
                          {duration} {duration === 1 ? 'day' : 'days'}
                        </div>
                      </Table.Cell>
                      <Table.Cell>
                        <div className="flex items-center">
                          <Button
                            variant="danger"
                            size="small"
                            onClick={() => handleDelete(season)}
                            disabled={isDeletingSeason === season.id}
                            className="flex items-center gap-1"
                          >
                            {isDeletingSeason === season.id ? (
                              <>
                                <Loader2 className="w-3 h-3 animate-spin" />
                                Deleting...
                              </>
                            ) : (
                              <>
                                <Trash2 className="w-3 h-3" />
                                Delete
                              </>
                            )}
                          </Button>
                        </div>
                      </Table.Cell>
                    </Table.Row>
                  );
                })}
              </Table.Body>
            </Table>
          </div>
        )}

        {/* Add/Edit Season Modal */}
        <FocusModal
          open={isModalOpen}
          onOpenChange={(open) => {
            setIsModalOpen(open);
            if (!open) {
              handleCloseModal();
            }
          }}
        >
          <FocusModal.Content>
            <FocusModal.Header>
              <Button
                variant="secondary"
                onClick={handleCloseModal}
                disabled={isAddingSeasonalPeriod}
              >
                Cancel
              </Button>
              <Text className="inter-large-semibold">
                Add Seasonal Period
              </Text>
              <Button
                variant="primary"
                onClick={handleAddSeasonalPeriod}
                disabled={isAddingSeasonalPeriod}
              >
                {isAddingSeasonalPeriod ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin mr-2" />
                    Adding...
                  </>
                ) : (
                  "Add Season"
                )}
              </Button>
            </FocusModal.Header>
            <FocusModal.Body>
              <div className="p-6 space-y-6">
                <div>
                  <Label htmlFor="seasonName" className="mb-1 block">
                    Season Name
                  </Label>
                  <Input
                    id="seasonName"
                    value={newSeasonName}
                    onChange={(e) => setNewSeasonName(e.target.value)}
                    placeholder="e.g., Summer 2023"
                    className="w-full"
                    required
                  />
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="startDate" className="mb-1 block">
                      Start Date
                    </Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <Calendar className="w-4 h-4 text-gray-500" />
                      </div>
                      <Input
                        id="startDate"
                        type="date"
                        value={
                          newSeasonStartDate
                            ? format(newSeasonStartDate, "yyyy-MM-dd")
                            : ""
                        }
                        onChange={(e) => {
                          if (e.target.value) {
                            // Create date at noon to avoid timezone issues
                            const date = new Date(e.target.value + "T12:00:00");
                            setNewSeasonStartDate(date);
                          } else {
                            setNewSeasonStartDate(new Date());
                          }
                        }}
                        className="pl-10 w-full"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="endDate" className="mb-1 block">
                      End Date
                    </Label>
                    <div className="relative">
                      <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                        <Calendar className="w-4 h-4 text-gray-500" />
                      </div>
                      <Input
                        id="endDate"
                        type="date"
                        value={
                          newSeasonEndDate
                            ? format(newSeasonEndDate, "yyyy-MM-dd")
                            : ""
                        }
                        onChange={(e) => {
                          if (e.target.value) {
                            // Create date at noon to avoid timezone issues
                            const date = new Date(e.target.value + "T12:00:00");
                            setNewSeasonEndDate(date);
                          } else {
                            // Default to 7 days after start date
                            const date = new Date(newSeasonStartDate);
                            date.setDate(date.getDate() + 7);
                            setNewSeasonEndDate(date);
                          }
                        }}
                        className="pl-10 w-full"
                        required
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
                  <Text className="text-blue-800">
                    <strong>Note:</strong> Adding a new seasonal period will create pricing rows for all room types, occupancy types, and meal plans. Base prices will be copied to the new seasonal period by default.
                  </Text>
                </div>
              </div>
            </FocusModal.Body>
          </FocusModal.Content>
        </FocusModal>
      </Container>
    </>
  );
};

export default SeasonsManager;
