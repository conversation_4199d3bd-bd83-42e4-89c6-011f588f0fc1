import React, { useState, useEffect } from "react";
import { Button, Text, Input, Label, FocusModal, toast } from "@camped-ai/ui";
import {
  Calculator,
  Percent,
  DollarSign,
  Euro,
  PoundSterling,
  Loader2,
} from "lucide-react";
import { format } from "date-fns";
import { CurrencySelector } from "../../common/currency-selector";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type OccupancyConfig = {
  id: string;
  name: string;
  is_default?: boolean;
};

type MealPlan = {
  id: string;
  name: string;
  is_default?: boolean;
};

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type PricingRow = {
  id: string;
  roomConfigId: string;
  occupancyTypeId: string;
  mealPlanId: string | null;
  seasonalPeriodId?: string;
  prices: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  modified: boolean;
};

type BulkUpdateType = "amount" | "percentage";

interface BulkPriceUpdateModalProps {
  isOpen: boolean;
  onClose: () => void;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  seasonalPeriods: SeasonalPeriod[];
  currencyCode: string;
  pricingRows: PricingRow[];
  onApplyUpdate: (updatedRows: PricingRow[]) => void;
  onCurrencyChange?: (currencyCode: string) => void;
}

const BulkPriceUpdateModal: React.FC<BulkPriceUpdateModalProps> = ({
  isOpen,
  onClose,
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods,
  currencyCode,
  pricingRows,
  onApplyUpdate,
  onCurrencyChange,
}) => {
  const [updateType, setUpdateType] = useState<BulkUpdateType>("amount");
  const [updateValue, setUpdateValue] = useState<number>(0);
  const [selectedRoomConfigs, setSelectedRoomConfigs] = useState<string[]>([]);
  const [selectedOccupancyConfigs, setSelectedOccupancyConfigs] = useState<
    string[]
  >([]);
  const [selectedMealPlans, setSelectedMealPlans] = useState<string[]>([]);
  const [selectedSeasonalPeriods, setSelectedSeasonalPeriods] = useState<
    string[]
  >([]);
  const [seasonFilter, setSeasonFilter] = useState<"all" | "base" | "seasonal">(
    "all"
  );
  const [isApplying, setIsApplying] = useState(false);
  const [selectedCurrency, setSelectedCurrency] =
    useState<string>(currencyCode);

  const { currencies, defaultCurrency } = useAdminCurrencies();

  // Sync selected currency with prop changes
  useEffect(() => {
    setSelectedCurrency(currencyCode);
  }, [currencyCode]);

  // Get current currency object for proper formatting
  const currentCurrency =
    currencies.find((c: any) => c.currency_code === selectedCurrency) ||
    defaultCurrency;

  // Helper function to get currency symbol
  const getCurrencySymbol = (): string => {
    return currentCurrency?.symbol || selectedCurrency;
  };

  // Handle currency change - notify parent to reload pricing data
  const handleCurrencyChange = (newCurrencyCode: string) => {
    setSelectedCurrency(newCurrencyCode);
    if (onCurrencyChange) {
      onCurrencyChange(newCurrencyCode);
    }
  };

  const handleRoomConfigToggle = (roomConfigId: string) => {
    setSelectedRoomConfigs((prev) =>
      prev.includes(roomConfigId)
        ? prev.filter((id) => id !== roomConfigId)
        : [...prev, roomConfigId]
    );
  };

  const handleOccupancyConfigToggle = (occupancyConfigId: string) => {
    setSelectedOccupancyConfigs((prev) =>
      prev.includes(occupancyConfigId)
        ? prev.filter((id) => id !== occupancyConfigId)
        : [...prev, occupancyConfigId]
    );
  };

  const handleMealPlanToggle = (mealPlanId: string) => {
    setSelectedMealPlans((prev) =>
      prev.includes(mealPlanId)
        ? prev.filter((id) => id !== mealPlanId)
        : [...prev, mealPlanId]
    );
  };

  const handleSeasonalPeriodToggle = (seasonalPeriodId: string) => {
    setSelectedSeasonalPeriods((prev) =>
      prev.includes(seasonalPeriodId)
        ? prev.filter((id) => id !== seasonalPeriodId)
        : [...prev, seasonalPeriodId]
    );
  };

  const calculateNewPrice = (currentPrice: number): number => {
    if (updateType === "amount") {
      return Math.max(0, currentPrice + updateValue);
    } else {
      // Percentage update
      const multiplier = 1 + updateValue / 100;
      return Math.max(0, currentPrice * multiplier);
    }
  };

  const getMatchingRows = (): PricingRow[] => {
    return pricingRows.filter((row) => {
      // Check room config filter
      if (
        selectedRoomConfigs.length > 0 &&
        !selectedRoomConfigs.includes(row.roomConfigId)
      ) {
        return false;
      }

      // Check occupancy config filter
      if (
        selectedOccupancyConfigs.length > 0 &&
        !selectedOccupancyConfigs.includes(row.occupancyTypeId)
      ) {
        return false;
      }

      // Check meal plan filter (handle null meal plans for extra beds/cots)
      if (selectedMealPlans.length > 0) {
        if (row.mealPlanId === null) {
          return false; // Extra beds/cots don't have meal plans
        }
        if (!selectedMealPlans.includes(row.mealPlanId)) {
          return false;
        }
      }

      // Check seasonal period filter
      if (seasonFilter === "base") {
        // Only base pricing (no seasonal period)
        if (row.seasonalPeriodId) {
          return false;
        }
      } else if (seasonFilter === "seasonal") {
        // Only seasonal pricing
        if (!row.seasonalPeriodId) {
          return false;
        }
        // If specific seasonal periods are selected, filter by them
        if (
          selectedSeasonalPeriods.length > 0 &&
          !selectedSeasonalPeriods.includes(row.seasonalPeriodId)
        ) {
          return false;
        }
      } else if (seasonFilter === "all") {
        // All pricing, but if specific seasonal periods are selected, filter by them
        if (selectedSeasonalPeriods.length > 0) {
          // Include base pricing and selected seasonal periods
          if (
            row.seasonalPeriodId &&
            !selectedSeasonalPeriods.includes(row.seasonalPeriodId)
          ) {
            return false;
          }
        }
      }

      return true;
    });
  };

  const handleApplyUpdate = async () => {
    if (updateValue === 0) {
      toast.error("Error", {
        description: "Please enter a valid update value",
      });
      return;
    }

    if (
      selectedRoomConfigs.length === 0 &&
      selectedOccupancyConfigs.length === 0 &&
      selectedMealPlans.length === 0 &&
      seasonFilter === "all" &&
      selectedSeasonalPeriods.length === 0
    ) {
      toast.error("Error", {
        description: "Please select at least one filter criteria",
      });
      return;
    }

    setIsApplying(true);

    try {
      const matchingRows = getMatchingRows();

      if (matchingRows.length === 0) {
        toast.error("Error", {
          description: "No pricing rows match the selected criteria",
        });
        return;
      }

      // Apply updates to matching rows
      const updatedRows = pricingRows.map((row) => {
        if (matchingRows.includes(row)) {
          return {
            ...row,
            prices: {
              mon: calculateNewPrice(row.prices.mon),
              tue: calculateNewPrice(row.prices.tue),
              wed: calculateNewPrice(row.prices.wed),
              thu: calculateNewPrice(row.prices.thu),
              fri: calculateNewPrice(row.prices.fri),
              sat: calculateNewPrice(row.prices.sat),
              sun: calculateNewPrice(row.prices.sun),
            },
            modified: true,
          };
        }
        return row;
      });

      onApplyUpdate(updatedRows);

      toast.success("Success", {
        description: `Updated ${matchingRows.length} pricing rows`,
      });

      // Reset form and close modal
      setUpdateValue(0);
      setSelectedRoomConfigs([]);
      setSelectedOccupancyConfigs([]);
      setSelectedMealPlans([]);
      setSelectedSeasonalPeriods([]);
      setSeasonFilter("all");
      onClose();
    } catch (error) {
      console.error("Error applying bulk update:", error);
      toast.error("Error", {
        description: "Failed to apply bulk price update",
      });
    } finally {
      setIsApplying(false);
    }
  };

  const matchingRowsCount = getMatchingRows().length;

  return (
    <FocusModal open={isOpen} onOpenChange={onClose}>
      <FocusModal.Content>
        <FocusModal.Header>
          <Button variant="secondary" onClick={onClose} disabled={isApplying}>
            Cancel
          </Button>
          <Text className="inter-large-semibold">Bulk Price Update</Text>
          <Button
            variant="primary"
            onClick={handleApplyUpdate}
            disabled={isApplying || updateValue === 0}
          >
            {isApplying ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin mr-2" />
                Applying...
              </>
            ) : (
              <>
                <Calculator className="w-4 h-4 mr-2" />
                Update {matchingRowsCount} Rows
              </>
            )}
          </Button>
        </FocusModal.Header>
        <FocusModal.Body>
          <div className="p-6 space-y-6">
            {/* Update Type Selection */}
            <div>
              <Label className="mb-3 block text-sm font-medium">
                Update Type
              </Label>
              <div className="grid grid-cols-2 gap-3">
                <button
                  type="button"
                  onClick={() => setUpdateType("amount")}
                  className={`p-3 border rounded-lg flex items-center gap-2 transition-colors ${
                    updateType === "amount"
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  title="Add or subtract a fixed amount from each price. Use negative values to subtract."
                >
                  {selectedCurrency === "USD" && (
                    <DollarSign className="w-4 h-4" />
                  )}
                  {selectedCurrency === "EUR" && <Euro className="w-4 h-4" />}
                  {selectedCurrency === "GBP" && (
                    <PoundSterling className="w-4 h-4" />
                  )}
                  <span>Fixed Amount</span>
                </button>
                <button
                  type="button"
                  onClick={() => setUpdateType("percentage")}
                  className={`p-3 border rounded-lg flex items-center gap-2 transition-colors ${
                    updateType === "percentage"
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  title="Increase or decrease prices by a percentage. Use negative values to decrease."
                >
                  <Percent className="w-4 h-4" />
                  <span>Percentage</span>
                </button>
              </div>
            </div>

            {/* Currency Selection */}
            <div>
              {currencies.length > 1 && (
              <CurrencySelector
                value={selectedCurrency}
                onChange={handleCurrencyChange}
                label="Currency"
                id="bulkUpdateCurrency"
                className="w-full"
              />
              )}
              {selectedCurrency !== currencyCode && (
                <div className="mt-2 p-3 bg-amber-50 border border-amber-200 rounded-md">
                  <Text className="text-amber-800 text-sm">
                    <strong>Note:</strong> Changing currency will reload pricing
                    data for {selectedCurrency}. Any unsaved changes in the main
                    table will be preserved, but you'll only see pricing rows
                    that exist for the selected currency.
                  </Text>
                </div>
              )}
            </div>

            {/* Update Value */}
            <div>
              <Label htmlFor="updateValue" className="mb-1 block">
                {updateType === "amount"
                  ? `Amount to Add/Subtract (${getCurrencySymbol()})`
                  : "Percentage to Increase/Decrease (%)"}
              </Label>
              <div className="relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  {updateType === "amount" ? (
                    <span className="text-gray-500">{getCurrencySymbol()}</span>
                  ) : (
                    <Percent className="w-4 h-4 text-gray-500" />
                  )}
                </div>
                <Input
                  id="updateValue"
                  type="number"
                  step={updateType === "amount" ? "0.01" : "1"}
                  value={updateValue}
                  onChange={(e) => setUpdateValue(Number(e.target.value))}
                  className="pl-8"
                  placeholder={updateType === "amount" ? "10.00" : "10"}
                />
              </div>
              <p className="text-xs text-gray-500 mt-1">
                {updateType === "amount"
                  ? "Use negative values to subtract from prices"
                  : "Use negative values to decrease prices"}
              </p>
            </div>

            {/* Room Type Selection */}
            <div>
              <Label className="mb-2 block text-sm font-medium">
                Room Types (Optional)
              </Label>
              <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedRoomConfigs.length === roomConfigs.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedRoomConfigs(roomConfigs.map((r) => r.id));
                      } else {
                        setSelectedRoomConfigs([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">Select All</span>
                </label>
                {roomConfigs.map((room) => (
                  <label key={room.id} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={selectedRoomConfigs.includes(room.id)}
                      onChange={() => handleRoomConfigToggle(room.id)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">{room.title}</span>
                  </label>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Leave empty to update all room types
              </p>
            </div>

            {/* Occupancy Type Selection */}
            <div>
              <Label className="mb-2 block text-sm font-medium">
                Occupancy Types (Optional)
              </Label>
              <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={
                      selectedOccupancyConfigs.length ===
                      occupancyConfigs.length
                    }
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedOccupancyConfigs(
                          occupancyConfigs.map((o) => o.id)
                        );
                      } else {
                        setSelectedOccupancyConfigs([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">Select All</span>
                </label>
                {occupancyConfigs.map((occupancy) => (
                  <label key={occupancy.id} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={selectedOccupancyConfigs.includes(occupancy.id)}
                      onChange={() => handleOccupancyConfigToggle(occupancy.id)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">{occupancy.name}</span>
                  </label>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Leave empty to update all occupancy types
              </p>
            </div>

            {/* Meal Plan Selection */}
            <div>
              <Label className="mb-2 block text-sm font-medium">
                Meal Plans (Optional)
              </Label>
              <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={selectedMealPlans.length === mealPlans.length}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedMealPlans(mealPlans.map((m) => m.id));
                      } else {
                        setSelectedMealPlans([]);
                      }
                    }}
                    className="rounded border-gray-300"
                  />
                  <span className="text-sm font-medium">Select All</span>
                </label>
                {mealPlans.map((mealPlan) => (
                  <label key={mealPlan.id} className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={selectedMealPlans.includes(mealPlan.id)}
                      onChange={() => handleMealPlanToggle(mealPlan.id)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm">{mealPlan.name}</span>
                  </label>
                ))}
              </div>
              <p className="text-xs text-gray-500 mt-1">
                Leave empty to update all meal plans (excludes extra beds/cots)
              </p>
            </div>

            {/* Season Filter */}
            <div>
              <Label className="mb-2 block text-sm font-medium">
                Season Filter
              </Label>
              <div className="grid grid-cols-3 gap-2 mb-3">
                <button
                  type="button"
                  onClick={() => setSeasonFilter("all")}
                  className={`p-2 border rounded-md text-sm transition-colors ${
                    seasonFilter === "all"
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  title="Update both base pricing and seasonal pricing. Select specific seasons below to limit which seasonal periods are updated."
                >
                  All Pricing
                </button>
                <button
                  type="button"
                  onClick={() => setSeasonFilter("base")}
                  className={`p-2 border rounded-md text-sm transition-colors ${
                    seasonFilter === "base"
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  title="Update only base pricing (no seasonal periods will be affected)"
                >
                  Base Only
                </button>
                <button
                  type="button"
                  onClick={() => setSeasonFilter("seasonal")}
                  className={`p-2 border rounded-md text-sm transition-colors ${
                    seasonFilter === "seasonal"
                      ? "border-blue-500 bg-blue-50 text-blue-700"
                      : "border-gray-300 hover:border-gray-400"
                  }`}
                  title="Update only seasonal pricing. Select specific seasons below or leave empty to update all seasonal periods."
                >
                  Seasonal Only
                </button>
              </div>

              {/* Seasonal Period Selection - only show when seasonal filter is active */}
              {(seasonFilter === "seasonal" || seasonFilter === "all") &&
                seasonalPeriods.length > 0 && (
                  <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-200 rounded-md p-2">
                    <label className="flex items-center gap-2">
                      <input
                        type="checkbox"
                        checked={
                          selectedSeasonalPeriods.length ===
                          seasonalPeriods.length
                        }
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedSeasonalPeriods(
                              seasonalPeriods.map((s) => s.id)
                            );
                          } else {
                            setSelectedSeasonalPeriods([]);
                          }
                        }}
                        className="rounded border-gray-300"
                      />
                      <span className="text-sm font-medium">
                        Select All Seasons
                      </span>
                    </label>
                    {seasonalPeriods.map((season) => (
                      <label
                        key={season.id}
                        className="flex items-center gap-2"
                      >
                        <input
                          type="checkbox"
                          checked={selectedSeasonalPeriods.includes(season.id)}
                          onChange={() => handleSeasonalPeriodToggle(season.id)}
                          className="rounded border-gray-300"
                        />
                        <span className="text-sm">{season.name}</span>
                        <span className="text-xs text-gray-500">
                          ({format(new Date(season.start_date), "MMM d")} to{" "}
                          {format(new Date(season.end_date), "MMM d, yyyy")})
                        </span>
                      </label>
                    ))}
                  </div>
                )}

              <p className="text-xs text-gray-500 mt-1">
                {seasonFilter === "all" &&
                  "Select specific seasons or leave empty to update all pricing"}
                {seasonFilter === "base" &&
                  "Will only update base pricing (no seasonal periods)"}
                {seasonFilter === "seasonal" &&
                  "Will only update seasonal pricing"}
              </p>
            </div>

            {/* Preview */}
            <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
              <Text className="text-blue-800 font-medium mb-2">Preview</Text>
              <Text className="text-blue-700 text-sm">
                This will update <strong>{matchingRowsCount}</strong> pricing
                rows
                {updateType === "amount"
                  ? ` by ${
                      updateValue >= 0 ? "adding" : "subtracting"
                    } ${getCurrencySymbol()}${Math.abs(updateValue)}`
                  : ` by ${
                      updateValue >= 0 ? "increasing" : "decreasing"
                    } ${Math.abs(updateValue)}%`}
              </Text>
              {matchingRowsCount === 0 && pricingRows.length === 0 && (
                <Text className="text-orange-600 text-sm mt-1">
                  No pricing data available for {selectedCurrency}. Please
                  select a different currency or add pricing data first.
                </Text>
              )}
              {matchingRowsCount === 0 && pricingRows.length > 0 && (
                <Text className="text-orange-600 text-sm mt-1">
                  No rows match the current selection criteria
                </Text>
              )}
            </div>
          </div>
        </FocusModal.Body>
      </FocusModal.Content>
    </FocusModal>
  );
};

export default BulkPriceUpdateModal;
