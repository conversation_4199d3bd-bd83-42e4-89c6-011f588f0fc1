import { useState, useEffect, useRef } from "react";
import {
  <PERSON><PERSON>,
  Text,
  Button,
  Toaster,
  toast,
  DropdownMenu,
  FocusModal,
} from "@camped-ai/ui";
import {
  ChevronLeft,
  ChevronRight,
  ArrowRight,
  Pencil,
  Check,
  XMark,
} from "@camped-ai/icons";
import {
  format,
  addDays,
  isSameDay,
  parseISO,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  addMonths,
  subMonths,
} from "date-fns";
import SimpleBookingDetail from "../booking/simple-booking-detail";
import CollapsibleBookingsPanel, {
  EmptyBookingsState,
} from "../../../components/room-availability/CollapsibleBookingsPanel";

type Room = {
  id: string;
  name: string;
  room_number: string;
  floor?: string;
  status?: string;
  room_config_id: string;
};

type RoomConfig = {
  id: string;
  name: string;
  title?: string;
  type?: string;
};

type Booking = {
  id: string;
  room_id: string;
  guest_name: string;
  check_in: string;
  check_out: string;
  status: "confirmed" | "pending" | "cancelled" | "completed";
  notes?: string;
};

type RoomAvailability = {
  id?: string;
  room_id: string;
  date?: string; // For backward compatibility
  from_date?: string;
  to_date?: string;
  status:
    | "available"
    | "booked"
    | "maintenance"
    | "reserved"
    | "unavailable"
    | "on_demand";
  quantity: number;
  dynamic_price?: number;
  notes?: string;
  order_id?: string;
  cart_id?: string;
};

type SimpleRoomCalendarProps = {
  hotelId: string;
};

export default function SimpleRoomCalendar({
  hotelId,
}: SimpleRoomCalendarProps) {
  // State
  const [roomConfigs, setRoomConfigs] = useState<RoomConfig[]>([]);
  const [rooms, setRooms] = useState<Room[]>([]);
  const [availability, setAvailability] = useState<RoomAvailability[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [viewMode, setViewMode] = useState<"month" | "twoWeek">("month");
  const [selectedBookingId, setSelectedBookingId] = useState<string | null>(
    null
  );

  // Context menu state
  const [contextMenuOpen, setContextMenuOpen] = useState(false);
  const [contextMenuPosition, setContextMenuPosition] = useState({
    x: 0,
    y: 0,
  });
  const [selectedCell, setSelectedCell] = useState<{
    roomId?: string; // Added roomId property
    room_config_id: string | null;
    date: Date;
    status: string;
    bookingId?: string;
    notes?: string;
  } | null>(null);

  // Modal states
  const [moveModalOpen, setMoveModalOpen] = useState(false);
  const [confirmModalOpen, setConfirmModalOpen] = useState(false);
  const [splitModalOpen, setSplitModalOpen] = useState(false);
  const [availableRooms, setAvailableRooms] = useState<Room[]>([]);
  const [selectedTargetRoomId, setSelectedTargetRoomId] = useState<string>("");
  const [actionType, setActionType] = useState<"move" | "confirm" | "split">(
    "move"
  );

  // Split booking states
  const [splitDate, setSplitDate] = useState<Date | null>(null);
  const [keepFirstPart, setKeepFirstPart] = useState(true); // Whether to keep the first part in the current room
  const [isSplitting, setIsSplitting] = useState(false); // Loading state for split operation

  // Calculate dates for the current view
  const today = new Date();

  // Calculate default dates based on view mode
  const getDefaultDates = (mode: "month" | "twoWeek") => {
    if (mode === "month") {
      return {
        start: startOfMonth(today),
        end: endOfMonth(today),
      };
    } else {
      return {
        start: today,
        end: addDays(today, 13), // 14 days total (today + 13 more days)
      };
    }
  };

  // State for date range
  const [startDate, setStartDate] = useState(getDefaultDates(viewMode).start);
  const [endDate, setEndDate] = useState(getDefaultDates(viewMode).end);

  // Create array of dates to display based on view mode
  const dates =
    viewMode === "month"
      ? eachDayOfInterval({ start: startDate, end: endDate })
      : Array.from(
          {
            length:
              Math.ceil(
                (endDate.getTime() - startDate.getTime()) /
                  (1000 * 60 * 60 * 24)
              ) + 1,
          }, // +1 to include both start and end dates
          (_, i) => addDays(startDate, i)
        );

  // Fetch data function
  const fetchData = async () => {
    if (!hotelId) return;

    setIsLoading(true);
    try {
      // Format dates for API
      const formattedStartDate = format(startDate, "yyyy-MM-dd");
      const formattedEndDate = format(endDate, "yyyy-MM-dd");

      // Fetch hotel availability data with consolidated date ranges
      const response = await fetch(
        `/admin/hotel-management/availability?hotel_id=${hotelId}&start_date=${formattedStartDate}&end_date=${formattedEndDate}&consolidate=true`
      );

      if (!response.ok) {
        throw new Error("Failed to fetch availability data");
      }

      const data = await response.json();

      // Set room configs and rooms
      setRoomConfigs(data.room_configs || []);
      setRooms(data.rooms || []);

      // Set availability data
      setAvailability(data.availability || []);

      // Set unallocated bookings from the API response
      if (data.bookings && Array.isArray(data.bookings)) {
        console.log("Unallocated bookings from API:", data.bookings);

        // Filter out orders that are present in adjustedAvailability
        const orderIdsInAvailability = new Set();
        if (data.availability && Array.isArray(data.availability)) {
          data.availability.forEach((item) => {
            if (item.order_id) {
              orderIdsInAvailability.add(item.order_id);
            }
          });
        }

        // Filter unallocated bookings to remove those present in adjustedAvailability
        const filteredUnallocatedBookings = data.bookings.filter((booking) => {
          // Extract booking ID from notes if available
          let bookingId = null;
          if (booking.notes) {
            const match = booking.notes.match(/\b(order_[a-z0-9]+)/i);
            bookingId = match ? match[1] : null;
          }

          // Also check the order_id property if it exists
          const orderId = booking.order_id || booking.id;

          // Keep the booking only if its ID is not in the availability records
          return (
            !(bookingId && orderIdsInAvailability.has(bookingId)) &&
            !(orderId && orderIdsInAvailability.has(orderId))
          );
        });

        console.log(
          "Filtered unallocated bookings (removed orders in adjustedAvailability):",
          filteredUnallocatedBookings
        );

        setLocalUnallocatedBookings(filteredUnallocatedBookings);
      }
    } catch (error) {
      toast.error("Error", {
        description: "Failed to load availability data",
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch data when component mounts or dates change
  useEffect(() => {
    fetchData();
  }, [hotelId, startDate, endDate]);

  // Listen for booking_cancelled events to refresh availability data
  useEffect(() => {
    const handleBookingCancelled = (event: Event) => {
      console.log(
        "Booking cancelled event received in SimpleRoomCalendar:",
        (event as any).detail
      );

      // Refresh the availability data
      fetchData();

      // Show a toast notification
      toast.success("Availability Updated", {
        description:
          "Room availability has been refreshed after booking cancellation",
      });
    };

    // Add event listener
    window.addEventListener("booking_cancelled", handleBookingCancelled);

    // Clean up
    return () => {
      window.removeEventListener("booking_cancelled", handleBookingCancelled);
    };
  }, [hotelId, startDate, endDate]);

  // Group rooms by room config
  const roomsByConfig = roomConfigs
    .map((config) => ({
      config,
      rooms: rooms.filter((room) => room.room_config_id === config.id),
    }))
    .filter((group) => group.rooms.length > 0);

  // State for locally managed unallocated bookings (for demo purposes)
  const [localUnallocatedBookings, setLocalUnallocatedBookings] = useState<
    any[]
  >([]);

  // Get unallocated bookings from local state
  // This is used in the unallocated bookings section
  // We're now using localUnallocatedBookings which is populated from the API's bookings property
  const unallocatedBookings = localUnallocatedBookings;

  // Get available rooms based on current availability data
  const getAvailableRoomsForDate = (
    fromDate: Date,
    toDate: Date,
    roomConfigId?: string
  ) => {
    // Format dates for comparison
    const formattedFromDate = format(fromDate, "yyyy-MM-dd");
    const formattedToDate = format(toDate, "yyyy-MM-dd");

    console.log("Finding available rooms for:", {
      fromDate: formattedFromDate,
      toDate: formattedToDate,
      roomConfigId,
    });

    // First filter rooms by room configuration if provided
    const filteredRoomsByConfig = roomConfigId
      ? rooms.filter((room) => room.room_config_id === roomConfigId)
      : rooms;

    // Now check availability for each room in the date range
    const availableRooms = filteredRoomsByConfig
      .filter((room) => {
        // Check if there are any availability records that indicate the room is not available
        const hasConflictingBooking = availability.some((item) => {
          // Skip if not for this room
          if (item.room_id !== room.id) return false;

          // Skip if the status is "available" (we're looking for conflicts)
          if (item.status === "available") return false;

          // Check for date range overlap
          if (item.from_date && item.to_date) {
            // For a conflict, check if the requested period overlaps with the booked period
            // This happens when:
            // 1. The requested start date is BEFORE the booked end date AND
            // 2. The requested end date is AFTER the booked start date
            //
            // IMPORTANT: End dates are exclusive in hotel bookings
            // - A booking from 2025-05-14 to 2025-05-15 means checkout is on 05-15
            // - A new booking can start on 2025-05-15 (same day as previous checkout)
            //
            // This handles cases like:
            // - Requested: 15-16, Booked: 15-17 (should be unavailable)
            // - Requested: 16-17, Booked: 15-17 (should be unavailable)
            // - Requested: 15-17, Booked: 16-18 (should be unavailable)
            // - Requested: 15-18, Booked: 16-17 (should be unavailable)
            // - Requested: 15-16, Booked: 14-15 (should be AVAILABLE - checkout is on 15)

            // For a proper non-overlapping booking system:
            // - If requested start date is the same as a booked end date, they don't overlap
            // - Otherwise, use standard overlap detection
            const hasOverlap =
              formattedFromDate < item.to_date &&
              formattedToDate > item.from_date;

            if (hasOverlap) {
              console.log(
                `Room ${room.id} has conflict: requested ${formattedFromDate}-${formattedToDate}, booked ${item.from_date}-${item.to_date}`
              );
            }

            return hasOverlap;
          }

          // For backward compatibility with single-date records
          if (item.date) {
            const isWithinRange =
              item.date >= formattedFromDate && item.date < formattedToDate;

            if (isWithinRange) {
              console.log(
                `Room ${room.id} has conflict on single date: ${item.date}`
              );
            }

            return isWithinRange;
          }

          return false;
        });

        // Room is available if there are no conflicting bookings
        const isAvailable = !hasConflictingBooking;

        if (isAvailable) {
          console.log(
            `Room ${room.id} (${room.room_number}) is AVAILABLE for ${formattedFromDate} to ${formattedToDate}`
          );
        } else {
          console.log(
            `Room ${room.id} (${room.room_number}) is NOT AVAILABLE for ${formattedFromDate} to ${formattedToDate}`
          );
        }

        return isAvailable;
      })
      .map((room) => ({
        ...room,
        name: room.name || `Room ${room.room_number}`,
      }));

    console.log(
      `Found ${availableRooms.length} available rooms out of ${filteredRoomsByConfig.length} matching rooms`
    );
    return availableRooms;
  };

  // Handle right-click on a booking cell
  const handleContextMenu = (
    e: React.MouseEvent,
    roomId: string,
    date: Date,
    status: string,
    bookingId?: string,
    notes?: string
  ) => {
    e.preventDefault();
    setContextMenuPosition({ x: e.clientX, y: e.clientY });

    // Find the room to get its room_config_id
    const room = rooms.find((r) => r.id === roomId);
    const room_config_id = room?.room_config_id || null;

    setSelectedCell({ roomId, date, status, bookingId, notes, room_config_id });
    setContextMenuOpen(true);
  };

  // Handle move to unallocated
  const handleMoveToUnallocated = async () => {
    if (!selectedCell) return;

    setContextMenuOpen(false);

    // Get the booking details from the notes
    const bookingId = selectedCell.bookingId;
    if (!bookingId) {
      toast.error("Error", {
        description: "No booking ID found for this cell",
      });
      return;
    }

    // Find the inventory record for this cell
    const formattedDate = format(selectedCell.date, "yyyy-MM-dd");
    const inventoryRecord = availability.find((item: any) => {
      if (item.room_id !== selectedCell.roomId) return false;

      // Check if this is the right date
      if (item.from_date && item.to_date) {
        return formattedDate >= item.from_date && formattedDate < item.to_date;
      }

      return item.date === formattedDate;
    });

    if (!inventoryRecord) {
      toast.error("Error", { description: "Could not find booking details" });
      return;
    }

    try {
      // Call the API to unallocate the room
      const response = await fetch(
        `/admin/hotel-management/room-inventory/unallocate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            room_id: selectedCell.roomId,
            order_id: bookingId,
            from_date: inventoryRecord.from_date || formattedDate,
            to_date:
              inventoryRecord.to_date ||
              addDays(new Date(formattedDate), 1).toISOString().split("T")[0],
            inventory_id: inventoryRecord.id || "",
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to unallocate room");
      }

      // Refresh the data
      fetchData();

      toast.success("Success", {
        description: "Room has been unallocated successfully",
      });

      // Log success for debugging
      console.log(
        "Room unallocated successfully - check unallocated bookings section"
      );
    } catch (error) {
      console.error("Error unallocating room:", error);

      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to unallocate room",
      });

      // Fallback to local state for demo if API call fails
      const room = rooms.find((r) => r.id === selectedCell.roomId);
      const roomConfigId = room?.room_config_id || "";
      const roomConfig = roomConfigs.find((c) => c.id === roomConfigId);

      // Create a new unallocated booking entry
      const newUnallocatedBooking = {
        id: `unallocated_${Date.now()}`,
        status: "reserved",
        room_id: null,
        from_date: inventoryRecord.from_date || formattedDate,
        to_date:
          inventoryRecord.to_date ||
          addDays(new Date(formattedDate), 1).toISOString().split("T")[0],
        notes:
          inventoryRecord.notes ||
          `order_${bookingId} room_config_id:${roomConfigId} ${
            roomConfig ? `room_type:${roomConfig.title || roomConfig.name}` : ""
          }`,
        // Add room configuration information for filtering
        room_config_id: roomConfigId,
      };

      // Add to local unallocated bookings
      setLocalUnallocatedBookings((prev) => [...prev, newUnallocatedBooking]);

      toast.info("Info", {
        description: "Using local state as fallback (demo mode)",
      });
    }
  };

  // Handle confirm booking
  const handleConfirmBooking = async () => {
    if (!selectedCell) return;

    setContextMenuOpen(false);
    setActionType("confirm");
    setConfirmModalOpen(true);
  };

  // Execute the move action
  const executeMove = async () => {
    console.log("executeMove called with:", {
      selectedCell,
      selectedTargetRoomId,
    });

    if (!selectedCell || !selectedTargetRoomId) {
      console.error("Missing required data:", {
        selectedCell: !!selectedCell,
        selectedTargetRoomId: !!selectedTargetRoomId,
      });
      return;
    }

    const bookingId = selectedCell.bookingId;
    console.log("Booking ID:", bookingId);

    if (!bookingId) {
      console.error("No booking ID found for this cell");
      toast.error("Error", {
        description: "No booking ID found for this cell",
      });
      return;
    }

    // Check if this is from the unallocated pool
    console.log("Checking if booking is from unallocated pool:", bookingId);
    console.log("localUnallocatedBookings:", localUnallocatedBookings);

    const isFromUnallocated = localUnallocatedBookings.some((booking) => {
      const matchesOrderId = booking.order_id === bookingId;
      const matchesNotes = booking.notes && booking.notes.includes(bookingId);
      console.log("Booking check:", {
        booking,
        matchesOrderId,
        matchesNotes,
        result: matchesOrderId || matchesNotes,
      });
      return matchesOrderId || matchesNotes;
    });

    try {
      // Find the inventory record for this cell to get date range
      const formattedDate = format(selectedCell.date, "yyyy-MM-dd");
      let fromDate = formattedDate;
      let toDate = addDays(new Date(formattedDate), 1)
        .toISOString()
        .split("T")[0];
      let inventoryId = "";

      // If from unallocated pool, get dates from the unallocated booking
      if (isFromUnallocated) {
        const unallocatedBooking = localUnallocatedBookings.find(
          (booking) =>
            booking.order_id === bookingId ||
            (booking.notes && booking.notes.includes(bookingId))
        );
        if (unallocatedBooking) {
          fromDate = unallocatedBooking.from_date;
          toDate = unallocatedBooking.to_date;
          inventoryId = unallocatedBooking.id;
        }
      } else {
        // Otherwise, find the inventory record for this cell
        const inventoryRecord = availability.find((item: any) => {
          if (item.room_id !== selectedCell.roomId) return false;

          // Check if this is the right date
          if (item.from_date && item.to_date) {
            return (
              formattedDate >= item.from_date && formattedDate < item.to_date
            );
          }

          return item.date === formattedDate;
        });

        if (inventoryRecord) {
          fromDate = inventoryRecord.from_date || formattedDate;
          toDate =
            inventoryRecord.to_date ||
            addDays(new Date(formattedDate), 1).toISOString().split("T")[0];
          inventoryId = inventoryRecord.id || "";
        }
      }

      // Call the API to allocate the room
      const requestBody = {
        inventory_item_id: selectedTargetRoomId, // This is the correct field name expected by the API
        room_id: selectedTargetRoomId, // Keep for backward compatibility
        order_id: bookingId,
        from_date: fromDate,
        to_date: toDate,
        status: "booked",
        inventory_id: inventoryId,
      };

      console.log("Sending API request to allocate room:", {
        url: `/admin/hotel-management/room-inventory/allocate`,
        method: "POST",
        body: requestBody,
      });

      const response = await fetch(
        `/admin/hotel-management/room-inventory/allocate`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify(requestBody),
        }
      );

      console.log("API response status:", response.status, response.statusText);

      if (!response.ok) {
        let errorMessage = "Failed to allocate room";
        try {
          const errorData = await response.json();
          console.error("API error response:", errorData);
          errorMessage = errorData.message || errorMessage;
        } catch (parseError) {
          console.error("Error parsing API error response:", parseError);
        }
        throw new Error(errorMessage);
      }

      // Log the successful response
      try {
        const responseData = await response.json();
        console.log("API success response:", responseData);

        // If from unallocated pool, remove from local state
        if (isFromUnallocated) {
          console.log(
            "Removing booking from unallocated pool after successful API call:",
            bookingId
          );
          setLocalUnallocatedBookings((prev) => {
            const filtered = prev.filter(
              (booking) =>
                !(
                  booking.order_id === bookingId ||
                  (booking.notes && booking.notes.includes(bookingId))
                )
            );
            console.log("Filtered unallocated bookings:", filtered);
            return filtered;
          });
        }

        // Refresh the data
        console.log("Refreshing data after successful allocation");
        fetchData();

        toast.success("Success", {
          description: selectedCell.roomId
            ? "Room has been moved successfully"
            : "Room has been assigned successfully",
        });

        // Close the modal and reset state
        setMoveModalOpen(false);
        setSelectedTargetRoomId("");
        return; // Exit early since we've handled everything
      } catch (parseError) {
        console.error("Error parsing API success response:", parseError);
      }

      // This code is now handled in the success response handler above
    } catch (error) {
      console.error("Error allocating room:", error);

      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to allocate room",
      });

      // Fallback to local state for demo if API call fails
      console.log("API call failed, using local state fallback");

      if (isFromUnallocated) {
        // Remove from unallocated pool
        console.log("Removing booking from unallocated pool:", bookingId);
        setLocalUnallocatedBookings((prev) => {
          const filtered = prev.filter(
            (booking) =>
              !(
                booking.order_id === bookingId ||
                (booking.notes && booking.notes.includes(bookingId))
              )
          );
          console.log("Filtered unallocated bookings:", filtered);
          return filtered;
        });
      }

      // Add a local entry to simulate the room allocation
      // Get the dates from the unallocated booking if available
      let localFromDate = format(selectedCell.date, "yyyy-MM-dd");
      let localToDate = addDays(new Date(localFromDate), 1)
        .toISOString()
        .split("T")[0];

      if (isFromUnallocated) {
        const unallocatedBooking = localUnallocatedBookings.find(
          (booking) =>
            booking.order_id === bookingId ||
            (booking.notes && booking.notes.includes(bookingId))
        );

        if (
          unallocatedBooking &&
          unallocatedBooking.from_date &&
          unallocatedBooking.to_date
        ) {
          localFromDate = unallocatedBooking.from_date;
          localToDate = unallocatedBooking.to_date;
        }
      }

      const localAllocation: any = {
        id: `local_allocation_${Date.now()}`,
        room_id: selectedTargetRoomId,
        inventory_item_id: selectedTargetRoomId,
        order_id: bookingId,
        from_date: localFromDate,
        to_date: localToDate,
        status: "reserved_unassigned",
        notes: `Local allocation for order ${bookingId} on ${new Date().toISOString()}`,
        quantity: 0,
        available_quantity: 0,
      };

      console.log("Adding local allocation to availability:", localAllocation);

      // Add to local availability
      setAvailability((prev) => [...prev, localAllocation as any]);

      toast.info("Info", {
        description: "Using local state as fallback (demo mode)",
      });

      // Close the modal and reset state in the error case too
      setMoveModalOpen(false);
      setSelectedTargetRoomId("");
    }

    // This ensures the modal is closed in all cases
  };

  // Execute the confirm action
  const executeConfirm = async () => {
    if (!selectedCell) return;

    const bookingId = selectedCell.bookingId;
    if (!bookingId) {
      toast.error("Error", {
        description: "No booking ID found for this cell",
      });
      return;
    }

    try {
      // Call the API to confirm the booking
      const response = await fetch(
        `/admin/hotel-management/orders/${bookingId}/confirm`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to confirm booking");
      }

      // Close the modal
      setConfirmModalOpen(false);

      // Refresh the data
      fetchData();

      toast.success("Success", {
        description: "Booking has been confirmed successfully",
      });
    } catch (error) {
      console.error("Error confirming booking:", error);

      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to confirm booking",
      });
    }
  };

  // Handle split booking
  const handleSplitBooking = async () => {
    if (!selectedCell || !splitDate) return;

    // Get the booking details
    const bookingId = selectedCell.bookingId;
    if (!bookingId) {
      toast.error("Error", {
        description: "No booking ID found for this cell",
      });
      return;
    }

    // Find the inventory record for this cell
    const formattedCellDate = format(selectedCell.date, "yyyy-MM-dd");
    const inventoryRecord = availability.find((item: any) => {
      if (item.room_id !== selectedCell.roomId) return false;

      // Check if this is the right date
      if (item.from_date && item.to_date) {
        return (
          formattedCellDate >= item.from_date &&
          formattedCellDate < item.to_date
        );
      }

      return item.date === formattedCellDate;
    });

    if (!inventoryRecord) {
      toast.error("Error", { description: "Could not find booking details" });
      return;
    }

    // Verify this is a multi-day booking
    if (inventoryRecord.from_date && inventoryRecord.to_date) {
      const fromDate = new Date(inventoryRecord.from_date);
      const toDate = new Date(inventoryRecord.to_date);
      const daysDiff = Math.round(
        (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
      );

      if (daysDiff <= 1) {
        toast.error("Error", {
          description: "Booking must span multiple days to be split",
        });
        return;
      }

      // Verify the split date is between the booking dates (but not on the exact boundaries)
      const splitDateTime = splitDate.getTime();
      const minDate = addDays(fromDate, 1).getTime();
      const maxDate = addDays(toDate, -1).getTime();

      if (splitDateTime < minDate) {
        toast.error("Error", {
          description:
            "Split date must be at least one day after check-in date",
        });
        return;
      }

      if (splitDateTime > maxDate) {
        toast.error("Error", {
          description:
            "Split date must be at least one day before check-out date",
        });
        return;
      }
    } else {
      toast.error("Error", {
        description: "Cannot determine booking duration",
      });
      return;
    }

    // Format the split date
    const formattedSplitDate = format(splitDate, "yyyy-MM-dd");

    // Set loading state
    setIsSplitting(true);

    try {
      // Call the API to split the booking
      try {
        const response = await fetch(
          `/admin/hotel-management/orders/${bookingId}/split`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({
              split_date: formattedSplitDate,
              keep_first_part: keepFirstPart,
            }),
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to split booking");
        }

        const result = await response.json();
        console.log("Split booking API response:", result);

        // If the API call fails, fall back to client-side simulation
        if (!result.first_part_id || !result.second_part_id) {
          throw new Error("API response missing split booking IDs");
        }

        // The backend will handle updating the room inventory and order metadata
        // We just need to refresh the data to show the changes
      } catch (apiError) {
        console.error("Error calling split booking API:", apiError);

        // Fall back to client-side simulation if the API call fails
        console.log("Falling back to client-side simulation");

        // Format dates for the split
        const fromDate = inventoryRecord.from_date
          ? new Date(inventoryRecord.from_date)
          : selectedCell.date;
        const toDate = inventoryRecord.to_date
          ? new Date(inventoryRecord.to_date)
          : addDays(selectedCell.date, 1);

        // Create two booking parts
        const firstPartFromDate = format(fromDate, "yyyy-MM-dd");
        const firstPartToDate = formattedSplitDate;

        const secondPartFromDate = formattedSplitDate;
        const secondPartToDate = format(toDate, "yyyy-MM-dd");

        // Find the room to get its configuration
        const room = rooms.find((r) => r.id === selectedCell.roomId);
        const roomConfigId = room?.room_config_id || "";

        // Find the room configuration
        const roomConfig = roomConfigs.find((c) => c.id === roomConfigId);

        // Create a new unallocated booking entry for the second part
        const newUnallocatedBooking = {
          id: `unallocated_${Date.now()}`,
          status: "reserved",
          room_id: keepFirstPart ? null : selectedCell.roomId,
          from_date: secondPartFromDate,
          to_date: secondPartToDate,
          notes: `${
            inventoryRecord.notes || `order_${bookingId}_part2`
          } (Split from ${bookingId}) room_config_id:${roomConfigId} ${
            roomConfig ? `room_type:${roomConfig.title || roomConfig.name}` : ""
          }`,
          room_config_id: roomConfigId,
          parent_booking_id: bookingId,
        };

        // Add to local unallocated bookings if the second part should be unallocated
        if (keepFirstPart) {
          setLocalUnallocatedBookings((prev) => [
            ...prev,
            newUnallocatedBooking,
          ]);
        }

        // Create a new unallocated booking entry for the first part if needed
        if (!keepFirstPart) {
          const firstPartBooking = {
            id: `unallocated_${Date.now() + 1}`,
            status: "reserved",
            room_id: null,
            from_date: firstPartFromDate,
            to_date: firstPartToDate,
            notes: `${
              inventoryRecord.notes || `order_${bookingId}_part1`
            } (Split) room_config_id:${roomConfigId} ${
              roomConfig
                ? `room_type:${roomConfig.title || roomConfig.name}`
                : ""
            }`,
            room_config_id: roomConfigId,
            parent_booking_id: bookingId,
          };

          setLocalUnallocatedBookings((prev) => [...prev, firstPartBooking]);
        }
      }

      // Close the modal and refresh data
      setSplitModalOpen(false);
      fetchData();

      toast.success("Success", {
        description:
          "Booking has been split successfully. The calendar will refresh to show the changes.",
      });
    } catch (error) {
      console.error("Error splitting booking:", error);
      toast.error("Error", {
        description:
          error instanceof Error ? error.message : "Failed to split booking",
      });
    } finally {
      setIsSplitting(false);
    }
  };

  // Get room status for a specific date
  const getRoomStatus = (roomId: string, date: Date) => {
    try {
      // Validate the date
      if (!date || !(date instanceof Date)) {
        return "unavailable";
      }

      // Format the date
      const dateStr = format(date, "yyyy-MM-dd");

      // Find the availability record for this room and date
      // With date ranges, we need to check if the date falls within from_date and to_date
      const availabilityRecord = availability.find((item) => {
        try {
          // Validate the room ID
          if (item.room_id !== roomId) return false;

          // If we have from_date and to_date fields (date ranges)
          if (item.from_date && item.to_date) {
            // Check if this is a 1-day period by comparing from_date and to_date
            const fromDate = parseISO(item.from_date);
            const toDate = parseISO(item.to_date);
            const diffInDays = Math.round(
              (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
            );
            const isOneDayPeriod = diffInDays === 1;

            // Special handling for 1-day periods (where to_date is 1 day after from_date due to noon-to-noon concept)
            if (isOneDayPeriod) {
              // For 1-day periods, match the from_date exactly
              // This ensures 1-day bookings are properly displayed
              return dateStr === item.from_date;
            }

            // If not a 1-day period, check if the date falls within the range (inclusive of from_date, exclusive of to_date)
            return dateStr >= item.from_date && dateStr < item.to_date;
          }

          // Fallback to date field for backward compatibility
          return item.date === dateStr;
        } catch (err) {
          // Silently handle errors
          return false;
        }
      });

      console.log("Availability record:", availabilityRecord);

      // Return the status from the availability record, or "unavailable" if not found
      return availabilityRecord?.status || "unavailable";
    } catch (err) {
      // Silently handle errors
      return "unavailable";
    }
  };

  // Function to check if a date is a weekend (Saturday or Sunday)
  const isWeekend = (date: Date) => {
    const day = date.getDay();
    return day === 0 || day === 6; // 0 is Sunday, 6 is Saturday
  };

  // This function is now used directly in the button onClick handlers

  // Noon-to-noon concept is now implemented directly in the CSS positioning

  return (
    <div className="flex h-full">
      <div className="calendar-container w-full">
        <style>{`
        /* Make booking cells clickable */
        .booking-cell-clickable {
          cursor: pointer !important;
          pointer-events: auto !important;
          user-select: none;
          -webkit-user-select: none;
        }

        .booking-cell-clickable:hover {
          transform: translateY(-1px);
          box-shadow: 0 3px 6px rgba(0,0,0,0.15) !important;
          z-index: 40 !important;
        }

        .booking-cell-clickable:active {
          transform: translateY(0);
          box-shadow: 0 1px 2px rgba(0,0,0,0.1) !important;
        }
        .calendar-container {
          width: 100%;
          padding-bottom: 24px;
          font-family: 'Inter', system-ui, -apple-system, sans-serif;
          position: relative;
        }
        .calendar-inner {
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          position: relative;
        }
        .scroll-container {
          width: 100%;
          overflow-x: auto;
          overflow-y: visible;
        }
        .scrollable-content {
          /* Calculate width based on consistent full-width days:
             200px for room names column
             + 80px for each day (full-width)
          */
          min-width: ${200 + (dates.length - 1) * 80}px;
          width: max-content;
        }
        .calendar-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 18px 20px;
          background-color: white;
          border-bottom: 1px solid #e5e7eb;
          position: sticky;
          top: 0;
          z-index: 10;
        }
        .header-left {
          display: flex;
          align-items: center;
          gap: 12px;
        }
        .header-right {
          display: flex;
          align-items: center;
          gap: 16px;
        }
        .calendar-grid {
          display: grid;
          /* First column is 200px for room names */
          /* For dates: all days are full-width (80px) to ensure consistent display */
          grid-template-columns: 200px repeat(${(dates.length - 1) * 2}, 40px);
          border-bottom: 1px solid #e5e7eb;
          position: relative;
          background-color: #f3f4f6;
        }
        .fixed-column {
          position: sticky;
          left: 0;
          z-index: 20;
          background-color: white;
          box-shadow: 2px 0 4px rgba(0,0,0,0.05);
        }
        .first-column {
          position: sticky;
          left: 0;
          z-index: 20;
          background-color: white;
          box-shadow: 2px 0 4px rgba(0,0,0,0.05);
        }
        .calendar-cell {
          padding: 8px;
          border-right: 1px solid #e5e7eb;

          transition: background-color 0.2s ease;
          position: relative;
          gridColumn: 2,

        }
        .calendar-cell.header {
          background-color: #f8fafc;
          font-weight: 600;
          text-align: center;
          padding: 10px 8px;
          font-size: 0.85rem;
          color: #475569;
        }

        .calendar-cell.today {
          background-color: #eff6ff;
          font-weight: bold;
        }
        .room-group-header {
          grid-column: 1 / -1;
          background-color: #f3f4f6;
          padding: 8px 12px;
          font-weight: 600;
          border-bottom: 1px solid #e5e7eb;
          position: sticky;
          left: 0;
          z-index: 4;
          font-size: 0.9rem;
          color: #1e293b;
        }
        .summary-row {
          grid-column: 1 / -1;
          background-color: #f9fafb;
          border-bottom: 1px solid #e5e7eb;
          border-top: 1px solid #e5e7eb;
          font-weight: 500;
        }
        .summary-cell {
          padding: 8px;
          border-right: 1px solid #e5e7eb;
          text-align: center;
          font-size: 0.8rem;
          color: #475569;

        }
        .summary-label {
          font-weight: 600;
          padding: 8px 12px;
          border-right: 1px solid #e5e7eb;
          color: #1e293b;
        }
        .room-cell {
          padding: 8px 12px;
          border-right: 1px solid #e5e7eb;
          border-bottom: 1px solid #e5e7eb;
          font-weight: 500;
          color: #1e293b;
          font-size: 0.85rem;
          display: flex;
          flex-direction: column;
          gap: 2px;
        }
        .status-cell {
          padding: 4px;
          text-align: center;
          font-size: 0.8rem;
          display: flex;
          justify-content: center;
          align-items: center;
          height: 28px;
          position: relative;
          transition: all 0.2s ease;
          border-radius: 0;
          /* Ensure proper rendering for noon-to-noon concept */
          box-sizing: border-box;
          overflow: visible;
          /* Noon-to-noon concept with borders */
          border-left-width: 2px !important;
          border-right-width: 2px !important;
        }
        .status-cell:hover {
          box-shadow: inset 0 0 0 2px #3b82f6;
          z-index: 5;
        }
        .maintenance-span {
          background-color: #fee2e2;
          border: 1px solid #f87171;
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: 4;
          box-shadow: 0 1px 2px rgba(0,0,0,0.05);
          transition: transform 0.2s ease, box-shadow 0.2s ease;
          /* No rounded corners for noon-to-noon concept */
          border-radius: 0;
          /* Noon-to-noon concept with borders */
          border-left-color: #f97316 !important;
          border-right-color: #f97316 !important;
        }
        .maintenance-span:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status-available {
          background-color: white;
          border: 1px solid white;
          display: flex;
          justify-content: center;
          align-items: center;
          /* Noon-to-noon concept with borders */
          border-left-color: white !important;
          border-right-color: white !important;
        }
        .status-booked {
          background-color: #dcfce7;
          background-image: linear-gradient(to bottom, #dcfce7, #d1fae5);
          border: 1px solid #4ade80;
          display: flex;
          justify-content: center;
          align-items: center;
          /* Noon-to-noon concept with borders */
          border-left-color: #22c55e !important;
          border-right-color: #22c55e !important;
        }
        .status-maintenance {
          background-color: white;
          border: 1px solid #f87171;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .status-reserved {
          background-color: white;
          border: 1px solid #34d399;
          display: flex;
          justify-content: center;
          align-items: center;
          /* Noon-to-noon concept with borders */
          border-left-color: #34d399 !important;
          border-right-color: #34d399 !important;
        }
        .status-unavailable {
          background-color: #f1f5f9;
          border: 1px solid #cbd5e1;
          display: flex;
          justify-content: center;
          align-items: center;
        }
        .status-on_demand {
          background-color: #fef3c7;
          background-image: linear-gradient(to bottom, #fef9c3, #fef3c7);
          border: 1px solid #fbbf24;
          display: flex;
          justify-content: center;
          align-items: center;
          /* Noon-to-noon concept with borders */
          border-left-color: #f59e0b !important;
          border-right-color: #f59e0b !important;
        }
        .booking-cell {
          padding: 4px 10px;
          font-size: 0.8rem;
          font-weight: 500;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          z-index: 30 !important; /* Increased z-index to ensure it's above other elements */
          box-shadow: 0 1px 3px rgba(0,0,0,0.1);
          transition: all 0.2s ease;
          border-radius: 4px;
          /* Ensure proper rendering for noon-to-noon concept */
          box-sizing: border-box;
          overflow: visible;
          /* Styling for booking cells */
          background-color: #ffb8b8;
          border: 1px solid #ff8080;
          cursor: pointer !important;
          pointer-events: auto !important;
          position: relative;
          appearance: none;
          -webkit-appearance: none;
          margin: 0;
          font-family: inherit;
        }

        .booking-cell:hover {
          transform: translateY(-1px);
          box-shadow: 0 3px 6px rgba(0,0,0,0.15);
          z-index: 40 !important;
        }

        .booking-cell:active {
          transform: translateY(0);
          box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .selected-booking {
          background-color: #3b82f6 !important;
          color: white !important;
          border: 2px solid #2563eb !important;
          transform: translateY(-1px);
          box-shadow: 0 3px 6px rgba(0,0,0,0.2) !important;
        }

        /* Status-specific styling */
        .booking-confirmed {
          background-color: #ffb8b8;
          border-color: #ff8080;
        }

        .booking-checked_in {
          background-color: #bfdbfe;
          border-color: #93c5fd;
        }

        .booking-checked_out {
          background-color: #d1fae5;
          border-color: #6ee7b7;
        }

        .booking-canceled {
          background-color: #e5e7eb;
          border-color: #d1d5db;
          color: #6b7280;
        }
        /* Removed duplicate hover style */
        .booking-confirmed {
          background-color: #dbeafe;
          background-image: linear-gradient(to bottom, #eff6ff, #dbeafe);
          border: 1px solid #93c5fd;
        }
        .booking-pending {
          background-color: #fef3c7;
          background-image: linear-gradient(to bottom, #fef9c3, #fef3c7);
          border: 1px solid #fcd34d;
        }
        .booking-cancelled {
          background-color: #fecaca;
          background-image: linear-gradient(to bottom, #fee2e2, #fecaca);
          border: 1px solid #f87171;
        }
        .booking-completed {
          background-color: #d1fae5;
          background-image: linear-gradient(to bottom, #ecfdf5, #d1fae5);
          border: 1px solid #6ee7b7;
        }
      `}</style>

        <Toaster />

        {/* Calendar header */}
        <div className="flex flex-col md:flex-row justify-between items-center gap-4 mb-4 p-4 bg-white rounded-lg shadow-sm">
          <div className="flex items-center gap-3">
            <Button
              variant="secondary"
              size="small"
              className="hover:bg-gray-100 transition-colors shadow-sm"
              onClick={() => {
                if (viewMode === "month") {
                  setStartDate(startOfMonth(subMonths(startDate, 1)));
                  setEndDate(endOfMonth(subMonths(endDate, 1)));
                } else {
                  setStartDate(addDays(startDate, -30));
                  setEndDate(addDays(endDate, -30));
                }
              }}
            >
              <ChevronLeft className="w-4 h-4" />
              <ChevronLeft className="w-4 h-4 -ml-2" />
            </Button>

            <Heading
              level="h3"
              className="text-xl font-semibold text-gray-800 px-3 py-1 bg-gray-50 rounded-md"
            >
              {format(startDate, "MMM dd")} - {format(endDate, "MMM dd, yyyy")}
            </Heading>

            <Button
              variant="secondary"
              size="small"
              className="hover:bg-gray-100 transition-colors shadow-sm"
              onClick={() => {
                if (viewMode === "month") {
                  setStartDate(startOfMonth(addMonths(startDate, 1)));
                  setEndDate(endOfMonth(addMonths(endDate, 1)));
                } else {
                  setStartDate(addDays(startDate, 30));
                  setEndDate(addDays(endDate, 30));
                }
              }}
            >
              <ChevronRight className="w-4 h-4" />
              <ChevronRight className="w-4 h-4 -ml-2" />
            </Button>
          </div>
        </div>

        {/* Instructions for users */}
        <div className="bg-blue-50 p-4 mb-4 rounded-lg border border-blue-200">
          <div className="flex items-start">
            <div className="flex-shrink-0 pt-0.5">
              <svg
                className="h-5 w-5 text-blue-400"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
                fill="currentColor"
              >
                <path
                  fillRule="evenodd"
                  d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z"
                  clipRule="evenodd"
                />
              </svg>
            </div>
            <div className="ml-3">
              <h3 className="text-sm font-medium text-blue-800">
                Click on any booking in the calendar to view details
              </h3>
              <div className="mt-2 text-sm text-blue-700">
                <p>
                  Bookings appear as colored chips in the calendar. Click on any
                  booking to see its details in a side panel. Right-click on a
                  booking to see available actions.
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="calendar-inner">
          <div className="scroll-container">
            <div className="scrollable-content">
              {/* Legend */}
              <div
                className="calendar-legend"
                style={{
                  padding: "8px 12px",
                  borderBottom: "1px solid #e5e7eb",
                  display: "flex",
                  gap: "16px",
                  backgroundColor: "#f8fafc",
                  position: "sticky",
                  top: 0,
                  zIndex: 10,
                }}
              >
                <div
                  className="legend-item"
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                  title="Room is booked for a guest"
                >
                  <div
                    style={{
                      fontSize: "1rem",
                      width: "20px",
                      height: "20px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    👤
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Booked
                  </span>
                </div>
                <div
                  className="legend-item"
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                  title="Room is under maintenance and unavailable"
                >
                  <div
                    style={{
                      fontSize: "1rem",
                      width: "20px",
                      height: "20px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    🔧
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Maintenance
                  </span>
                </div>
                <div
                  className="legend-item"
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                  title="Room is reserved but not confirmed"
                >
                  <div
                    style={{
                      fontSize: "1rem",
                      width: "20px",
                      height: "20px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  >
                    ⛔
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Reserved
                  </span>
                </div>
                <div
                  className="legend-item"
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                  title="Room is available on customer demand"
                >
                  <div
                    style={{
                      fontSize: "1rem",
                      width: "20px",
                      height: "20px",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                      backgroundColor: "#fef3c7",
                      borderRadius: "2px",
                    }}
                  >
                    📞
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    On Demand
                  </span>
                </div>
                <div
                  className="legend-item"
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                  title="Room is available for booking"
                >
                  <div
                    style={{
                      width: "20px",
                      height: "20px",
                      backgroundColor: "white",
                      border: "1px solid white",
                      display: "flex",
                      alignItems: "center",
                      justifyContent: "center",
                    }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700">
                    Available
                  </span>
                </div>
                <div
                  className="legend-item"
                  style={{ display: "flex", alignItems: "center", gap: "8px" }}
                  title="Room is unavailable for booking"
                >
                  <div
                    style={{
                      width: "20px",
                      height: "20px",
                      backgroundColor: "#f1f5f9",
                      border: "1px solid #cbd5e1",
                    }}
                  ></div>
                  <span className="text-sm font-medium text-gray-700">
                    Unavailable
                  </span>
                </div>
              </div>

              {/* Days header */}
              <div className="calendar-grid">
                <div className="calendar-cell header fixed-column">Rooms</div>
                {dates.map((date, index) => {
                  // First day (index 0) and last day (index dates.length-1) should span 1 column
                  // All other days should span 2 columns
                  const isFirstDay = index === 0;
                  const isLastDay = index === dates.length - 1;
                  const columnSpan = isFirstDay || isLastDay ? 1 : 2;

                  return (
                    <div
                      key={index}
                      className={`calendar-cell header ${
                        isSameDay(date, new Date())
                          ? "today"
                          : isWeekend(date)
                          ? "weekend"
                          : ""
                      }`}
                      style={{
                        gridColumn: `span ${columnSpan}`,
                      }}
                    >
                      <div style={{ fontSize: "0.75rem", fontWeight: "500" }}>
                        {format(date, "EEE")}
                      </div>
                      <div>{format(date, "dd")}</div>
                    </div>
                  );
                })}
              </div>

              {/* Summary row - Availability */}

              <div className="calendar-grid">
                <div className="summary-label first-column">
                  Available Rooms
                </div>
                {dates.map((date, index) => {
                  const availableRooms = rooms.filter((room) => {
                    // Get status using the same logic as getRoomStatus function
                    const status = getRoomStatus(room.id, date);
                    return status === "available";
                  }).length;

                  // First day (index 0) and last day (index dates.length-1) should span 1 column
                  // All other days should span 2 columns
                  const isFirstDay = index === 0;
                  const isLastDay = index === dates.length - 1;
                  const columnSpan = isFirstDay || isLastDay ? 1 : 2;

                  return (
                    <div
                      key={index}
                      className="summary-cell"
                      style={{
                        gridColumn: `span ${columnSpan}`,
                        backgroundColor:
                          availableRooms === 0
                            ? "#fecaca"
                            : availableRooms < rooms.length / 3
                            ? "#fed7aa"
                            : availableRooms < rooms.length / 2
                            ? "#fef3c7"
                            : "#d1fae5",
                      }}
                      title={`${availableRooms} out of ${
                        rooms.length
                      } rooms available on ${format(date, "MMM dd, yyyy")}`}
                    >
                      {availableRooms} / {rooms.length}
                    </div>
                  );
                })}
              </div>

              {/* Room groups */}
              {isLoading ? (
                <div style={{ padding: "40px", textAlign: "center" }}>
                  <Text>Loading...</Text>
                </div>
              ) : roomsByConfig.length === 0 ? (
                <div style={{ padding: "40px", textAlign: "center" }}>
                  <Text>No rooms found</Text>
                </div>
              ) : (
                roomsByConfig.map(({ config, rooms }) => (
                  <div key={config.id}>
                    {/* Room type header */}
                    <div className="room-group-header first-column">
                      {config.title} ({rooms.length})
                    </div>

                    {/* Rooms in this group */}
                    {rooms.map((room) => (
                      <div key={room.id} className="calendar-grid">
                        {/* Room info */}
                        <div className="room-cell fixed-column">
                          <div
                            style={{ fontWeight: "500", fontSize: "0.85rem" }}
                          >
                            {room.room_number}{" "}
                            {room.floor && `• Floor ${room.floor}`}
                          </div>
                        </div>

                        {/* Room availability cells */}
                        <div
                          style={{
                            display: "contents",
                            position: "relative",
                            pointerEvents: "auto",
                          }}
                        >
                          {/* Process the dates to identify maintenance spans */}
                          {(() => {
                            const elements = [];
                            let skipCount = 0;

                            for (
                              let dateIndex = 0;
                              dateIndex < dates.length;
                              dateIndex++
                            ) {
                              if (skipCount > 0) {
                                skipCount--;
                                continue;
                              }

                              const date = dates[dateIndex];
                              const dateStr = format(date, "yyyy-MM-dd");
                              const status = getRoomStatus(room.id, date);

                              // Find the inventory record for this date
                              // With date ranges, we need to check if the date falls within from_date and to_date
                              const inventoryRecord = availability.find(
                                (item) => {
                                  if (item.room_id !== room.id) return false;

                                  // If we have from_date and to_date fields (date ranges)
                                  if (item.from_date && item.to_date) {
                                    // Check if this is a 1-day period
                                    const fromDate = parseISO(item.from_date);
                                    const toDate = parseISO(item.to_date);
                                    const diffInDays = Math.round(
                                      (toDate.getTime() - fromDate.getTime()) /
                                        (1000 * 60 * 60 * 24)
                                    );
                                    const isOneDayPeriod = diffInDays === 1;

                                    // Special handling for 1-day periods
                                    if (isOneDayPeriod) {
                                      return dateStr === item.from_date;
                                    }

                                    // For multi-day periods, check if the date falls within the range (inclusive of from_date, exclusive of to_date)
                                    return (
                                      dateStr >= item.from_date &&
                                      dateStr < item.to_date
                                    );
                                  }

                                  // Fallback to date field for backward compatibility
                                  return item.date === dateStr;
                                }
                              );

                              // Skip if the status is unavailable
                              if (status === "unavailable") continue;

                              // If this is a maintenance day, check for consecutive maintenance days
                              if (status === "maintenance") {
                                // Find how many consecutive days are in maintenance
                                let maintenanceSpan = 1;
                                let nextIndex = dateIndex + 1;

                                // Find the current inventory record for this maintenance period
                                const currentMaintenanceRecord =
                                  inventoryRecord;

                                // If we have a record with from_date and to_date, use that to determine the span
                                if (
                                  currentMaintenanceRecord &&
                                  currentMaintenanceRecord.from_date &&
                                  currentMaintenanceRecord.to_date
                                ) {
                                  // Calculate days between from_date and to_date
                                  const fromDate = parseISO(
                                    currentMaintenanceRecord.from_date
                                  );
                                  const toDate = parseISO(
                                    currentMaintenanceRecord.to_date
                                  );

                                  // Calculate the exact number of days between the dates
                                  // For noon-to-noon concept, we need to be precise
                                  const diffTime = Math.abs(
                                    toDate.getTime() - fromDate.getTime()
                                  );
                                  maintenanceSpan = Math.ceil(
                                    diffTime / (1000 * 60 * 60 * 24)
                                  );

                                  // Ensure we have at least 1 day span
                                  maintenanceSpan = Math.max(
                                    maintenanceSpan,
                                    1
                                  );
                                } else {
                                  // Fallback to checking consecutive days
                                  while (
                                    nextIndex < dates.length &&
                                    getRoomStatus(room.id, dates[nextIndex]) ===
                                      "maintenance"
                                  ) {
                                    maintenanceSpan++;
                                    nextIndex++;
                                  }
                                }

                                // Calculate the actual number of days
                                const actualDays = maintenanceSpan;
                                let maxWidth = "none";
                                if (
                                  currentMaintenanceRecord &&
                                  currentMaintenanceRecord.from_date &&
                                  currentMaintenanceRecord.to_date
                                ) {
                                  const blockEndDate = parseISO(
                                    currentMaintenanceRecord.to_date
                                  );

                                  // If block end date is after view end date, adjust the span
                                  if (blockEndDate > endDate) {
                                    maxWidth = "-webkit-fill-available";
                                  }
                                }

                                // Add a maintenance span element
                                // Extract booking ID from notes if available
                                let bookingId = null;

                                if (inventoryRecord?.notes) {
                                  const match =
                                    inventoryRecord.notes.match(
                                      /\b(order_[a-z0-9]+)/i
                                    );
                                  bookingId = match ? match[1] : null;
                                }
                                console.log(inventoryRecord?.notes, bookingId);

                                // Adjust for noon-to-noon concept
                                // First day (dateIndex === 0) and last day of maintenance span should be half-width
                                const isFirstDay = dateIndex === 0;
                                // Check if this span ends on the last day of the calendar
                                const isLastDayOfSpan =
                                  dateIndex + maintenanceSpan - 1 ===
                                  dates.length - 1;

                                // Also check if this is the last day of the month (for multi-month views)
                                const lastDayOfSpan =
                                  dates[
                                    Math.min(
                                      dateIndex + maintenanceSpan - 1,
                                      dates.length - 1
                                    )
                                  ];
                                const isLastDayOfMonth =
                                  lastDayOfSpan &&
                                  lastDayOfSpan.getDate() ===
                                    endOfMonth(lastDayOfSpan).getDate();

                                // Check if this is the first day of the month
                                const isFirstDayOfMonth = date.getDate() === 1;

                                // Calculate grid column span based on noon-to-noon concept
                                // For first day of month, we need to start at the correct column
                                let gridColumnStart;

                                // If this is the first day of the month but not the first day in the calendar view,
                                // we need to calculate the correct starting column
                                if (isFirstDayOfMonth && !isFirstDay) {
                                  // Find the index of this date in the dates array
                                  const exactDateIndex = dates.findIndex(
                                    (d) =>
                                      d.getFullYear() === date.getFullYear() &&
                                      d.getMonth() === date.getMonth() &&
                                      d.getDate() === date.getDate()
                                  );

                                  // Calculate the grid column start based on the exact date index
                                  gridColumnStart = exactDateIndex * 2 + 2; // +2 because first column is for room names
                                } else {
                                  // Normal calculation for other days
                                  gridColumnStart = dateIndex * 2 + 2; // +2 because first column is for room names
                                }
                                let gridColumnSpan;

                                // Special handling for 1-day maintenance periods
                                if (maintenanceSpan === 1) {
                                  // For 1-day maintenance, we need at least 2 columns width
                                  gridColumnSpan = 2;

                                  // Special handling for 1-day maintenance on day 1
                                  if (
                                    isFirstDayOfMonth &&
                                    dateStr ===
                                      currentMaintenanceRecord?.from_date
                                  ) {
                                    // For 1-day maintenance on day 1, ensure they span 2 columns
                                    gridColumnSpan = 2;
                                  }
                                } else {
                                  // For multi-day maintenance periods
                                  // Calculate the exact number of columns needed
                                  // Each full day needs 2 columns, first and last days need special handling

                                  // Start with the base calculation: 2 columns per day
                                  gridColumnSpan = maintenanceSpan * 2;

                                  // Special handling for spans that start on day 1 and end on the last day of the month
                                  // This is to fix the issue with room 451 available from 01-05
                                  if (isFirstDayOfMonth && isLastDayOfMonth) {
                                    // For spans that cover a full month, we need to ensure they span the correct number of columns
                                    // Calculate the exact number of days in the month
                                    const daysInMonth =
                                      endOfMonth(date).getDate();

                                    // Each day gets 2 columns, except first and last days which get 1 column each
                                    gridColumnSpan = (daysInMonth - 2) * 2 + 2; // -2 for first and last days, +2 for their half-width
                                  } else {
                                    // Normal handling for other spans

                                    // Check if the first day of the span is the first day of the calendar
                                    // or if it's the first day of the month
                                    if (isFirstDay || isFirstDayOfMonth) {
                                      // First day of calendar/month is half-width (1 column instead of 2)
                                      gridColumnSpan -= 1;
                                    }

                                    // Check if the last day of the span is the last day of the calendar
                                    // or if it's the last day of the month
                                    if (isLastDayOfSpan || isLastDayOfMonth) {
                                      // Last day of calendar/month is half-width (1 column instead of 2)
                                      gridColumnSpan -= 1;
                                    }
                                  }
                                }

                                elements.push(
                                  <div
                                    key={`maintenance-${dateIndex}`}
                                    className={`status-cell maintenance-span booking-cell-clickable ${
                                      isWeekend(date) ? "weekend" : ""
                                    }`}
                                    style={{
                                      gridColumn: `${gridColumnStart} / span ${gridColumnSpan}`,
                                      position: "absolute",
                                      left:
                                        maintenanceSpan === 1 ? "0" : "auto",
                                      width: "100%",
                                      maxWidth: maxWidth,
                                      height: "100%",
                                      borderRadius: "0",
                                      zIndex: 2,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      if (bookingId) {
                                        setSelectedBookingId(
                                          bookingId === selectedBookingId
                                            ? null
                                            : bookingId
                                        );
                                      }
                                    }}
                                    onContextMenu={(e) => {
                                      // Extract booking ID from notes if available
                                      if (bookingId) {
                                        handleContextMenu(
                                          e,
                                          room.id,
                                          date,
                                          "maintenance",
                                          bookingId,
                                          inventoryRecord?.notes
                                        );
                                      }
                                    }}
                                    title={(() => {
                                      // Get the date range for the tooltip with validation
                                      let startDateFormatted = "Unknown date";
                                      let endDateFormatted = "Unknown date";

                                      try {
                                        // Validate start date
                                        const startDate = dates[dateIndex];
                                        if (
                                          startDate &&
                                          startDate instanceof Date &&
                                          !isNaN(startDate.getTime())
                                        ) {
                                          startDateFormatted = format(
                                            startDate,
                                            "MMM dd, yyyy"
                                          );
                                        }

                                        // Validate end date
                                        const endDateIndex =
                                          dateIndex + maintenanceSpan - 1;
                                        if (
                                          endDateIndex >= 0 &&
                                          endDateIndex < dates.length
                                        ) {
                                          const endDate = dates[endDateIndex];
                                          if (
                                            endDate &&
                                            endDate instanceof Date &&
                                            !isNaN(endDate.getTime())
                                          ) {
                                            endDateFormatted = format(
                                              endDate,
                                              "MMM dd, yyyy"
                                            );
                                          }
                                        }
                                      } catch (err) {
                                        // Silently handle errors in date formatting
                                      }

                                      // If maintenance extends beyond view end date, show view end date in tooltip
                                      let displayEndDateFormatted =
                                        endDateFormatted;
                                      if (
                                        currentMaintenanceRecord &&
                                        currentMaintenanceRecord.to_date
                                      ) {
                                        const blockEndDate = parseISO(
                                          currentMaintenanceRecord.to_date
                                        );
                                        if (blockEndDate > endDate) {
                                          displayEndDateFormatted = format(
                                            endDate,
                                            "MMM dd, yyyy"
                                          );
                                        }
                                      }

                                      const maintenanceDateRange =
                                        maintenanceSpan === 1
                                          ? startDateFormatted
                                          : `${startDateFormatted} to ${displayEndDateFormatted}${
                                              currentMaintenanceRecord &&
                                              currentMaintenanceRecord.to_date &&
                                              parseISO(
                                                currentMaintenanceRecord.to_date
                                              ) > endDate
                                                ? " (continues beyond view)"
                                                : ""
                                            }`;

                                      // Check if notes is null, undefined, or empty string
                                      const notes =
                                        inventoryRecord?.notes &&
                                        inventoryRecord.notes !== ""
                                          ? `\n\nNotes: ${inventoryRecord.notes}`
                                          : "";

                                      return maintenanceSpan === 1
                                        ? `Room is under maintenance on ${maintenanceDateRange}${notes}`
                                        : `Room is under maintenance from ${maintenanceDateRange} (${actualDays} days)${notes}`;
                                    })()}
                                  >
                                    <span style={{ fontSize: "1rem" }}>🔧</span>
                                    {maintenanceSpan > 1 && (
                                      <span
                                        style={{
                                          marginLeft: "3px",
                                          fontSize: "0.7rem",
                                        }}
                                      >
                                        {actualDays} day
                                        {actualDays > 1 ? "s" : ""}
                                      </span>
                                    )}
                                  </div>
                                );

                                // Skip the next cells that are part of this maintenance span
                                // For date ranges, we need to skip the days between from_date and to_date
                                skipCount = maintenanceSpan - 1;
                              } else {
                                // Regular status cell - find consecutive days with same status
                                let statusSpan = 1;
                                let nextIndex = dateIndex + 1;

                                // Calculate display span based on actual span

                                // Find the current inventory record for this status period
                                const currentStatusRecord = inventoryRecord;

                                // If we have a record with from_date and to_date, use that to determine the span
                                if (
                                  currentStatusRecord &&
                                  currentStatusRecord.from_date &&
                                  currentStatusRecord.to_date
                                ) {
                                  // Calculate days between from_date and to_date
                                  const fromDate = parseISO(
                                    currentStatusRecord.from_date
                                  );
                                  const toDate = parseISO(
                                    currentStatusRecord.to_date
                                  );

                                  // Calculate the exact number of days between the dates
                                  // For noon-to-noon concept, we need to be precise
                                  const diffTime = Math.abs(
                                    toDate.getTime() - fromDate.getTime()
                                  );
                                  statusSpan = Math.ceil(
                                    diffTime / (1000 * 60 * 60 * 24)
                                  );

                                  // Ensure we have at least 1 day span
                                  statusSpan = Math.max(statusSpan, 1);
                                } else {
                                  // Look for consecutive days for all statuses
                                  if (
                                    status === "booked" ||
                                    status === "reserved" ||
                                    status === "available" ||
                                    status === "unavailable" ||
                                    status === "on_demand"
                                  ) {
                                    // Check if the next day has the same status
                                    while (
                                      nextIndex < dates.length &&
                                      getRoomStatus(
                                        room.id,
                                        dates[nextIndex]
                                      ) === status
                                    ) {
                                      statusSpan++;
                                      nextIndex++;
                                    }
                                  }
                                }

                                // Check if the status period extends beyond the view's end date
                                let maxWidth = "none";

                                if (
                                  currentStatusRecord &&
                                  currentStatusRecord.from_date &&
                                  currentStatusRecord.to_date
                                ) {
                                  const blockEndDate = parseISO(
                                    currentStatusRecord.to_date
                                  );

                                  // If block end date is after view end date, adjust the span
                                  if (blockEndDate > endDate) {
                                    maxWidth = "-webkit-fill-available";
                                  }
                                }

                                // Extract booking ID from notes if available
                                let bookingId = null;
                                if (inventoryRecord?.notes) {
                                  const match =
                                    inventoryRecord.notes.match(
                                      /\b(order_[a-z0-9]+)/i
                                    );
                                  bookingId = match ? match[1] : null;
                                }

                                // Adjust for noon-to-noon concept
                                // First day (dateIndex === 0) and last day of status span should be half-width
                                const isFirstDay = dateIndex === 0;
                                // Check if this span ends on the last day of the calendar
                                const isLastDayOfSpan =
                                  dateIndex + statusSpan - 1 ===
                                  dates.length - 1;

                                // Also check if this is the last day of the month (for multi-month views)
                                const lastDayOfSpan =
                                  dates[
                                    Math.min(
                                      dateIndex + statusSpan - 1,
                                      dates.length - 1
                                    )
                                  ];
                                const isLastDayOfMonth =
                                  lastDayOfSpan &&
                                  lastDayOfSpan.getDate() ===
                                    endOfMonth(lastDayOfSpan).getDate();

                                // Check if this is the first day of the month
                                const isFirstDayOfMonth = date.getDate() === 1;

                                // Calculate grid column span based on noon-to-noon concept
                                // For first day of month, we need to start at the correct column
                                let gridColumnStart;

                                // For first day of month, we need special handling
                                if (isFirstDayOfMonth) {
                                  // Find the index of this date in the dates array
                                  const exactDateIndex = dates.findIndex(
                                    (d) =>
                                      d.getFullYear() === date.getFullYear() &&
                                      d.getMonth() === date.getMonth() &&
                                      d.getDate() === date.getDate()
                                  );

                                  // Calculate the grid column start based on the exact date index
                                  gridColumnStart = exactDateIndex * 2 + 2; // +2 because first column is for room names

                                  // Special handling for 1-day bookings on day 1
                                  if (
                                    statusSpan === 1 &&
                                    dateStr === currentStatusRecord?.from_date
                                  ) {
                                    // For 1-day bookings on day 1, ensure they start at the correct column
                                    // This is especially important for reserved cells
                                  }
                                } else {
                                  // Normal calculation for other days
                                  gridColumnStart = dateIndex * 2 + 2; // +2 because first column is for room names
                                }
                                let gridColumnSpan;

                                // Special handling for 1-day bookings
                                if (statusSpan === 1) {
                                  // For 1-day bookings, we need at least 2 columns width
                                  gridColumnSpan = 2;

                                  // Special handling for 1-day bookings on day 1
                                  if (
                                    isFirstDayOfMonth &&
                                    dateStr === currentStatusRecord?.from_date
                                  ) {
                                    // For 1-day bookings on day 1, ensure they span 2 columns
                                    gridColumnSpan = 2;
                                  }
                                } else {
                                  // For multi-day bookings
                                  // Calculate the exact number of columns needed
                                  // Each full day needs 2 columns, first and last days need special handling

                                  // Start with the base calculation: 2 columns per day
                                  gridColumnSpan = statusSpan * 2;

                                  // Special handling for spans that start on day 1 and end on the last day of the month
                                  // This is to fix the issue with room 451 available from 01-05
                                  if (isFirstDayOfMonth && isLastDayOfMonth) {
                                    // For spans that cover a full month, we need to ensure they span the correct number of columns
                                    // Calculate the exact number of days in the month
                                    const daysInMonth =
                                      endOfMonth(date).getDate();

                                    // Each day gets 2 columns, except first and last days which get 1 column each
                                    gridColumnSpan = (daysInMonth - 2) * 2 + 2; // -2 for first and last days, +2 for their half-width
                                  } else {
                                    // Normal handling for other spans

                                    // Check if the first day of the span is the first day of the calendar
                                    // or if it's the first day of the month
                                    // For spans starting on day 1, we need special handling
                                    if (isFirstDay || isFirstDayOfMonth) {
                                      // For spans starting on day 1, we need to be careful with the adjustment
                                      // If the span is 4 days (like room 451 from 01-05), we need to ensure it spans 8 columns
                                      // First day gets 1 column, middle days get 2 columns each, last day gets 1 column

                                      // First day of calendar/month is half-width (1 column instead of 2)
                                      // But we need to be careful not to over-adjust
                                      if (
                                        isFirstDayOfMonth &&
                                        dateStr ===
                                          currentStatusRecord?.from_date
                                      ) {
                                        // For spans starting on day 1, don't reduce the span
                                        // This ensures room 451 from 01-05 gets 8 columns (1+6+1)
                                      } else {
                                        // Normal adjustment for other cases
                                        gridColumnSpan -= 1;
                                      }
                                    }

                                    // Check if the last day of the span is the last day of the calendar
                                    // or if it's the last day of the month
                                    if (isLastDayOfSpan || isLastDayOfMonth) {
                                      // For spans ending on the last day of the month, we need special handling
                                      // Calculate the last day of the span
                                      const lastDayOfSpanDate =
                                        dates[
                                          Math.min(
                                            dateIndex + statusSpan - 1,
                                            dates.length - 1
                                          )
                                        ];
                                      const lastDayStr = format(
                                        lastDayOfSpanDate,
                                        "yyyy-MM-dd"
                                      );

                                      // Check if this is the last day of the span
                                      if (
                                        currentStatusRecord?.to_date &&
                                        lastDayStr ===
                                          format(
                                            addDays(
                                              parseISO(
                                                currentStatusRecord.to_date
                                              ),
                                              -1
                                            ),
                                            "yyyy-MM-dd"
                                          )
                                      ) {
                                        // Last day of calendar/month is half-width (1 column instead of 2)
                                        gridColumnSpan -= 1;
                                      } else {
                                        // If this is not the actual last day of the span, don't adjust
                                      }
                                    }
                                  }
                                }

                                elements.push(
                                  <div
                                    key={`status-${dateIndex}`}
                                    className={`status-cell status-${status} booking-cell-clickable ${
                                      isWeekend(date) ? "weekend" : ""
                                    }`}
                                    style={{
                                      gridColumn: `${gridColumnStart} / span ${gridColumnSpan}`,
                                      position: "absolute",
                                      left: statusSpan === 1 ? "0" : "auto",
                                      width: "100%",
                                      maxWidth: maxWidth,
                                      height: "100%",
                                      borderRadius: "0",
                                      zIndex: 2,
                                      cursor: "pointer",
                                    }}
                                    onClick={() => {
                                      if (bookingId) {
                                        setSelectedBookingId(
                                          bookingId === selectedBookingId
                                            ? null
                                            : bookingId
                                        );
                                      }
                                    }}
                                    onContextMenu={(e) => {
                                      // Extract booking ID from notes if available
                                      if (bookingId) {
                                        handleContextMenu(
                                          e,
                                          room.id,
                                          date,
                                          status,
                                          bookingId,
                                          inventoryRecord?.notes
                                        );
                                      }
                                    }}
                                    title={(() => {
                                      // Get the date range for the tooltip with validation
                                      let startDateFormatted = "Unknown date";
                                      let endDateFormatted = "Unknown date";

                                      try {
                                        // Validate start date
                                        const startDate = dates[dateIndex];
                                        if (
                                          startDate &&
                                          startDate instanceof Date &&
                                          !isNaN(startDate.getTime())
                                        ) {
                                          startDateFormatted = format(
                                            startDate,
                                            "MMM dd, yyyy"
                                          );
                                        }

                                        // Validate end date
                                        const endDateIndex =
                                          dateIndex + statusSpan - 1;
                                        if (
                                          endDateIndex >= 0 &&
                                          endDateIndex < dates.length
                                        ) {
                                          const endDate = dates[endDateIndex];
                                          if (
                                            endDate &&
                                            endDate instanceof Date &&
                                            !isNaN(endDate.getTime())
                                          ) {
                                            endDateFormatted = format(
                                              endDate,
                                              "MMM dd, yyyy"
                                            );
                                          }
                                        }
                                      } catch (err) {
                                        // Silently handle errors in date formatting
                                      }

                                      // If status extends beyond view end date, show view end date in tooltip
                                      let displayEndDateFormatted =
                                        endDateFormatted;
                                      if (
                                        currentStatusRecord &&
                                        currentStatusRecord.to_date
                                      ) {
                                        const blockEndDate = parseISO(
                                          currentStatusRecord.to_date
                                        );
                                        if (blockEndDate > endDate) {
                                          displayEndDateFormatted = format(
                                            blockEndDate,
                                            "MMM dd, yyyy"
                                          );
                                        }
                                      }

                                      const statusDateRange =
                                        statusSpan === 1
                                          ? startDateFormatted
                                          : `${startDateFormatted} to ${displayEndDateFormatted}${
                                              currentStatusRecord &&
                                              currentStatusRecord.to_date &&
                                              parseISO(
                                                currentStatusRecord.to_date
                                              ) > endDate
                                                ? " (continues beyond view)"
                                                : ""
                                            }`;

                                      // Check if notes is null, undefined, or empty string
                                      const notes =
                                        inventoryRecord?.notes &&
                                        inventoryRecord.notes !== ""
                                          ? `\n\nNotes: ${inventoryRecord.notes}`
                                          : "";

                                      // Use a string mapping to handle all possible status values
                                      const statusMessages: Record<
                                        string,
                                        string
                                      > = {
                                        booked: `Room is booked for a guest ${
                                          statusSpan === 1 ? "on" : "from"
                                        } ${statusDateRange}${notes}`,
                                        reserved: `Room is reserved but not confirmed ${
                                          statusSpan === 1 ? "on" : "from"
                                        } ${statusDateRange}${notes}`,
                                        maintenance: `Room is under maintenance ${
                                          statusSpan === 1 ? "on" : "from"
                                        } ${statusDateRange}${notes}`,
                                        unavailable: `Room is unavailable ${
                                          statusSpan === 1 ? "on" : "from"
                                        } ${statusDateRange}${notes}`,
                                        available: `Room is available for booking ${
                                          statusSpan === 1 ? "on" : "from"
                                        } ${statusDateRange}${notes}`,
                                        on_demand: `Room is available on customer demand ${
                                          statusSpan === 1 ? "on" : "from"
                                        } ${statusDateRange}${notes}`,
                                      };

                                      return (
                                        statusMessages[status] ||
                                        statusMessages["available"]
                                      );
                                    })()}
                                  >
                                    {status === "booked" && (
                                      <span style={{ fontSize: "1rem" }}>
                                        👤
                                      </span>
                                    )}
                                    {status === "reserved" && (
                                      <span style={{ fontSize: "1rem" }}>
                                        ⛔
                                      </span>
                                    )}
                                    {status === "on_demand" && (
                                      <span style={{ fontSize: "1rem" }}>
                                        📞
                                      </span>
                                    )}
                                    {/* No icon for unavailable status, just grayed out */}
                                  </div>
                                );

                                // Skip the next cells that are part of this status span
                                // For date ranges, we need to skip the days between from_date and to_date
                                if (statusSpan > 1) {
                                  skipCount = statusSpan - 1;
                                }
                              }
                            }

                            return elements;
                          })()}

                          {/*
                      CLICKABLE CELLS IN THE CALENDAR:

                      1. ALL STATUS CELLS are clickable (reserved, booked, maintenance, etc.):
                         - Status cells extract the booking ID from the notes field if available
                         - When clicked, they show the booking details in the right panel
                         - This includes the cells with ⛔ (reserved) and other status indicators

                      2. ALL BOOKING CELLS are clickable regardless of status:
                         - All bookings are clickable (confirmed, reserved, cart-reserved, reserved_unassigned, etc.)
                         - This allows staff to view details of any booking in the system by clicking on it
                         - No filtering is done based on booking status - everything is clickable

                      Each cell is made clickable by:
                      1. Adding the 'booking-cell-clickable' class which provides cursor:pointer and other interactive styles
                      2. Adding an onClick handler that sets the selectedBookingId state
                      3. Using position:relative and z-index values to ensure the elements can receive clicks
                      4. Setting pointer-events:auto in the CSS class to ensure clicks are captured
                    */}
                        </div>
                      </div>
                    ))}
                  </div>
                ))
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Booking Details Modal */}
      {selectedBookingId && (
        <div className="fixed inset-y-0 right-0 w-1/3 bg-white shadow-xl border-l border-gray-200 z-50 overflow-auto transition-transform duration-300 ease-in-out transform">
          <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3 flex justify-between items-center shadow-sm z-10">
            <h2 className="text-lg font-semibold text-gray-800">
              Booking Details
            </h2>
            <button
              className="p-1 rounded-full hover:bg-gray-100 transition-colors"
              onClick={() => setSelectedBookingId(null)}
              aria-label="Close booking details"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-6 w-6 text-gray-500"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M6 18L18 6M6 6l12 12"
                />
              </svg>
            </button>
          </div>
          <SimpleBookingDetail
            bookingId={selectedBookingId}
            isInSidebar={true}
          />
        </div>
      )}

      {/* Collapsible Bookings Panel */}
      <CollapsibleBookingsPanel title="Bookings">
        {unallocatedBookings.length === 0 ? (
          <EmptyBookingsState
            title="No unallocated bookings found"
            subtitle="All bookings have been assigned to rooms"
          />
        ) : (
          <div className="space-y-3">
            {unallocatedBookings.map((booking, index) => {
              // Extract booking ID from notes if available
              let bookingId = null;
              if (booking.notes) {
                const match = booking.notes.match(/\b(order_[a-z0-9]+)/i);
                bookingId = match ? match[1] : null;
              }

              // We don't need to format dates here anymore since we're using metadata

              return (
                <div
                  key={index}
                  className="bg-yellow-50 p-4 rounded-lg border border-yellow-200 hover:shadow-md transition-shadow"
                  onClick={() => bookingId && setSelectedBookingId(bookingId)}
                >
                  {/* Booking ID and Order Status */}
                  <div className="flex justify-between items-start mb-3">
                    <div className="font-medium text-gray-800">
                      {booking.id ? (
                        <span className="cursor-pointer hover:text-blue-600">
                          {booking.id}
                        </span>
                      ) : (
                        "Unknown Booking"
                      )}
                    </div>
                  </div>

                  {/* Guest Information */}
                  {booking.metadata?.guest_name && (
                    <div className="flex items-center text-sm text-gray-700 mb-2">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 mr-1 text-gray-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                      <span className="font-medium">
                        {booking.metadata.guest_name}
                      </span>
                    </div>
                  )}

                  {/* Contact Information */}
                  <div className="grid grid-cols-1 gap-1 mb-3">
                    {booking.metadata?.guest_email && (
                      <div className="flex items-center text-xs text-gray-600">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 mr-1 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                          />
                        </svg>
                        {booking.metadata.guest_email}
                      </div>
                    )}
                  </div>

                  {/* Booking Details */}
                  <div className="grid grid-cols-2 gap-2 mb-3">
                    {/* Check-in/Check-out Dates */}
                    <div className="col-span-2 flex items-center text-xs text-gray-600 mb-1">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-4 w-4 mr-1 text-gray-500"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                        />
                      </svg>
                      {booking.metadata?.check_in_date ? (
                        <span>
                          <span className="font-medium">
                            {format(
                              new Date(booking.metadata.check_in_date),
                              "MMM dd"
                            )}
                          </span>
                          {booking.metadata.check_in_time && (
                            <span className="text-gray-500">
                              {" "}
                              ({booking.metadata.check_in_time})
                            </span>
                          )}
                          <span> - </span>
                          <span className="font-medium">
                            {format(
                              new Date(booking.metadata.check_out_date),
                              "MMM dd, yyyy"
                            )}
                          </span>
                          {booking.metadata.check_out_time && (
                            <span className="text-gray-500">
                              {" "}
                              ({booking.metadata.check_out_time})
                            </span>
                          )}
                        </span>
                      ) : (
                        "Unknown dates"
                      )}
                    </div>

                    {/* Room Type */}
                    {(() => {
                      // Try to get room type from booking metadata
                      let roomTypeName = booking.metadata?.room_type || "";

                      if (!roomTypeName && booking.metadata?.room_config_id) {
                        // If room_config_id is available in the booking metadata
                        const roomConfig = roomConfigs.find(
                          (c) => c.id === booking.metadata.room_config_id
                        );
                        if (roomConfig) {
                          roomTypeName = roomConfig.title || roomConfig.name;
                        }
                      }

                      return roomTypeName ? (
                        <div className="flex items-center text-xs text-gray-600">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-3 w-3 mr-1 text-gray-500"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                            />
                          </svg>
                          <span className="font-medium">{roomTypeName}</span>
                        </div>
                      ) : null;
                    })()}

                    {/* Number of Rooms */}
                    {booking.metadata?.number_of_rooms && (
                      <div className="flex items-center text-xs text-gray-600">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 mr-1 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                          />
                        </svg>
                        <span>
                          {booking.metadata.number_of_rooms} room
                          {booking.metadata.number_of_rooms > 1 ? "s" : ""}
                        </span>
                      </div>
                    )}

                    {/* Number of Guests */}
                    {booking.metadata?.number_of_guests && (
                      <div className="flex items-center text-xs text-gray-600">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 mr-1 text-gray-500"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"
                          />
                        </svg>
                        <span>
                          {booking.metadata.number_of_guests} guest
                          {booking.metadata.number_of_guests > 1 ? "s" : ""}
                        </span>
                      </div>
                    )}

                    {/* Price */}
                  </div>

                  {/* Action Button */}
                  <div className="mt-3 pt-2 border-t border-yellow-100">
                    <button
                      className="w-full text-center text-xs font-medium text-blue-600 hover:text-blue-800 py-1"
                      onClick={(e) => {
                        e.stopPropagation();
                        console.log(
                          "Assign Room button clicked for booking:",
                          booking.id
                        );
                        if (booking.id) {
                          // Set up for allocation
                          const cellData = {
                            room_config_id: booking.metadata.room_config_id,
                            date: booking.metadata?.check_in_date,
                            status: "reserved",
                            bookingId: booking.id,
                            id: booking.id,
                            notes: booking.notes,
                          };
                          console.log("Setting selectedCell to:", cellData);
                          setSelectedCell(cellData);
                          console.log("Opening move modal");
                          setMoveModalOpen(true);
                          const availableRoomsForDate =
                            getAvailableRoomsForDate(
                              booking.metadata?.check_in_date,
                              booking.metadata?.check_out_date,
                              booking.metadata.room_config_id
                            );

                          console.log(
                            "Available rooms:",
                            availableRoomsForDate
                          );

                          // Update the availableRooms state
                          setAvailableRooms(availableRoomsForDate);
                        } else {
                          console.error("No booking ID found");
                        }
                      }}
                    >
                      Assign Room
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        )}
      </CollapsibleBookingsPanel>

      {/* Context Menu */}
      {contextMenuOpen && selectedCell && (
        <div
          className="fixed bg-white shadow-lg rounded-md overflow-hidden z-50 border border-gray-200"
          style={{
            top: contextMenuPosition.y,
            left: contextMenuPosition.x,
            width: "200px",
          }}
        >
          <div className="py-1">
            <button
              className="flex items-center gap-2 p-2 hover:bg-gray-100 cursor-pointer w-full text-left"
              onClick={handleMoveToUnallocated}
            >
              <ArrowRight className="w-4 h-4 text-gray-500" />
              <span>Move to unallocated</span>
            </button>

            {/* Split Booking Option - Only show for multi-day bookings */}
            {selectedCell.bookingId &&
              (() => {
                // Find the booking record for this cell to check duration
                const formattedCellDate = format(
                  selectedCell.date,
                  "yyyy-MM-dd"
                );

                // Debug: Log all availability records for this room
                console.log(
                  "All availability records for room:",
                  selectedCell.roomId
                );
                availability
                  .filter((item) => item.room_id === selectedCell.roomId)
                  .forEach((item) => {
                    console.log("Record:", item);
                  });

                const bookingRecord = availability.find((item: any) => {
                  // Match by room ID and booking ID (from notes)
                  if (item.room_id !== selectedCell.roomId) return false;

                  // Debug: Log each potential match
                  console.log("Checking record:", item);

                  // Check if this booking contains the selected date
                  if (item.from_date && item.to_date) {
                    const cellDate = new Date(formattedCellDate);
                    const fromDate = new Date(item.from_date);
                    const toDate = new Date(item.to_date);

                    const isMatch = cellDate >= fromDate && cellDate < toDate;
                    console.log("Date check:", {
                      cellDate,
                      fromDate,
                      toDate,
                      isMatch,
                    });

                    return isMatch;
                  }

                  return false;
                });

                // Check if this is a multi-day booking
                let isMultiDayBooking = false;

                if (
                  bookingRecord &&
                  bookingRecord.from_date &&
                  bookingRecord.to_date
                ) {
                  const fromDate = new Date(bookingRecord.from_date);
                  const toDate = new Date(bookingRecord.to_date);

                  // Calculate days difference
                  const daysDiff = Math.floor(
                    (toDate.getTime() - fromDate.getTime()) /
                      (1000 * 60 * 60 * 24)
                  );
                  console.log(
                    "Booking duration in days:",
                    daysDiff,
                    "From:",
                    fromDate,
                    "To:",
                    toDate
                  );

                  isMultiDayBooking = daysDiff > 1;
                }

                return isMultiDayBooking ? (
                  <button
                    className="flex items-center gap-2 p-2 hover:bg-gray-100 cursor-pointer w-full text-left"
                    onClick={() => {
                      setContextMenuOpen(false);
                      setActionType("split");
                      setSplitModalOpen(true);
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="w-4 h-4 text-gray-500"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                    <span>Split booking</span>
                  </button>
                ) : null;
              })()}

            {selectedCell.status === "reserved" && (
              <button
                className="flex items-center gap-2 p-2 hover:bg-gray-100 cursor-pointer w-full text-left"
                onClick={handleConfirmBooking}
              >
                <Check className="w-4 h-4 text-gray-500" />
                <span>Confirm booking</span>
              </button>
            )}
          </div>
        </div>
      )}
      <FocusModal open={moveModalOpen}>
        <FocusModal.Content>
          <FocusModal.Header>
            <Heading>Assign Room</Heading>
          </FocusModal.Header>
          <FocusModal.Body>
            <div className="p-4">
              {selectedCell && (
                <div className="space-y-4">
                  {/* Booking Information */}
                  <div className="bg-blue-50 p-4 rounded-lg border border-blue-100 mb-4">
                    <h3 className="text-sm font-medium text-blue-800 mb-2">
                      Booking Details
                    </h3>
                    <div className="text-sm text-blue-700">
                      <p>
                        <span className="font-medium">Booking ID:</span>{" "}
                        {selectedCell.bookingId || "Unknown"}
                      </p>
                      <p>
                        <span className="font-medium">Date:</span>{" "}
                        {format(selectedCell.date, "MMM dd, yyyy")}
                      </p>
                      {selectedCell.room_config_id && (
                        <p>
                          <span className="font-medium">Room Type:</span>{" "}
                          {roomConfigs.find(
                            (r) => r.id === selectedCell.room_config_id
                          )?.title || "Unknown"}
                        </p>
                      )}
                    </div>
                  </div>

                  {/* Room Selection */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Select a room to assign:
                    </label>

                    {availableRooms.length === 0 ? (
                      <div className="text-center py-4 bg-gray-50 rounded-lg border border-gray-200">
                        <p className="text-gray-500">
                          No available rooms found
                        </p>
                      </div>
                    ) : (
                      <div className="grid grid-cols-1 gap-2 max-h-60 overflow-y-auto p-2 border border-gray-200 rounded-lg">
                        {availableRooms.map((room) => (
                          <div
                            key={room.id}
                            className={`p-3 rounded-lg border cursor-pointer transition-colors ${
                              selectedTargetRoomId === room.id
                                ? "bg-blue-50 border-blue-300"
                                : "bg-white border-gray-200 hover:bg-gray-50"
                            }`}
                            onClick={() => {
                              console.log("Room selected:", room.id);
                              setSelectedTargetRoomId(room.id);
                            }}
                          >
                            <div className="flex justify-between items-center">
                              <div>
                                <div className="font-medium">
                                  Room {room.room_number}
                                </div>
                                <div className="text-sm text-gray-500">
                                  {roomConfigs.find(
                                    (c) => c.id === room.room_config_id
                                  )?.title || "Unknown Type"}
                                  {room.floor && ` • Floor ${room.floor}`}
                                </div>
                              </div>
                              {selectedTargetRoomId === room.id && (
                                <div className="text-blue-500">
                                  <Check className="w-5 h-5" />
                                </div>
                              )}
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Date Range Information */}
                  <div className="mt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Booking Period:
                    </label>
                    <div className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg border border-gray-200">
                      {(() => {
                        // Find the booking from unallocatedBookings if this is an unallocated booking
                        if (!selectedCell.roomId) {
                          const booking = localUnallocatedBookings.find(
                            (b) =>
                              b.order_id === selectedCell.bookingId ||
                              (b.notes &&
                                b.notes.includes(selectedCell.bookingId || ""))
                          );

                          if (booking && booking.from_date && booking.to_date) {
                            return (
                              <>
                                <span className="font-medium">Check-in:</span>{" "}
                                {format(
                                  new Date(booking.from_date),
                                  "MMM dd, yyyy"
                                )}
                                <br />
                                <span className="font-medium">
                                  Check-out:
                                </span>{" "}
                                {format(
                                  new Date(booking.to_date),
                                  "MMM dd, yyyy"
                                )}
                              </>
                            );
                          }
                        } else {
                          // Find the inventory record for this cell to get date range
                          const formattedCellDate = format(
                            selectedCell.date,
                            "yyyy-MM-dd"
                          );
                          const inventoryRecord = availability.find(
                            (item: any) => {
                              if (item.room_id !== selectedCell.roomId)
                                return false;

                              // Check if this is the right date
                              if (item.from_date && item.to_date) {
                                return (
                                  formattedCellDate >= item.from_date &&
                                  formattedCellDate < item.to_date
                                );
                              }

                              return item.date === formattedCellDate;
                            }
                          );

                          if (
                            inventoryRecord &&
                            inventoryRecord.from_date &&
                            inventoryRecord.to_date
                          ) {
                            return (
                              <>
                                <span className="font-medium">Check-in:</span>{" "}
                                {format(
                                  new Date(inventoryRecord.from_date),
                                  "MMM dd, yyyy"
                                )}
                                <br />
                                <span className="font-medium">
                                  Check-out:
                                </span>{" "}
                                {format(
                                  new Date(inventoryRecord.to_date),
                                  "MMM dd, yyyy"
                                )}
                              </>
                            );
                          }
                        }

                        // Fallback if no date information is available
                        return (
                          <>
                            <span className="font-medium">Date:</span>{" "}
                            {format(selectedCell.date, "MMM dd, yyyy")}
                          </>
                        );
                      })()}
                    </div>
                  </div>
                </div>
              )}
            </div>
          </FocusModal.Body>
          <FocusModal.Footer>
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                onClick={() => {
                  setMoveModalOpen(false);
                  setSelectedTargetRoomId("");
                }}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={() => {
                  console.log(
                    "Assign/Move button clicked with targetRoomId:",
                    selectedTargetRoomId
                  );
                  executeMove();
                }}
                disabled={!selectedTargetRoomId}
              >
                {selectedCell?.roomId ? "Move Booking" : "Assign Room"}
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>

      {/* Confirm Booking Modal */}
      <FocusModal open={confirmModalOpen} onOpenChange={setConfirmModalOpen}>
        <FocusModal.Content>
          <FocusModal.Header>
            <Heading>Confirm Booking</Heading>
          </FocusModal.Header>
          <FocusModal.Body>
            <div className="p-4">
              <Text>Are you sure you want to confirm this booking?</Text>
              <Text className="text-gray-500 mt-2">
                This will change the status from reserved to booked.
              </Text>
            </div>
          </FocusModal.Body>
          <FocusModal.Footer>
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                onClick={() => setConfirmModalOpen(false)}
              >
                Cancel
              </Button>
              <Button variant="primary" onClick={executeConfirm}>
                Confirm
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>

      {/* Split Booking Modal */}
      <FocusModal
        open={splitModalOpen}
        onOpenChange={(open) => {
          setSplitModalOpen(open);

          // Initialize split date to middle of booking when opening
          if (open && selectedCell) {
            // Find the inventory record for this cell to get date range
            const formattedCellDate = format(selectedCell.date, "yyyy-MM-dd");
            const inventoryRecord = availability.find((item: any) => {
              if (item.room_id !== selectedCell.roomId) return false;

              // Check if this is the right date
              if (item.from_date && item.to_date) {
                return (
                  formattedCellDate >= item.from_date &&
                  formattedCellDate < item.to_date
                );
              }

              return item.date === formattedCellDate;
            });

            if (
              inventoryRecord &&
              inventoryRecord.from_date &&
              inventoryRecord.to_date
            ) {
              const fromDate = new Date(inventoryRecord.from_date);
              const toDate = new Date(inventoryRecord.to_date);

              console.log("Booking date range:", {
                fromDate: format(fromDate, "yyyy-MM-dd"),
                toDate: format(toDate, "yyyy-MM-dd"),
              });

              // Calculate duration in days
              const duration = Math.round(
                (toDate.getTime() - fromDate.getTime()) / (1000 * 60 * 60 * 24)
              );
              console.log("Booking duration in days:", duration);

              if (duration > 1) {
                // For multi-day bookings, set split date to a day after the start date
                // This ensures the split date is always within the booking range
                const middleDate = addDays(fromDate, 1);
                console.log(
                  "Setting split date to:",
                  format(middleDate, "yyyy-MM-dd")
                );
                setSplitDate(middleDate);
              } else {
                // If booking is only 1 day, we shouldn't allow splitting
                // But for safety, set a valid date
                setSplitDate(addDays(fromDate, 1));
              }
            } else {
              // Fallback to selected date + 1 day
              setSplitDate(addDays(selectedCell.date, 1));
            }
          }
        }}
      >
        <FocusModal.Content>
          <FocusModal.Header>
            <Heading>Split Booking</Heading>
          </FocusModal.Header>
          <FocusModal.Body>
            <div className="flex flex-col gap-4 p-4">
              <Text>Split this booking into two parts:</Text>

              {selectedCell && (
                <div className="space-y-4">
                  {/* Date selection */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Split date (check-out from first part, check-in to second
                      part):
                    </label>
                    {/* Custom date picker with validation */}
                    <div className="relative">
                      <input
                        type="date"
                        className="w-full p-2 border border-gray-300 rounded-md"
                        value={splitDate ? format(splitDate, "yyyy-MM-dd") : ""}
                        onChange={(e) => {
                          if (e.target.value) {
                            const newDate = new Date(e.target.value);

                            // Find the inventory record to get date range
                            const formattedCellDate = format(
                              selectedCell.date,
                              "yyyy-MM-dd"
                            );
                            const inventoryRecord = availability.find(
                              (item: any) => {
                                if (item.room_id !== selectedCell.roomId)
                                  return false;

                                // Check if this is the right date
                                if (item.from_date && item.to_date) {
                                  return (
                                    formattedCellDate >= item.from_date &&
                                    formattedCellDate < item.to_date
                                  );
                                }

                                return item.date === formattedCellDate;
                              }
                            );

                            // Validate the selected date is within the booking range
                            if (
                              inventoryRecord &&
                              inventoryRecord.from_date &&
                              inventoryRecord.to_date
                            ) {
                              const fromDate = new Date(
                                inventoryRecord.from_date
                              );
                              const toDate = new Date(inventoryRecord.to_date);

                              // Add one day to from date as minimum
                              const minDate = addDays(fromDate, 1);
                              // Subtract one day from to date as maximum
                              const maxDate = addDays(toDate, -1);

                              if (newDate < minDate) {
                                toast.error("Error", {
                                  description:
                                    "Split date must be after the check-in date",
                                });
                                return;
                              }

                              if (newDate > maxDate) {
                                toast.error("Error", {
                                  description:
                                    "Split date must be before the check-out date",
                                });
                                return;
                              }
                            }

                            setSplitDate(newDate);
                          }
                        }}
                        min={(() => {
                          // Find the inventory record to get date range
                          const formattedCellDate = format(
                            selectedCell.date,
                            "yyyy-MM-dd"
                          );
                          const inventoryRecord = availability.find(
                            (item: any) => {
                              if (item.room_id !== selectedCell.roomId)
                                return false;

                              // Check if this is the right date
                              if (item.from_date && item.to_date) {
                                return (
                                  formattedCellDate >= item.from_date &&
                                  formattedCellDate < item.to_date
                                );
                              }

                              return item.date === formattedCellDate;
                            }
                          );

                          if (inventoryRecord && inventoryRecord.from_date) {
                            // Add one day to from date as minimum
                            const fromDate = new Date(
                              inventoryRecord.from_date
                            );
                            const minDate = addDays(fromDate, 1);
                            return format(minDate, "yyyy-MM-dd");
                          }

                          return format(
                            addDays(selectedCell.date, 1),
                            "yyyy-MM-dd"
                          );
                        })()}
                        max={(() => {
                          // Find the inventory record to get date range
                          const formattedCellDate = format(
                            selectedCell.date,
                            "yyyy-MM-dd"
                          );
                          const inventoryRecord = availability.find(
                            (item: any) => {
                              if (item.room_id !== selectedCell.roomId)
                                return false;

                              // Check if this is the right date
                              if (item.from_date && item.to_date) {
                                return (
                                  formattedCellDate >= item.from_date &&
                                  formattedCellDate < item.to_date
                                );
                              }

                              return item.date === formattedCellDate;
                            }
                          );

                          if (inventoryRecord && inventoryRecord.to_date) {
                            // Subtract one day from to date as maximum
                            const toDate = new Date(inventoryRecord.to_date);
                            const maxDate = addDays(toDate, -1);
                            return format(maxDate, "yyyy-MM-dd");
                          }

                          return format(
                            addDays(selectedCell.date, 7),
                            "yyyy-MM-dd"
                          );
                        })()}
                      />
                      <div className="text-xs text-gray-500 mt-1">
                        Select a date between check-in and check-out
                      </div>
                    </div>
                  </div>

                  {/* Room allocation options */}
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Room allocation:
                    </label>
                    <div className="space-y-2">
                      <label className="flex items-center">
                        <input
                          type="radio"
                          className="mr-2"
                          checked={keepFirstPart}
                          onChange={() => setKeepFirstPart(true)}
                        />
                        <span>
                          Keep first part in current room, move second part to
                          unallocated
                        </span>
                      </label>
                      <label className="flex items-center">
                        <input
                          type="radio"
                          className="mr-2"
                          checked={!keepFirstPart}
                          onChange={() => setKeepFirstPart(false)}
                        />
                        <span>
                          Move first part to unallocated, keep second part in
                          current room
                        </span>
                      </label>
                    </div>
                  </div>

                  {/* Booking preview */}
                  {splitDate && (
                    <div className="border border-gray-200 rounded-md p-4 bg-gray-50">
                      <h4 className="font-medium text-gray-700 mb-2">
                        Preview:
                      </h4>
                      <div className="grid grid-cols-2 gap-4">
                        <div className="p-3 bg-white rounded border border-gray-200">
                          <div className="text-sm font-medium">First Part</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {(() => {
                              // Find the inventory record for this cell to get date range
                              const formattedCellDate = format(
                                selectedCell.date,
                                "yyyy-MM-dd"
                              );
                              const inventoryRecord = availability.find(
                                (item: any) => {
                                  if (item.room_id !== selectedCell.roomId)
                                    return false;

                                  // Check if this is the right date
                                  if (item.from_date && item.to_date) {
                                    return (
                                      formattedCellDate >= item.from_date &&
                                      formattedCellDate < item.to_date
                                    );
                                  }

                                  return item.date === formattedCellDate;
                                }
                              );

                              if (
                                inventoryRecord &&
                                inventoryRecord.from_date
                              ) {
                                const fromDate = new Date(
                                  inventoryRecord.from_date
                                );
                                return `${format(fromDate, "MMM dd")} - ${
                                  splitDate
                                    ? format(splitDate, "MMM dd, yyyy")
                                    : ""
                                }`;
                              }

                              return `${format(
                                selectedCell.date,
                                "MMM dd"
                              )} - ${
                                splitDate
                                  ? format(splitDate, "MMM dd, yyyy")
                                  : ""
                              }`;
                            })()}
                          </div>
                          <div className="text-xs mt-2 px-2 py-1 rounded-full inline-block bg-blue-100 text-blue-800">
                            {keepFirstPart ? "Current Room" : "Unallocated"}
                          </div>
                        </div>
                        <div className="p-3 bg-white rounded border border-gray-200">
                          <div className="text-sm font-medium">Second Part</div>
                          <div className="text-xs text-gray-500 mt-1">
                            {(() => {
                              // Find the inventory record for this cell to get date range
                              const formattedCellDate = format(
                                selectedCell.date,
                                "yyyy-MM-dd"
                              );
                              const inventoryRecord = availability.find(
                                (item: any) => {
                                  if (item.room_id !== selectedCell.roomId)
                                    return false;

                                  // Check if this is the right date
                                  if (item.from_date && item.to_date) {
                                    return (
                                      formattedCellDate >= item.from_date &&
                                      formattedCellDate < item.to_date
                                    );
                                  }

                                  return item.date === formattedCellDate;
                                }
                              );

                              if (
                                inventoryRecord &&
                                inventoryRecord.to_date &&
                                splitDate
                              ) {
                                const toDate = new Date(
                                  inventoryRecord.to_date
                                );
                                return `${format(
                                  splitDate,
                                  "MMM dd"
                                )} - ${format(toDate, "MMM dd, yyyy")}`;
                              }

                              return splitDate
                                ? `${format(splitDate, "MMM dd")} - ${format(
                                    addDays(selectedCell.date, 7),
                                    "MMM dd, yyyy"
                                  )}`
                                : "";
                            })()}
                          </div>
                          <div className="text-xs mt-2 px-2 py-1 rounded-full inline-block bg-blue-100 text-blue-800">
                            {!keepFirstPart ? "Current Room" : "Unallocated"}
                          </div>
                        </div>
                      </div>
                    </div>
                  )}
                </div>
              )}
            </div>
          </FocusModal.Body>
          <FocusModal.Footer>
            <div className="flex justify-end gap-2">
              <Button
                variant="secondary"
                onClick={() => setSplitModalOpen(false)}
              >
                Cancel
              </Button>
              <Button
                variant="primary"
                onClick={handleSplitBooking}
                disabled={!splitDate || isSplitting}
              >
                {isSplitting ? "Splitting..." : "Split Booking"}
              </Button>
            </div>
          </FocusModal.Footer>
        </FocusModal.Content>
      </FocusModal>
    </div>
  );
}
