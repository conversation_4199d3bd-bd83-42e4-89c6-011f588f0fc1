import { useState, useEffect } from "react";
import {
  Input,
  Button,
  Select,
  Textarea,
  Switch,
  Label,
  Text,
  Tooltip,
} from "@camped-ai/ui";
import { <PERSON><PERSON><PERSON><PERSON>, Controller } from "react-hook-form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { format } from "date-fns";
import {
  DollarSign,
  X,
  Hotel,
  Map,
  Image as ImageIcon,
  Info,
  Edit,
  Plus,
  Users,
  Package,
  Calendar,
} from "lucide-react";
import AddOnCategorySelect from "../../add-on-category-select";
import { CurrencySelector } from "../../common/currency-selector";

// Form schema
const addOnServiceFormSchema = z
  .object({
    name: z.string().min(1, "Name is required"),
    description: z.string().optional(),
    service_level: z.enum(["hotel", "destination"]),
    hotel_id: z.union([z.string(), z.array(z.string())]).optional(),
    destination_id: z.union([z.string(), z.array(z.string())]).optional(),
    category_id: z.string().optional(),
    is_active: z.boolean().default(true),
    start_date: z.date().optional().nullable(),
    end_date: z.date().optional().nullable(),
    max_capacity: z.number().optional().nullable(),
    // Pricing type
    pricing_type: z
      .enum(["per_person", "package", "usage_based"])
      .default("per_person"),
    // Per-person pricing
    adult_price: z.number().min(0).optional().nullable(),
    child_price: z.number().min(0).optional().nullable(),
    // Package pricing
    package_price: z.number().min(0).optional().nullable(),
    // Usage-based pricing
    per_day_adult_price: z.number().min(0).optional().nullable(),
    per_day_child_price: z.number().min(0).optional().nullable(),
    currency_code: z.string().optional(),
    images: z.array(z.string()).optional(),
    handle: z.string().optional(), // Add handle field for unique product identification
  })
  .refine(
    (data) => {
      // If service_level is hotel, hotel_id must be provided
      if (data.service_level === "hotel") {
        return !!data.hotel_id;
      }
      // If service_level is destination, destination_id must be provided
      if (data.service_level === "destination") {
        return !!data.destination_id;
      }
      return false;
    },
    {
      message:
        "hotel_id is required for hotel-level services, destination_id is required for destination-level services",
      path: ["service_level"],
    }
  )
  .refine(
    (data) => {
      // Validate pricing based on pricing type
      if (data.pricing_type === "package") {
        return (
          data.package_price !== null &&
          data.package_price !== undefined &&
          data.package_price > 0
        );
      } else if (data.pricing_type === "usage_based") {
        return (
          (data.per_day_adult_price !== null &&
            data.per_day_adult_price !== undefined &&
            data.per_day_adult_price > 0) ||
          (data.per_day_child_price !== null &&
            data.per_day_child_price !== undefined &&
            data.per_day_child_price > 0)
        );
      } else {
        return (
          (data.adult_price !== null &&
            data.adult_price !== undefined &&
            data.adult_price > 0) ||
          (data.child_price !== null &&
            data.child_price !== undefined &&
            data.child_price > 0)
        );
      }
    },
    {
      message:
        "For package pricing, package price is required. For per-person pricing, at least adult or child price is required. For usage-based pricing, at least per-day adult or child price is required.",
      path: ["pricing_type"],
    }
  );

interface AddOnServiceFormProps {
  initialData?: any;
  onSubmit: (data: any) => void;
  onCancel: () => void;
  hotelCurrency: string;
  hotelId?: string;
  destinationId?: string;
  availableHotels?: Array<{ id: string; name: string }>;
  availableDestinations?: Array<{ id: string; name: string }>;
  handle?: string; // Optional handle for existing services
}

export default function AddOnServiceForm({
  initialData,
  onSubmit,
  onCancel,
  hotelCurrency,
  hotelId,
  destinationId,
  availableHotels = [],
  availableDestinations = [],
}: AddOnServiceFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedImageFiles, setSelectedImageFiles] = useState<File[]>([]);
  const [imagePreviewUrls, setImagePreviewUrls] = useState<string[]>([]);

  // Cleanup preview URLs on unmount
  useEffect(() => {
    return () => {
      imagePreviewUrls.forEach((url) => {
        URL.revokeObjectURL(url);
      });
    };
  }, [imagePreviewUrls]);

  // Generate a unique handle for new services
  const generateUniqueHandle = (name: string, serviceLevel: string) => {
    const timestamp = new Date().getTime();
    const randomString = Math.random().toString(36).substring(2, 8);
    // Sanitize the name to create a URL-friendly base
    const sanitizedName = name
      ? name
          .toLowerCase()
          .replace(/[^a-z0-9]/g, "-")
          .replace(/-+/g, "-")
          .replace(/^-|-$/g, "")
      : "unnamed-service";
    return `${sanitizedName}-${serviceLevel}-${timestamp}-${randomString}`;
  };

  // Initialize form with default values or initial data
  const form = useForm({
    resolver: zodResolver(addOnServiceFormSchema),
    defaultValues: {
      name: initialData?.name || "",
      description: initialData?.description || "",
      service_level:
        initialData?.service_level || (hotelId ? "hotel" : "destination"),
      hotel_id: initialData?.hotel_id || hotelId || "",
      destination_id: initialData?.destination_id || destinationId || "",
      category_id: initialData?.category_id || "",
      is_active: initialData?.is_active ?? true,
      start_date: initialData?.start_date
        ? new Date(initialData.start_date)
        : null,
      end_date: initialData?.end_date ? new Date(initialData.end_date) : null,
      max_capacity: initialData?.max_capacity || null,
      // Pricing type
      pricing_type: initialData?.pricing_type || "per_person",
      // Try to get prices from metadata first, then fall back to direct properties
      // For adult price
      adult_price: initialData?.metadata?.adult_price
        ? initialData.metadata.adult_price / 100
        : initialData?.adult_price
        ? initialData.adult_price / 100
        : null, // Convert cents to dollars for display

      // For child price
      child_price: initialData?.metadata?.child_price
        ? initialData.metadata.child_price / 100
        : initialData?.child_price
        ? initialData.child_price / 100
        : null, // Convert cents to dollars for display

      // For package price
      package_price: initialData?.metadata?.package_price
        ? initialData.metadata.package_price / 100
        : initialData?.package_price
        ? initialData.package_price / 100
        : null, // Convert cents to dollars for display

      // For usage-based pricing
      per_day_adult_price: initialData?.metadata?.per_day_adult_price
        ? initialData.metadata.per_day_adult_price / 100
        : initialData?.per_day_adult_price
        ? initialData.per_day_adult_price / 100
        : null, // Convert cents to dollars for display

      per_day_child_price: initialData?.metadata?.per_day_child_price
        ? initialData.metadata.per_day_child_price / 100
        : initialData?.per_day_child_price
        ? initialData.per_day_child_price / 100
        : null, // Convert cents to dollars for display

      currency_code: initialData?.currency_code || initialData?.default_currency || hotelCurrency || "USD",
      images: initialData?.images || [],
      handle: initialData?.handle || "", // Keep existing handle if editing
    },
  });

  // Set hotel_id when form is initialized and hotelId is available
  useEffect(() => {
    if (hotelId && !form.getValues("hotel_id")) {
      console.log("Setting initial hotel_id:", hotelId);
      form.setValue("hotel_id", hotelId);
    }
  }, [hotelId, form]);

  // Handle form submission
  const handleSubmit = async (data: any) => {
    setIsSubmitting(true);
    try {
      // Format the data for submission
      // Generate a unique handle if not editing an existing service
      if (!initialData && !data.handle) {
        data.handle = generateUniqueHandle(data.name, data.service_level);
        console.log(`Generated unique handle for new service: ${data.handle}`);
      }

      // First, format the form data
      let formattedData = formatFormData(data);

      // Handle image deletions for existing services
      if (initialData?.id && initialData?.images) {
        const currentImages = form.getValues("images") || [];
        const existingImages = initialData.images || [];

        // Find images that were removed (exist in initial data but not in current form)
        const removedImages = existingImages.filter(
          (existingImg: string) => !currentImages.includes(existingImg)
        );

        // Update the formatted data to only include remaining images (excluding blob URLs)
        const finalImages = currentImages.filter(
          (img: string) => !img.startsWith("blob:")
        );
        formattedData = {
          ...formattedData,
          images: finalImages,
        };

        console.log("Existing images:", existingImages);
        console.log("Current images:", currentImages);
        console.log("Removed images:", removedImages);
        console.log("Final images for update:", finalImages);
      }

      // Submit the service data first
      onSubmit(formattedData);

      // Wait for 2 seconds after edit/create API call is done to allow refetch to complete
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // If there are selected image files and we have a service ID (for updates), upload the images
      if (selectedImageFiles.length > 0 && initialData?.id) {
        try {
          // Upload images one by one
          for (const file of selectedImageFiles) {
            const formData = new FormData();
            formData.append("files", file);

            const response = await fetch(
              `/admin/add-on-services/${initialData.id}/upload`,
              {
                method: "POST",
                body: formData,
              }
            );

            if (!response.ok) {
              const errorData = await response.json();
              console.error("Image upload failed:", errorData.message);
              // Don't throw error here, just log it since the service was already saved
            } else {
              console.log("Image uploaded successfully:", file.name);
            }
          }
        } catch (error) {
          console.error("Error uploading images:", error);
          // Don't throw error here, just log it since the service was already saved
        }
      }
    } catch (error) {
      console.error("Error submitting form:", error);
      throw error; // Re-throw to be handled by the calling component
    } finally {
      setIsSubmitting(false);
    }
  };

  // Format form data for submission
  const formatFormData = (data: any) => {
    // Keep destination_id as an array for multi-select functionality
    let destinationId = data.destination_id;
    if (
      data.service_level === "destination" &&
      Array.isArray(data.destination_id)
    ) {
      // Keep the array as is
      console.log(
        `Keeping destination_id as array: ${JSON.stringify(
          data.destination_id
        )}`
      );

      // If it's an empty array, set to empty string
      if (data.destination_id.length === 0) {
        destinationId = "";
        console.log("Empty destination_id array, setting to empty string");
      }
    }

    // Handle hotel_id - keep as array for multi-select functionality
    let hotelId = data.hotel_id;
    if (data.service_level === "hotel" && Array.isArray(data.hotel_id)) {
      // Keep the array as is
      console.log(
        `Keeping hotel_id as array: ${JSON.stringify(data.hotel_id)}`
      );

      // If it's an empty array, set to empty string
      if (data.hotel_id.length === 0) {
        hotelId = "";
        console.log("Empty hotel_id array, setting to empty string");
      }
    }

    console.log("Raw form data before formatting:", data);
    console.log("Pricing type from form:", data.pricing_type);
    console.log("Package price from form:", data.package_price);
    console.log("Adult price from form:", data.adult_price);
    console.log("Child price from form:", data.child_price);
    console.log("All form values:", form.getValues());

    // Build multi-currency pricing object
    const currentCurrency = data.currency_code || "USD";
    const existingPrices = initialData?.prices || {};

    // Create pricing object for the current currency
    const currentCurrencyPrices: any = {};

    if (data.pricing_type === "per_person") {
      if (data.adult_price !== null && data.adult_price !== undefined) {
        currentCurrencyPrices.adult_price = Math.round(data.adult_price * 100);
      }
      if (data.child_price !== null && data.child_price !== undefined) {
        currentCurrencyPrices.child_price = Math.round(data.child_price * 100);
      }
    } else if (data.pricing_type === "package") {
      if (data.package_price !== null && data.package_price !== undefined) {
        currentCurrencyPrices.package_price = Math.round(data.package_price * 100);
      }
    } else if (data.pricing_type === "usage_based") {
      if (data.per_day_adult_price !== null && data.per_day_adult_price !== undefined) {
        currentCurrencyPrices.per_day_adult_price = Math.round(data.per_day_adult_price * 100);
      }
      if (data.per_day_child_price !== null && data.per_day_child_price !== undefined) {
        currentCurrencyPrices.per_day_child_price = Math.round(data.per_day_child_price * 100);
      }
    }

    // Merge with existing prices for other currencies
    const updatedPrices = {
      ...existingPrices,
      [currentCurrency]: currentCurrencyPrices
    };

    const formattedData = {
      ...data,
      // Ensure pricing_type is explicitly set
      pricing_type: data.pricing_type || "per_person",
      // Format dates if provided
      start_date: data.start_date
        ? format(data.start_date, "yyyy-MM-dd")
        : undefined,
      end_date: data.end_date ? format(data.end_date, "yyyy-MM-dd") : undefined,

      // Multi-currency pricing
      prices: updatedPrices,
      default_currency: initialData?.default_currency || currentCurrency,

      // Legacy single-currency fields for backward compatibility
      adult_price:
        data.pricing_type === "per_person" &&
        data.adult_price !== null &&
        data.adult_price !== undefined
          ? Math.round(data.adult_price * 100)
          : undefined,
      child_price:
        data.pricing_type === "per_person" &&
        data.child_price !== null &&
        data.child_price !== undefined
          ? Math.round(data.child_price * 100)
          : undefined,
      package_price:
        data.pricing_type === "package" &&
        data.package_price !== null &&
        data.package_price !== undefined
          ? Math.round(data.package_price * 100)
          : undefined,
      per_day_adult_price:
        data.pricing_type === "usage_based" &&
        data.per_day_adult_price !== null &&
        data.per_day_adult_price !== undefined
          ? Math.round(data.per_day_adult_price * 100)
          : undefined,
      per_day_child_price:
        data.pricing_type === "usage_based" &&
        data.per_day_child_price !== null &&
        data.per_day_child_price !== undefined
          ? Math.round(data.per_day_child_price * 100)
          : undefined,

      // Currency code
      currency_code: currentCurrency,

      // Handle IDs based on service_level
      hotel_id: data.service_level === "hotel" ? hotelId : undefined,
      destination_id:
        data.service_level === "destination" ? destinationId : undefined,
    };

    console.log("Formatted data for submission:", formattedData);
    console.log("Final pricing type:", formattedData.pricing_type);
    console.log("Final package price:", formattedData.package_price);

    return formattedData;
  };

  // Watch form values for conditional rendering
  const watchServiceLevel = form.watch("service_level");

  // Watch price values for debugging
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (
        name === "adult_price" ||
        name === "child_price" ||
        name === "pricing_type" ||
        name === "package_price"
      ) {
        console.log(`Form value changed - ${name}:`, value[name]);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Watch pricing type and set currency to USD for package pricing
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "pricing_type" && value.pricing_type === "package") {
        console.log("Setting currency to USD for package pricing");
        form.setValue("currency_code", "USD");
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  // Watch currency changes and update price fields accordingly
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "currency_code" && initialData?.prices) {
        const selectedCurrency = value.currency_code;
        const prices = initialData.prices;

        console.log("Currency changed to:", selectedCurrency);
        console.log("Available prices:", prices);

        if (prices[selectedCurrency]) {
          // Load prices for the selected currency
          const currencyPrices = prices[selectedCurrency];
          console.log("Loading prices for", selectedCurrency, ":", currencyPrices);

          // Update form fields with prices for the selected currency (convert from cents to dollars)
          if (currencyPrices.adult_price !== undefined) {
            form.setValue("adult_price", currencyPrices.adult_price / 100);
          }
          if (currencyPrices.child_price !== undefined) {
            form.setValue("child_price", currencyPrices.child_price / 100);
          }
          if (currencyPrices.package_price !== undefined) {
            form.setValue("package_price", currencyPrices.package_price / 100);
          }
          if (currencyPrices.per_day_adult_price !== undefined) {
            form.setValue("per_day_adult_price", currencyPrices.per_day_adult_price / 100);
          }
          if (currencyPrices.per_day_child_price !== undefined) {
            form.setValue("per_day_child_price", currencyPrices.per_day_child_price / 100);
          }
        } else {
          // Clear price fields if no prices exist for the selected currency
          console.log("No prices found for", selectedCurrency, "- clearing price fields");
          form.setValue("adult_price", null);
          form.setValue("child_price", null);
          form.setValue("package_price", null);
          form.setValue("per_day_adult_price", null);
          form.setValue("per_day_child_price", null);
        }
      }
    });
    return () => subscription.unsubscribe();
  }, [form, initialData]);

  // Description tooltip content
  const descriptionTooltip =
    "Include all relevant details such as:\n" +
    "• Duration (hours/minutes)\n" +
    "• Location where the service takes place\n" +
    "• Meeting point for guests\n" +
    "• What's included in the service\n" +
    "• What's not included in the service\n" +
    "• Any requirements or restrictions";

  // Debug available options and price data
  console.log("Available Hotels:", availableHotels);
  console.log("Available Destinations:", availableDestinations);
  console.log("Hotel ID:", hotelId);
  console.log("Destination ID:", destinationId);

  // Log price information for debugging
  if (initialData) {
    console.log("Initial Data Price Info:", {
      adult_price: initialData.adult_price,
      child_price: initialData.child_price,
      metadata_adult_price: initialData.metadata?.adult_price,
      metadata_child_price: initialData.metadata?.child_price,
      adult_price_formatted: initialData.metadata?.adult_price_formatted,
      child_price_formatted: initialData.metadata?.child_price_formatted,
    });
  }

  console.log("Form Values:", form.getValues());

  // Ensure hotel_id is set when in hotel context
  useEffect(() => {
    console.log("Effect running with hotelId:", hotelId);
    if (hotelId) {
      console.log("Setting hotel_id to:", hotelId);
      form.setValue("service_level", "hotel");
      form.setValue("hotel_id", hotelId);

      // Force a re-render to make sure the form reflects the new values
      setTimeout(() => {
        form.trigger("service_level");
        form.trigger("hotel_id");
      }, 0);
    }
  }, [hotelId, form]);

  // Fetch destinations directly if needed
  const [fetchedDestinations, setFetchedDestinations] = useState<
    Array<{ id: string; name: string }>
  >([]);

  // Fetch destinations directly if none are provided
  useEffect(() => {
    const fetchDestinations = async () => {
      if (availableDestinations && availableDestinations.length > 0) {
        console.log(
          "Using provided destinations:",
          availableDestinations.length
        );
        setFetchedDestinations(availableDestinations);
        return;
      }

      try {
        console.log("Fetching destinations directly from form component...");
        const response = await fetch(`/admin/hotel-management/destinations`);
        if (!response.ok) {
          throw new Error("Failed to fetch destinations");
        }
        const data = await response.json();
        console.log("Fetched destinations:", data);

        if (data.destinations && data.destinations.length > 0) {
          console.log(
            "Setting fetched destinations:",
            data.destinations.length
          );
          setFetchedDestinations(data.destinations);
        } else {
          console.warn("No destinations found in API response");
        }
      } catch (error) {
        console.error("Error fetching destinations:", error);
      }
    };

    fetchDestinations();
  }, [availableDestinations]);

  // Set initial values when the component mounts
  useEffect(() => {
    if (hotelId) {
      console.log("Initial setup - setting hotel_id to:", hotelId);
      form.setValue("service_level", "hotel");
      form.setValue("hotel_id", hotelId);
    }

    // Log available hotels and destinations
    console.log(`Available hotels: ${availableHotels?.length || 0}`);
    console.log(
      `Available destinations: ${availableDestinations?.length || 0}`
    );
    console.log(`Fetched destinations: ${fetchedDestinations?.length || 0}`);
  }, [
    hotelId,
    form,
    availableHotels,
    availableDestinations,
    fetchedDestinations,
  ]);

  return (
    <FormProvider {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-8 p-6 relative"
      >
        {/* Basic Information Section */}
        <div className="bg-gray-50 p-5 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium mb-4 text-gray-800">
            Basic Information
          </h3>

          {/* Name */}
          <div className="space-y-2 mb-5">
            <Label htmlFor="name" className="text-gray-700 font-medium">
              Service Name
            </Label>
            <Controller
              control={form.control}
              name="name"
              render={({ field }) => (
                <>
                  <Input
                    {...field}
                    id="name"
                    placeholder="Enter service name"
                    className="h-10"
                  />
                  {form.formState.errors.name && (
                    <p className="text-sm text-red-500 mt-1">
                      {form.formState.errors.name.message?.toString()}
                    </p>
                  )}
                </>
              )}
            />
          </div>

          {/* Description with tooltip */}
          <div className="space-y-2 mb-5">
            <div className="flex items-center space-x-2">
              <Label
                htmlFor="description"
                className="text-gray-700 font-medium"
              >
                Description
              </Label>
              <Tooltip content={descriptionTooltip}>
                <Info className="h-4 w-4 text-gray-400" />
              </Tooltip>
            </div>
            <Controller
              control={form.control}
              name="description"
              render={({ field }) => (
                <Textarea
                  {...field}
                  id="description"
                  placeholder="Describe the service in detail including duration, location, what's included, etc."
                  rows={5}
                  className="resize-none"
                />
              )}
            />
          </div>

          {/* Category Selection */}
          <div className="space-y-2">
            <Label htmlFor="category_id" className="text-gray-700 font-medium">
              Category
            </Label>
            <Controller
              control={form.control}
              name="category_id"
              render={({ field }) => (
                <AddOnCategorySelect
                  value={field.value || ""}
                  onChange={field.onChange}
                  placeholder="Select a category (optional)"
                  error={form.formState.errors.category_id?.message?.toString()}
                />
              )}
            />
          </div>
        </div>

        {/* Service Level Section */}
        <div className="bg-gray-50 p-5 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium mb-4 text-gray-800">
            Service Level
          </h3>

          <Controller
            control={form.control}
            name="service_level"
            render={({ field }) => (
              <div className="flex flex-col space-y-4 sm:flex-row sm:space-y-0 sm:space-x-6">
                <div
                  className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                    field.value === "hotel" || (!!hotelId && !field.value)
                      ? "bg-blue-50 border-blue-300"
                      : "bg-white border-gray-200 hover:bg-gray-50"
                  }`}
                  onClick={() => {
                    field.onChange("hotel");
                    if (hotelId) {
                      form.setValue("hotel_id", hotelId);
                    }
                  }}
                >
                  <input
                    type="radio"
                    id="hotel-level"
                    value="hotel"
                    checked={
                      field.value === "hotel" || (!!hotelId && !field.value)
                    }
                    onChange={() => {
                      field.onChange("hotel");
                      if (hotelId) {
                        form.setValue("hotel_id", hotelId);
                      }
                    }}
                    className="h-4 w-4 mr-3"
                  />
                  <label
                    htmlFor="hotel-level"
                    className="flex items-center cursor-pointer"
                  >
                    <div className="bg-blue-100 p-2 rounded-full mr-3">
                      <Hotel className="w-5 h-5 text-blue-600" />
                    </div>
                    <div>
                      <span className="font-medium block">Hotel Service</span>
                      <span className="text-sm text-gray-500">
                        Service available at specific hotels
                      </span>
                    </div>
                  </label>
                </div>

                <div
                  className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                    field.value === "destination"
                      ? "bg-green-50 border-green-300"
                      : "bg-white border-gray-200 hover:bg-gray-50"
                  }`}
                  onClick={() => field.onChange("destination")}
                >
                  <input
                    type="radio"
                    id="destination-level"
                    value="destination"
                    checked={field.value === "destination"}
                    onChange={() => field.onChange("destination")}
                    className="h-4 w-4 mr-3"
                  />
                  <label
                    htmlFor="destination-level"
                    className="flex items-center cursor-pointer"
                  >
                    <div className="bg-green-100 p-2 rounded-full mr-3">
                      <Map className="w-5 h-5 text-green-600" />
                    </div>
                    <div>
                      <span className="font-medium block">
                        Destination Service
                      </span>
                      <span className="text-sm text-gray-500">
                        Service available across destinations
                      </span>
                    </div>
                  </label>
                </div>
              </div>
            )}
          />
        </div>

        {/* Hotel selector */}
        {watchServiceLevel === "hotel" && (
          <div className="bg-gray-50 p-5 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium mb-4 text-gray-800">
              Select Hotels
            </h3>
            <Controller
              control={form.control}
              name="hotel_id"
              render={({ field }) => (
                <>
                  {/* Show hotel info */}
                  {availableHotels && availableHotels.length > 0 ? (
                    <div>
                      <div className="relative">
                        <div className="mb-2 text-sm text-gray-700 font-medium">
                          Choose hotels where this service will be available:
                        </div>
                        <select
                          id="hotel_id"
                          multiple
                          value={
                            Array.isArray(field.value)
                              ? field.value
                              : field.value
                              ? [field.value]
                              : []
                          }
                          onChange={(e) => {
                            // Get all selected options
                            const selectedOptions = Array.from(
                              e.target.selectedOptions
                            ).map((option) => option.value);
                            field.onChange(selectedOptions);
                          }}
                          className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                          aria-label="Select hotels (hold Ctrl/Cmd to select multiple)"
                        >
                          <option
                            value=""
                            disabled={
                              Array.isArray(field.value) &&
                              field.value.length > 0
                            }
                          >
                            Select hotels
                          </option>
                          {availableHotels.map((hotel) => (
                            <option key={hotel.id} value={hotel.id}>
                              {hotel.name}
                            </option>
                          ))}
                        </select>
                        <div className="mt-2 flex items-center text-sm text-gray-500">
                          <Info className="w-4 h-4 mr-1" />
                          Hold Ctrl/Cmd to select multiple hotels
                        </div>
                      </div>

                      {/* Selected hotels display */}
                      <div className="mt-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">
                          Selected Hotels:
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {(() => {
                            // Handle both string and array values
                            const ids = Array.isArray(field.value)
                              ? field.value
                              : field.value
                              ? [field.value]
                              : [];

                            if (ids.length === 0) {
                              return (
                                <div className="text-sm text-gray-500 italic">
                                  No hotels selected
                                </div>
                              );
                            }

                            return ids.map((id) => {
                              const hotel = availableHotels.find(
                                (h) => h.id === id
                              );
                              return hotel ? (
                                <div
                                  key={id}
                                  className="bg-blue-50 text-blue-700 px-3 py-1 rounded-full text-sm font-medium flex items-center"
                                >
                                  <Hotel className="w-3 h-3 mr-1" />
                                  {hotel.name}
                                </div>
                              ) : null;
                            });
                          })()}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <Input
                        {...field}
                        id="hotel_id"
                        placeholder="Hotel ID"
                        value={field.value || ""}
                        className="bg-white"
                      />
                      <div className="mt-3 text-sm font-medium bg-yellow-50 p-3 rounded-md border border-yellow-200 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-yellow-500 mr-2"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                        No hotels available. Please add hotels first.
                      </div>
                    </div>
                  )}
                </>
              )}
            />
          </div>
        )}

        {/* Destination selector */}
        {watchServiceLevel === "destination" && (
          <div className="bg-gray-50 p-5 rounded-lg border border-gray-200">
            <h3 className="text-lg font-medium mb-4 text-gray-800">
              Select Destinations
            </h3>
            <Controller
              control={form.control}
              name="destination_id"
              render={({ field }) => (
                <>
                  {fetchedDestinations.length > 0 ||
                  availableDestinations.length > 0 ? (
                    <div>
                      <div className="relative">
                        <div className="mb-2 text-sm text-gray-700 font-medium">
                          Choose destinations where this service will be
                          available:
                        </div>
                        <select
                          id="destination_id"
                          multiple
                          value={
                            Array.isArray(field.value)
                              ? field.value
                              : field.value
                              ? [field.value]
                              : []
                          }
                          onChange={(e) => {
                            // Get all selected options
                            const selectedOptions = Array.from(
                              e.target.selectedOptions
                            ).map((option) => option.value);
                            field.onChange(selectedOptions);
                          }}
                          className="w-full h-40 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
                          aria-label="Select destinations (hold Ctrl/Cmd to select multiple)"
                        >
                          <option
                            value=""
                            disabled={
                              Array.isArray(field.value) &&
                              field.value.length > 0
                            }
                          >
                            Select destinations
                          </option>
                          {(fetchedDestinations.length > 0
                            ? fetchedDestinations
                            : availableDestinations
                          ).map((destination) => (
                            <option key={destination.id} value={destination.id}>
                              {destination.name}
                            </option>
                          ))}
                        </select>
                        <div className="mt-2 flex items-center text-sm text-gray-500">
                          <Info className="w-4 h-4 mr-1" />
                          Hold Ctrl/Cmd to select multiple destinations
                        </div>
                      </div>

                      {/* Selected destinations display */}
                      <div className="mt-4">
                        <div className="text-sm font-medium text-gray-700 mb-2">
                          Selected Destinations:
                        </div>
                        <div className="flex flex-wrap gap-2">
                          {(() => {
                            // Handle both string and array values
                            const ids = Array.isArray(field.value)
                              ? field.value
                              : field.value
                              ? [field.value]
                              : [];

                            if (ids.length === 0) {
                              return (
                                <div className="text-sm text-gray-500 italic">
                                  No destinations selected
                                </div>
                              );
                            }

                            return ids.map((id) => {
                              const dest = (
                                fetchedDestinations.length > 0
                                  ? fetchedDestinations
                                  : availableDestinations
                              ).find((d) => d.id === id);
                              return dest ? (
                                <div
                                  key={id}
                                  className="bg-green-50 text-green-700 px-3 py-1 rounded-full text-sm font-medium flex items-center"
                                >
                                  <Map className="w-3 h-3 mr-1" />
                                  {dest.name}
                                </div>
                              ) : null;
                            });
                          })()}
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div>
                      <Input
                        {...field}
                        id="destination_id"
                        placeholder="Destination ID"
                        value={field.value || destinationId || ""}
                        disabled={!!destinationId}
                        className="bg-white"
                      />
                      <div className="mt-3 text-sm font-medium bg-yellow-50 p-3 rounded-md border border-yellow-200 flex items-center">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 text-yellow-500 mr-2"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path
                            fillRule="evenodd"
                            d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                            clipRule="evenodd"
                          />
                        </svg>
                        No destinations available. Please add destinations
                        first.
                      </div>
                    </div>
                  )}
                </>
              )}
            />
          </div>
        )}

        {/* Settings Section */}
        <div className="bg-gray-50 p-5 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium mb-4 text-gray-800">
            Service Settings
          </h3>

          {/* Active Status */}
          <div className="mb-6 p-4 bg-white rounded-md border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <Controller
                  control={form.control}
                  name="is_active"
                  render={({ field }) => (
                    <Switch
                      id="is_active"
                      checked={field.value}
                      onCheckedChange={field.onChange}
                      className="data-[state=checked]:bg-green-500"
                    />
                  )}
                />
                <div>
                  <Label
                    htmlFor="is_active"
                    className="text-gray-700 font-medium block"
                  >
                    {form.watch("is_active") ? "Active" : "Inactive"}
                  </Label>
                  <Text size="small" className="text-gray-500">
                    {form.watch("is_active")
                      ? "Service is available for booking"
                      : "Service is hidden from booking"}
                  </Text>
                </div>
              </div>
              <div
                className={`px-2 py-1 rounded-full text-xs font-medium ${
                  form.watch("is_active")
                    ? "bg-green-100 text-green-800"
                    : "bg-gray-100 text-gray-800"
                }`}
              >
                {form.watch("is_active") ? "Available" : "Hidden"}
              </div>
            </div>
          </div>

          {/* Maximum Capacity */}
          <div className="mb-4">
            <Label
              htmlFor="max_capacity"
              className="text-gray-700 font-medium block mb-2"
            >
              Maximum Capacity
            </Label>
            <div className="flex items-center gap-3">
              <div className="relative flex-grow">
                <Controller
                  control={form.control}
                  name="max_capacity"
                  render={({ field }) => (
                    <Input
                      {...field}
                      id="max_capacity"
                      type="number"
                      min="0"
                      placeholder="Leave empty for unlimited"
                      value={field.value === null ? "" : field.value}
                      onChange={(e) => {
                        const value =
                          e.target.value === ""
                            ? null
                            : parseInt(e.target.value);
                        field.onChange(value);
                      }}
                      className="bg-white h-10"
                    />
                  )}
                />
              </div>
              <div className="bg-blue-50 text-blue-700 px-3 py-1 rounded-md text-sm font-medium whitespace-nowrap">
                {form.watch("max_capacity") === null ||
                form.watch("max_capacity") === 999999
                  ? "Unlimited"
                  : `Max ${form.watch("max_capacity")} guests`}
              </div>
            </div>
            <Text size="small" className="text-gray-500 mt-2 flex items-center">
              <Info className="w-3 h-3 mr-1" />
              Maximum number of guests that can book this service
            </Text>
          </div>
        </div>

        {/* Pricing Section */}
        <div className="bg-gray-50 p-5 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium mb-4 text-gray-800">Pricing</h3>

          {/* Pricing Type Selector */}
          <div className="mb-6">
            <Label className="text-gray-700 font-medium block mb-3">
              Pricing Type
            </Label>
            <Controller
              control={form.control}
              name="pricing_type"
              render={({ field }) => (
                <div className="flex flex-col space-y-4 lg:flex-row lg:space-y-0 lg:space-x-4">
                  <div
                    className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                      field.value === "per_person"
                        ? "bg-blue-50 border-blue-300"
                        : "bg-white border-gray-200 hover:bg-gray-50"
                    }`}
                    onClick={() => field.onChange("per_person")}
                  >
                    <input
                      type="radio"
                      id="per-person-pricing"
                      value="per_person"
                      checked={field.value === "per_person"}
                      onChange={() => field.onChange("per_person")}
                      className="h-4 w-4 mr-3"
                    />
                    <label
                      htmlFor="per-person-pricing"
                      className="flex items-center cursor-pointer"
                    >
                      <div className="bg-blue-100 p-2 rounded-full mr-3">
                        <Users className="w-5 h-5 text-blue-600" />
                      </div>
                      <div>
                        <span className="font-medium block">Per Person</span>
                        <span className="text-sm text-gray-500">
                          Separate pricing for adults and children
                        </span>
                      </div>
                    </label>
                  </div>

                  <div
                    className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                      field.value === "package"
                        ? "bg-green-50 border-green-300"
                        : "bg-white border-gray-200 hover:bg-gray-50"
                    }`}
                    onClick={() => field.onChange("package")}
                  >
                    <input
                      type="radio"
                      id="package-pricing"
                      value="package"
                      checked={field.value === "package"}
                      onChange={() => field.onChange("package")}
                      className="h-4 w-4 mr-3"
                    />
                    <label
                      htmlFor="package-pricing"
                      className="flex items-center cursor-pointer"
                    >
                      <div className="bg-green-100 p-2 rounded-full mr-3">
                        <Package className="w-5 h-5 text-green-600" />
                      </div>
                      <div>
                        <span className="font-medium block">Package</span>
                        <span className="text-sm text-gray-500">
                          Flat rate regardless of occupancy
                        </span>
                      </div>
                    </label>
                  </div>

                  <div
                    className={`flex items-center p-4 border rounded-lg cursor-pointer transition-colors ${
                      field.value === "usage_based"
                        ? "bg-purple-50 border-purple-300"
                        : "bg-white border-gray-200 hover:bg-gray-50"
                    }`}
                    onClick={() => field.onChange("usage_based")}
                  >
                    <input
                      type="radio"
                      id="usage-based-pricing"
                      value="usage_based"
                      checked={field.value === "usage_based"}
                      onChange={() => field.onChange("usage_based")}
                      className="h-4 w-4 mr-3"
                    />
                    <label
                      htmlFor="usage-based-pricing"
                      className="flex items-center cursor-pointer"
                    >
                      <div className="bg-purple-100 p-2 rounded-full mr-3">
                        <Calendar className="w-5 h-5 text-purple-600" />
                      </div>
                      <div>
                        <span className="font-medium block">Usage Based</span>
                        <span className="text-sm text-gray-500">
                          Per-day pricing for selected dates
                        </span>
                      </div>
                    </label>
                  </div>
                </div>
              )}
            />
          </div>

          {/* Pricing Fields */}
          {form.watch("pricing_type") === "per_person" ? (
            <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
               <div className="bg-white p-4 rounded-md border border-gray-200">
                <Controller
                  control={form.control}
                  name="currency_code"
                  render={({ field }) => (
                    <CurrencySelector
                      value={field.value}
                      onChange={field.onChange}
                      label="Currency"
                      id="currency_code"
                      className="w-full"
                    />
                  )}
                />
              </div>
              <div className="bg-white p-4 rounded-md border border-gray-200">
                <Label
                  htmlFor="adult_price"
                  className="text-gray-700 font-medium block mb-2"
                >
                  Adult Price
                </Label>
                <Controller
                  control={form.control}
                  name="adult_price"
                  render={({ field }) => (
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                        {form.watch("currency_code") || "USD"}
                      </div>
                      <Input
                        {...field}
                        id="adult_price"
                        type="number"
                        min="0"
                        step="0.01"
                        className="pl-14 h-10"
                        placeholder="0.00"
                        value={field.value === null ? "" : field.value}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? null
                              : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </div>
                  )}
                />
                <Text size="small" className="text-gray-500 mt-2">
                  Price per adult for this service
                </Text>
              </div>

              <div className="bg-white p-4 rounded-md border border-gray-200">
                <Label
                  htmlFor="child_price"
                  className="text-gray-700 font-medium block mb-2"
                >
                  Child Price
                </Label>
                <Controller
                  control={form.control}
                  name="child_price"
                  render={({ field }) => (
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                        {form.watch("currency_code") || "USD"}
                      </div>
                      <Input
                        {...field}
                        id="child_price"
                        type="number"
                        min="0"
                        step="0.01"
                        className="pl-14 h-10"
                        placeholder="0.00"
                        value={field.value === null ? "" : field.value}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? null
                              : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </div>
                  )}
                />
                <Text size="small" className="text-gray-500 mt-2">
                  Price per child for this service
                </Text>
              </div>

             
            </div>
          ) : form.watch("pricing_type") === "usage_based" ? (
            // Usage-based pricing fields
            <div className="grid grid-cols-1 md:grid-cols-3 gap-5">
              <div className="bg-white p-4 rounded-md border border-gray-200">
                <Label
                  htmlFor="per_day_adult_price"
                  className="text-gray-700 font-medium block mb-2"
                >
                  Adult Price (Per Day)
                </Label>
                <Controller
                  control={form.control}
                  name="per_day_adult_price"
                  render={({ field }) => (
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                        {form.watch("currency_code") || "USD"}
                      </div>
                      <Input
                        {...field}
                        id="per_day_adult_price"
                        type="number"
                        min="0"
                        step="0.01"
                        className="pl-14 h-10"
                        placeholder="0.00"
                        value={field.value === null ? "" : field.value}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? null
                              : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </div>
                  )}
                />
                <Text size="small" className="text-gray-500 mt-2">
                  Price per adult per day of usage
                </Text>
              </div>

              <div className="bg-white p-4 rounded-md border border-gray-200">
                <Label
                  htmlFor="per_day_child_price"
                  className="text-gray-700 font-medium block mb-2"
                >
                  Child Price (Per Day)
                </Label>
                <Controller
                  control={form.control}
                  name="per_day_child_price"
                  render={({ field }) => (
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                        {form.watch("currency_code") || "USD"}
                      </div>
                      <Input
                        {...field}
                        id="per_day_child_price"
                        type="number"
                        min="0"
                        step="0.01"
                        className="pl-14 h-10"
                        placeholder="0.00"
                        value={field.value === null ? "" : field.value}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? null
                              : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </div>
                  )}
                />
                <Text size="small" className="text-gray-500 mt-2">
                  Price per child per day of usage
                </Text>
              </div>

              <div className="bg-white p-4 rounded-md border border-gray-200">
                <Controller
                  control={form.control}
                  name="currency_code"
                  render={({ field }) => (
                    <CurrencySelector
                      value={field.value}
                      onChange={field.onChange}
                      label="Currency"
                      id="currency_code_usage"
                      className="w-full"
                    />
                  )}
                />
                <Text size="small" className="text-gray-500 mt-2">
                  Currency for pricing
                </Text>
              </div>
            </div>
          ) : (
            // Package pricing fields
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <div className="bg-white p-4 rounded-md border border-gray-200">
                <Label
                  htmlFor="package_price"
                  className="text-gray-700 font-medium block mb-2"
                >
                  Package Price
                </Label>
                <Controller
                  control={form.control}
                  name="package_price"
                  render={({ field }) => (
                    <div className="relative">
                      <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 font-medium">
                        {form.watch("currency_code") || "USD"}
                      </div>
                      <Input
                        {...field}
                        id="package_price"
                        type="number"
                        min="0"
                        step="0.01"
                        className="pl-14 h-10"
                        placeholder="0.00"
                        value={field.value === null ? "" : field.value}
                        onChange={(e) => {
                          const value =
                            e.target.value === ""
                              ? null
                              : parseFloat(e.target.value);
                          field.onChange(value);
                        }}
                      />
                    </div>
                  )}
                />
                <Text size="small" className="text-gray-500 mt-2">
                  Flat rate for the entire package
                </Text>
              </div>

              <div className="bg-white p-4 rounded-md border border-gray-200">
                <Controller
                  control={form.control}
                  name="currency_code"
                  render={({ field }) => (
                    <CurrencySelector
                      value={field.value}
                      onChange={field.onChange}
                      label="Currency"
                      id="currency_code_package"
                      className="w-full"
                    />
                  )}
                />
                <Text size="small" className="text-gray-500 mt-2">
                  Currency for pricing
                </Text>
              </div>
            </div>
          )}

          {/* Price Preview */}
          <div className="mt-5 p-3 bg-blue-50 rounded-md border border-blue-100 flex items-center justify-between">
            <div className="flex items-center">
              <DollarSign className="w-5 h-5 text-blue-600 mr-2" />
              <span className="text-blue-800 font-medium">Price Preview</span>
            </div>
            <div className="text-blue-800">
              {form.watch("pricing_type") === "package" ? (
                form.watch("package_price") ? (
                  <span>
                    {`${form.watch("currency_code") || "USD"} ${Number(
                      form.watch("package_price")
                    ).toFixed(2)} (Package)`}
                  </span>
                ) : (
                  <span className="text-blue-600 italic">
                    No package price set
                  </span>
                )
              ) : form.watch("pricing_type") === "usage_based" ? (
                form.watch("per_day_adult_price") ||
                form.watch("per_day_child_price") ? (
                  <span>
                    {form.watch("per_day_adult_price") !== null &&
                    form.watch("per_day_adult_price") !== undefined
                      ? `${form.watch("currency_code") || "USD"} ${Number(
                          form.watch("per_day_adult_price")
                        ).toFixed(2)} (Adult/Day)`
                      : ""}
                    {form.watch("per_day_adult_price") !== null &&
                    form.watch("per_day_child_price") !== null
                      ? " / "
                      : ""}
                    {form.watch("per_day_child_price") !== null &&
                    form.watch("per_day_child_price") !== undefined
                      ? `${form.watch("currency_code") || "USD"} ${Number(
                          form.watch("per_day_child_price")
                        ).toFixed(2)} (Child/Day)`
                      : ""}
                  </span>
                ) : (
                  <span className="text-blue-600 italic">
                    No per-day prices set
                  </span>
                )
              ) : form.watch("adult_price") || form.watch("child_price") ? (
                <span>
                  {form.watch("adult_price") !== null &&
                  form.watch("adult_price") !== undefined
                    ? `${form.watch("currency_code") || "USD"} ${Number(
                        form.watch("adult_price")
                      ).toFixed(2)} (Adult)`
                    : ""}
                  {form.watch("adult_price") !== null &&
                  form.watch("child_price") !== null
                    ? " / "
                    : ""}
                  {form.watch("child_price") !== null &&
                  form.watch("child_price") !== undefined
                    ? `${form.watch("currency_code") || "USD"} ${Number(
                        form.watch("child_price")
                      ).toFixed(2)} (Child)`
                    : ""}
                </span>
              ) : (
                <span className="text-blue-600 italic">No prices set</span>
              )}
            </div>
          </div>
        </div>

        {/* Images Section */}
        <div className="bg-gray-50 p-5 rounded-lg border border-gray-200">
          <h3 className="text-lg font-medium mb-4 text-gray-800">
            Service Images
          </h3>

          <Controller
            control={form.control}
            name="images"
            render={({ field }) => {
              const handleFilesSelect = (files: FileList) => {
                if (!files || files.length === 0) return;

                const newFiles = Array.from(files);
                const newPreviewUrls: string[] = [];

                // Create preview URLs for new files
                newFiles.forEach((file) => {
                  const previewUrl = URL.createObjectURL(file);
                  newPreviewUrls.push(previewUrl);
                });

                // Add to existing files and preview URLs
                setSelectedImageFiles((prev) => [...prev, ...newFiles]);
                setImagePreviewUrls((prev) => [...prev, ...newPreviewUrls]);

                // Update form field with all preview URLs
                const allPreviewUrls = [...imagePreviewUrls, ...newPreviewUrls];
                const existingImages = field.value || [];
                const combinedImages = [
                  ...existingImages.filter(
                    (img: string) => !img.startsWith("blob:")
                  ),
                  ...allPreviewUrls,
                ];
                field.onChange(combinedImages);
              };

              const removeImage = (index: number) => {
                const currentImages = field.value || [];
                const imageToRemove = currentImages[index];

                // If it's a preview URL, clean it up and remove from state
                if (imageToRemove && imageToRemove.startsWith("blob:")) {
                  URL.revokeObjectURL(imageToRemove);
                  const previewIndex = imagePreviewUrls.indexOf(imageToRemove);
                  if (previewIndex !== -1) {
                    setSelectedImageFiles((prev) =>
                      prev.filter((_, i) => i !== previewIndex)
                    );
                    setImagePreviewUrls((prev) =>
                      prev.filter((_, i) => i !== previewIndex)
                    );
                  }
                }

                // Remove from form field
                const newImages = currentImages.filter(
                  (_: string, i: number) => i !== index
                );
                field.onChange(newImages);
              };

              const displayImages = field.value || [];

              return (
                <div>
                  <div className="mb-3">
                    <Label
                      htmlFor="images"
                      className="text-gray-700 font-medium"
                    >
                      Images
                    </Label>
                    <Text size="small" className="text-gray-500">
                      Select multiple images to showcase this service
                    </Text>
                  </div>

                  {displayImages.length === 0 ? (
                    <div className="bg-white p-8 border border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center text-center">
                      <ImageIcon className="w-12 h-12 text-gray-300 mb-3" />
                      <Text className="text-gray-500 mb-4">
                        No images selected yet
                      </Text>
                      <input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => {
                          if (e.target.files) {
                            handleFilesSelect(e.target.files);
                          }
                        }}
                        className="hidden"
                        id="image-upload-input"
                      />
                      <Button
                        type="button"
                        variant="secondary"
                        size="small"
                        onClick={() => {
                          document
                            .getElementById("image-upload-input")
                            ?.click();
                        }}
                      >
                        Select Images
                      </Button>
                    </div>
                  ) : (
                    <div>
                      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4 mb-4">
                        {displayImages.map((image: string, index: number) => (
                          <div
                            key={index}
                            className="relative group bg-white rounded-md overflow-hidden border border-gray-200 aspect-square"
                          >
                            <img
                              src={image}
                              alt={`Service image ${index + 1}`}
                              className="w-full h-full object-cover"
                              onError={() => {
                                console.error("Image failed to load:", image);
                              }}
                            />
                            <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all flex items-center justify-center opacity-0 group-hover:opacity-100">
                              <button
                                type="button"
                                className="bg-red-500 text-white rounded-full p-2 transform scale-90 hover:scale-100 transition-transform"
                                onClick={() => removeImage(index)}
                                title="Remove image"
                              >
                                <X className="w-4 h-4" />
                              </button>
                            </div>
                            <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-50 text-white text-xs py-1 px-2">
                              Image {index + 1}
                            </div>
                          </div>
                        ))}

                        {/* Add more images button */}
                        <div
                          className="aspect-square border-2 border-dashed border-gray-300 rounded-md flex flex-col items-center justify-center cursor-pointer hover:bg-gray-50 transition-colors"
                          onClick={() => {
                            document
                              .getElementById("image-upload-input")
                              ?.click();
                          }}
                        >
                          <ImageIcon className="w-8 h-8 text-gray-400 mb-2" />
                          <span className="text-sm text-gray-500">
                            Add More
                          </span>
                        </div>
                      </div>

                      <input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => {
                          if (e.target.files) {
                            handleFilesSelect(e.target.files);
                          }
                        }}
                        className="hidden"
                        id="image-upload-input"
                      />
                    </div>
                  )}
                </div>
              );
            }}
          />
        </div>

        {/* Form Actions */}
        <div className="sticky bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 mt-8 -mx-6 -mb-6 flex justify-end space-x-3 shadow-md z-10">
          <Button
            type="button"
            variant="secondary"
            onClick={onCancel}
            disabled={isSubmitting}
            className="px-5"
          >
            Cancel
          </Button>
          <Button type="submit" disabled={isSubmitting} className="px-5">
            {isSubmitting ? (
              <span className="flex items-center">
                <svg
                  className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                  xmlns="http://www.w3.org/2000/svg"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Saving...
              </span>
            ) : initialData ? (
              <span className="flex items-center">
                <Edit className="w-4 h-4 mr-2" />
                Update Service
              </span>
            ) : (
              <span className="flex items-center">
                <Plus className="w-4 h-4 mr-2" />
                Create Service
              </span>
            )}
          </Button>
        </div>
      </form>
    </FormProvider>
  );
}
