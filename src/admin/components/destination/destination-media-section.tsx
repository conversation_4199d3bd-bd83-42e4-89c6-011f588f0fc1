import React, { useState, useEffect, useRef } from "react";
import { Text, Label, toast } from "@camped-ai/ui";
import {
  DndContext,
  DragEndEvent,
  DragOverlay,
  DragStartEvent,
  DropAnimation,
  KeyboardSensor,
  PointerSensor,
  UniqueIdentifier,
  useSensor,
  useSensors,
  closestCenter,
} from "@dnd-kit/core";
import {
  arrayMove,
  SortableContext,
  sortableKeyboardCoordinates,
} from "@dnd-kit/sortable";
import { UseFormReturn, useFieldArray } from "react-hook-form";
import { defaultDropAnimationSideEffects } from "@dnd-kit/core";

import MediaItem, { MediaField } from "../hotel/media-item";
import MediaGridItemOverlay from "../hotel/media-grid-item-overlay";

export type DestinationFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured: boolean;
  country: string;
  currency: string;
  location: string | null;
  tags: string[] | null;
  website?: string | null;
  category_id?: string;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  faqs?: Array<{
    id?: string;
    question: string;
    answer: string;
  }>;
  id?: string;
};

const dropAnimationConfig: DropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.4",
      },
    },
  }),
};

const DestinationMediaSection = ({
  form,
}: {
  form: UseFormReturn<DestinationFormData>;
}) => {
  const { fields, append, remove } = useFieldArray({
    name: "media",
    control: form.control,
    keyName: "field_id",
  });

  const [activeId, setActiveId] = useState<UniqueIdentifier | null>(null);

  // Create a ref for the file input
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Watch for changes in the form's media field
  // This will help us detect when the form is reset
  useEffect(() => {
    // If the form's media field is empty but our fields array isn't,
    // it means the form was reset externally
    const formMedia = form.getValues("media") || [];
    if (formMedia.length === 0 && fields.length > 0) {
      // Clear our fields by removing all items
      fields.forEach((_, index) => remove(index));
    }
  }, [form, fields, remove]);

  // Clear the file input when the form is reset
  useEffect(() => {
    // If the form's media field is empty, also clear the file input
    const formMedia = form.getValues("media") || [];
    if (formMedia.length === 0 && fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  }, [form]);

  const sensors = useSensors(
    useSensor(PointerSensor, {
      activationConstraint: {
        distance: 5, // 5px movement to start dragging
      },
    }),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragStart = (event: DragStartEvent) => {
    setActiveId(event.active.id);
  };

  const handleDragEnd = async (event: DragEndEvent) => {
    const { active, over } = event;
    setActiveId(null);

    if (active.id !== over?.id) {
      const oldIndex = fields.findIndex(
        (field) => field.field_id === active.id
      );
      const newIndex = fields.findIndex((field) => field.field_id === over?.id);

      // Update local state immediately for better UX
      const reorderedFields = arrayMove(fields, oldIndex, newIndex);
      form.setValue("media", reorderedFields, {
        shouldDirty: true,
        shouldTouch: true,
      });

      // Update ranks on the server for existing images
      try {
        const updatePromises = reorderedFields.map(async (field, index) => {
          // Only update existing images (those with IDs)
          if (field.id) {
            const response = await fetch(
              `/admin/hotel-management/destinations/images/${field.id}/rank`,
              {
                method: "POST",
                credentials: "include",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ rank: index }),
              }
            );

            if (!response.ok) {
              console.error(`Failed to update rank for image ${field.id}`);
            }
          }
        });

        await Promise.all(updatePromises);

        toast.success("Success", {
          description: "Image order updated successfully",
        });
      } catch (error) {
        console.error("Error updating image order:", error);
        toast.error("Error", {
          description: "Failed to update image order",
        });
      }
    }
  };

  const handleDragCancel = () => {
    setActiveId(null);
  };

  const getOnDelete = (index: number) => {
    return async () => {
      const mediaToDelete = fields[index];

      // If the image has an ID, attempt to delete from the server
      if (mediaToDelete.id) {
        try {
          const response = await fetch(
            `/admin/hotel-management/destinations/images/${mediaToDelete.id}`,
            {
              method: "DELETE",
              credentials: "include",
            }
          );

          if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Failed to delete image: ${errorText}`);
          }

          toast.success("Success", {
            description: "Image deleted successfully",
          });
        } catch (error) {
          console.error("Error deleting image:", error);
          toast.error("Error", {
            description: "Failed to delete image",
          });
          return;
        }
      }

      // Remove the image from the form
      remove(index);
    };
  };

  const getMakeThumbnail = (index: number) => {
    return async () => {
      const selectedField = fields[index];

      // First, update the local form state to ensure only one thumbnail is selected
      const newFields = fields.map((field, i) => ({
        ...field,
        isThumbnail: i === index, // Only the selected item will be true, all others false
      }));

      form.setValue("media", newFields, {
        shouldDirty: true,
        shouldTouch: true,
      });

      // If this is an existing image (has an ID), call the API to set it as thumbnail
      if (selectedField.id) {
        try {
          // Get the destination ID from the URL or form context
          const currentUrl = window.location.pathname;
          const destinationIdMatch = currentUrl.match(/\/destinations\/([^\/]+)/);

          if (destinationIdMatch) {
            const destinationId = destinationIdMatch[1];

            const response = await fetch(
              `/admin/hotel-management/destinations/${destinationId}/thumbnail`,
              {
                method: "POST",
                credentials: "include",
                headers: {
                  "Content-Type": "application/json",
                },
                body: JSON.stringify({ image_id: selectedField.id }),
              }
            );

            if (!response.ok) {
              const errorText = await response.text();
              console.error("Failed to set thumbnail:", errorText);
              toast.error("Error", {
                description: "Failed to set thumbnail",
              });
              // Revert the local state if the API call fails
              const revertedFields = fields.map((field) => ({
                ...field,
                isThumbnail: field.isThumbnail, // Keep original thumbnail state
              }));
              form.setValue("media", revertedFields, {
                shouldDirty: true,
                shouldTouch: true,
              });
              return;
            }

            // Show success message
            toast.success("Success", {
              description: "Thumbnail updated successfully",
            });

            // Refresh the images data to reflect the server changes
            try {
              const refreshResponse = await fetch(
                `/admin/hotel-management/destinations/${destinationId}/images`,
                {
                  credentials: "include",
                }
              );

              if (refreshResponse.ok) {
                const refreshData = await refreshResponse.json();
                const refreshedMedia = refreshData.images.map((img: any) => ({
                  id: img.id,
                  url: img.url,
                  isThumbnail: img.isThumbnail,
                  field_id: img.id,
                }));

                // Update the form with refreshed data
                form.setValue("media", refreshedMedia, {
                  shouldDirty: true,
                  shouldTouch: true,
                });
              }
            } catch (refreshError) {
              console.error("Error refreshing images:", refreshError);
              // If refresh fails, the local state update above will still ensure
              // only one thumbnail is selected
            }
          }
        } catch (error) {
          console.error("Error setting thumbnail:", error);
          toast.error("Error", {
            description: "Failed to set thumbnail",
          });
          // Revert the local state if the API call fails
          const revertedFields = fields.map((field) => ({
            ...field,
            isThumbnail: field.isThumbnail, // Keep original thumbnail state
          }));
          form.setValue("media", revertedFields, {
            shouldDirty: true,
            shouldTouch: true,
          });
        }
      }
    };
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = event.target.files;
    if (files) {
      Array.from(files).forEach((file) => {
        const reader = new FileReader();
        reader.onload = (e) => {
          append({
            url: e.target?.result as string,
            file,
            isThumbnail: fields.length === 0,
            field_id: crypto.randomUUID(),
          });
        };
        reader.readAsDataURL(file);
      });
    }
  };

  return (
    <div id="media" className="flex flex-col gap-y-2">
      <div className="flex justify-between items-center">
        <Text>Destination Images</Text>
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept="image/*"
          onChange={handleMediaUpload}
          className="hidden"
          id="media-upload"
        />
        <Label
          htmlFor="media-upload"
          className="px-4 py-2 rounded cursor-pointer"
        >
          Upload Images
        </Label>
      </div>
      <DndContext
        sensors={sensors}
        collisionDetection={closestCenter}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onDragCancel={handleDragCancel}
      >
        {fields.length > 0 ? (
          <DragOverlay dropAnimation={dropAnimationConfig}>
            {activeId ? (
              <MediaGridItemOverlay
                field={
                  fields.find(
                    (field) => field.field_id === activeId
                  ) as MediaField
                }
              />
            ) : null}
          </DragOverlay>
        ) : null}
        <ul className="flex flex-col gap-y-2">
          <SortableContext items={fields.map((field) => field.field_id)}>
            {fields.map((field, index) => {
              const onDelete = getOnDelete(index);
              const onMakeThumbnail = getMakeThumbnail(index);

              return (
                <MediaItem
                  key={field.field_id}
                  field={field}
                  onDelete={onDelete}
                  onMakeThumbnail={onMakeThumbnail}
                />
              );
            })}
          </SortableContext>
        </ul>
      </DndContext>
    </div>
  );
};

export default DestinationMediaSection;
