import React, { useState, useEffect } from "react";
import {
  Heading,
  Button,
  Input,
  Textarea,
  Toaster,
  toast,
} from "@camped-ai/ui";
import CustomSelect from "./custom-select";
import { TextareaField } from "./ai-enhanced-inputs";
import { useForm } from "react-hook-form";
import RoomConfigMediaSection, {
  RoomConfigFormData,
} from "./room-config/room-config-media-section";
import { MediaField } from "./hotel/media-item";
import "../styles/room-config-modal-fix.css";

interface SimpleRoomConfigFormProps {
  hotelId: string;
  onComplete: (success: boolean) => void;
  initialData?: any;
  isEdit?: boolean;
}

const SimpleRoomConfigForm: React.FC<SimpleRoomConfigFormProps> = ({
  hotelId,
  onComplete,
  initialData,
  isEdit = false,
}) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [images, setImages] = useState<any[]>([]);

  console.log("Initial data:", initialData);

  // Use react-hook-form for better form handling
  // Calculate initial max_occupancy for the form
  const formInitialMaxAdults = initialData?.max_adults || 2;
  const formInitialMaxChildren = initialData?.max_children || 0;
  const formInitialMaxInfants = initialData?.max_infants || 0;
  const formInitialMaxOccupancy =
    formInitialMaxAdults + formInitialMaxChildren + formInitialMaxInfants;

  const form = useForm<RoomConfigFormData>({
    defaultValues: {
      id: initialData?.id || "",
      name: initialData?.name || "",
      handle: initialData?.handle || "",
      type: initialData?.type || "standard",
      description: initialData?.description || "",
      room_size: initialData?.room_size || "",
      bed_type: initialData?.bed_type || "queen",
      max_extra_beds: initialData?.max_extra_beds || 0,
      max_cots: initialData?.max_cots || 0,
      max_adults: formInitialMaxAdults,
      max_adults_beyond_capacity: initialData?.max_adults_beyond_capacity || 0,
      max_children: formInitialMaxChildren,
      max_infants: formInitialMaxInfants,
      max_occupancy: formInitialMaxOccupancy,
      amenities: initialData?.amenities || [],
      is_active: initialData?.is_active || true,
      tags: initialData?.tags || [],
      hotel_id: hotelId,
      media: [],
    },
  });

  console.log("Form initialized with ID:", initialData?.id);
  console.log("DEBUG: Form initialData:", {
    max_extra_beds: initialData?.max_extra_beds,
    max_cots: initialData?.max_cots,
    max_adults_beyond_capacity: initialData?.max_adults_beyond_capacity,
  });

  // Calculate initial max_occupancy based on adults, children, and infants
  const initialMaxAdults = initialData?.max_adults || 2;
  const initialMaxChildren = initialData?.max_children || 0;
  const initialMaxInfants = initialData?.max_infants || 0;
  const initialMaxOccupancy =
    initialMaxAdults + initialMaxChildren + initialMaxInfants;

  const [formData, setFormData] = useState({
    id: initialData?.id || "",
    name: initialData?.name || "",
    type: initialData?.type || "standard",
    description: initialData?.description || "",
    room_size: initialData?.room_size || "",
    bed_type: initialData?.bed_type || "queen",
    max_extra_beds: initialData?.max_extra_beds || 0,
    max_cots: initialData?.max_cots || 0,
    max_adults: initialMaxAdults,
    max_adults_beyond_capacity: initialData?.max_adults_beyond_capacity || 0,
    max_children: initialMaxChildren,
    max_infants: initialMaxInfants,
    max_occupancy: initialMaxOccupancy,
    amenities: initialData?.amenities || [],
    hotel_id: hotelId,
  });

  console.log("Initial form data:", formData);

  // Convert amenities array to comma-separated string for the input field
  const [amenitiesInput, setAmenitiesInput] = useState(() => {
    if (!initialData?.amenities) return "";
    if (Array.isArray(initialData.amenities))
      return initialData.amenities.join(", ");
    return "";
  });

  // Fetch existing images if in edit mode
  useEffect(() => {
    if (isEdit && initialData?.id) {
      fetchRoomConfigImages(initialData.id);
    }
  }, [isEdit, initialData]);

  // Fetch room config images
  const fetchRoomConfigImages = async (roomConfigId: string) => {
    try {
      const response = await fetch(
        `/admin/room-configs/${roomConfigId}/images`,
        {
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch room config images");
      }

      const { images } = await response.json();

      // Update the form with the fetched images
      const formattedImages = images.map((image: any) => ({
        id: image.id,
        url: image.url,
        isThumbnail: image.isThumbnail,
        field_id: image.id,
      }));

      form.setValue("media", formattedImages);
      setImages(formattedImages);
    } catch (error) {
      console.error("Error fetching room config images:", error);
    }
  };

  const roomTypes = [
    { value: "standard", label: "Standard Room" },
    { value: "deluxe", label: "Deluxe Room" },
    { value: "suite", label: "Suite" },
    { value: "executive", label: "Executive Room" },
    { value: "family", label: "Family Room" },
  ];

  const bedTypes = [
    { value: "single", label: "Single Bed" },
    { value: "twin", label: "Twin Beds" },
    { value: "double", label: "Double Bed" },
    { value: "queen", label: "Queen Bed" },
    { value: "king", label: "King Bed" },
  ];

  const calculateMaxOccupancy = (data: any) => {
    // Calculate max occupancy based on adults, children, and infants
    const adults = parseInt(data.max_adults?.toString()) || 1;
    const children = parseInt(data.max_children?.toString()) || 0;
    const infants = parseInt(data.max_infants?.toString()) || 0;

    return adults + children + infants;
  };

  const handleInputChange = (field: string, value: any) => {
    const updatedData = { ...formData, [field]: value };

    // If one of the occupancy fields is changed, recalculate max_occupancy
    if (["max_adults", "max_children", "max_infants"].includes(field)) {
      updatedData.max_occupancy = calculateMaxOccupancy(updatedData);
    }

    setFormData(updatedData);
  };

  const handleSubmit = async () => {
    setIsSubmitting(true);
    try {
      // Prepare the data for submission
      // Parse the occupancy values
      const max_adults = parseInt(formData.max_adults.toString()) || 1;
      const max_children = parseInt(formData.max_children.toString()) || 0;
      const max_infants = parseInt(formData.max_infants.toString()) || 0;
      const max_extra_beds = parseInt(formData.max_extra_beds.toString()) || 0;
      const max_cots = parseInt(formData.max_cots.toString()) || 0;

      // Calculate max_occupancy based on the parsed values
      const max_occupancy = max_adults + max_children + max_infants;

      // Process amenities input into an array
      const amenitiesArray = amenitiesInput
        ? amenitiesInput
            .split(",")
            .map((amenity: string) => amenity.trim())
            .filter((amenity: string) => amenity)
        : [];

      const newRoomConfig = {
        ...formData,
        // Set the parsed and calculated values
        max_extra_beds,
        max_cots,
        max_adults,
        max_adults_beyond_capacity: formData.max_adults_beyond_capacity,
        max_children,
        max_infants,
        max_occupancy,
        // Update amenities with the processed array
        amenities: amenitiesArray,
        // Add an ID if this is a new room config
        id: isEdit ? initialData.id : `room_config_${Date.now()}`,
        // Ensure hotel_id is included
        hotel_id: hotelId,
      };

      console.log("Room configuration data:", newRoomConfig);

      // Send the data to the API
      const endpoint = isEdit
        ? `/admin/hotel-management/room-configs/${initialData.id}`
        : "/admin/hotel-management/room-configs";
      const method = isEdit ? "PUT" : "POST";

      // Ensure hotel_id is included in the request
      const requestData = {
        ...newRoomConfig,
        hotel_id: hotelId, // Make sure hotel_id is explicitly set
      };

      // Log the hotel ID to make sure it's being sent correctly
      console.log(`Hotel ID: ${hotelId}`);
      console.log(
        `Sending ${method} request to ${endpoint} with data:`,
        requestData
      );

      const response = await fetch(endpoint, {
        method,
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(requestData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Failed to save room configuration"
        );
      }

      const data = await response.json();
      console.log("API response:", data);

      // Get the room config ID from the response
      const roomConfigId =
        data.roomConfig?.id || (isEdit ? initialData.id : null);

      if (roomConfigId) {
        // Handle image uploads if there are any new images
        const mediaFields = form.getValues("media") || [];
        const newMediaFiles = mediaFields.filter(
          (media) => media.file !== undefined
        );

        if (newMediaFiles.length > 0) {
          console.log(
            `Uploading ${newMediaFiles.length} images for room config ${roomConfigId}`
          );

          // Upload each new image
          for (const media of newMediaFiles) {
            if (media.file) {
              const formData = new FormData();
              formData.append("files", media.file);

              try {
                const uploadResponse = await fetch(
                  `/admin/hotel-management/room-configs/${roomConfigId}/upload`,
                  {
                    method: "POST",
                    credentials: "include",
                    body: formData,
                  }
                );

                if (!uploadResponse.ok) {
                  console.error(
                    "Failed to upload image",
                    await uploadResponse.text()
                  );
                } else {
                  const uploadResult = await uploadResponse.json();
                  console.log("Image upload result:", uploadResult);

                  // If this is a thumbnail image, set it as the product thumbnail
                  if (
                    media.isThumbnail &&
                    uploadResult.images &&
                    uploadResult.images.length > 0
                  ) {
                    const imageId = uploadResult.images[0].id;

                    await fetch(
                      `/admin/hotel-management/room-configs/${roomConfigId}/thumbnail`,
                      {
                        method: "POST",
                        credentials: "include",
                        headers: {
                          "Content-Type": "application/json",
                        },
                        body: JSON.stringify({ image_id: imageId }),
                      }
                    );
                  }
                }
              } catch (uploadError) {
                console.error("Error uploading image:", uploadError);
              }
            }
          }
        }
      }

      toast.success("Success", {
        description: isEdit
          ? "Room configuration updated successfully"
          : "Room configuration created successfully",
      });
      onComplete(true);
    } catch (error) {
      console.error("Error saving room configuration:", error);
      toast.error("Error", {
        description:
          error instanceof Error
            ? error.message
            : "Failed to save room configuration",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="h-full overflow-auto">
      <div className="p-6">
        <Toaster />

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Room Type Name *
            </label>
            <Input
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="e.g., Deluxe King Room"
              className="w-full h-9 text-sm"
              required
            />
          </div>

          <div>
            <TextareaField
              id="description"
              label="Description"
              value={formData.description}
              onChange={(value) => handleInputChange("description", value)}
              placeholder="Describe the room, its features, and what makes it special..."
              rows={4}
              contentType="description"
              context={{
                name: formData.name,
                type: formData.type,
                hotel_id: hotelId,
              }}
              helpText="A detailed description helps guests understand what to expect"
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Room Size
              </label>
              <Input
                value={formData.room_size}
                onChange={(e) => handleInputChange("room_size", e.target.value)}
                placeholder="e.g., 32 m²"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Bed Type
              </label>
              <CustomSelect
                value={formData.bed_type}
                onChange={(value) => handleInputChange("bed_type", value)}
                options={bedTypes.map((type) => ({
                  value: type.value,
                  label: type.label,
                }))}
                placeholder="Select a bed type"
                className="w-full h-9 text-sm"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Maximum Adults *
              </label>
              <Input
                type="number"
                min="1"
                max="10"
                value={formData.max_adults.toString()}
                onChange={(e) =>
                  handleInputChange("max_adults", parseInt(e.target.value) || 1)
                }
                className="w-full h-9 text-sm"
                required
              />
              <p className="text-xs text-gray-500 mt-1">
                Base room capacity for adults
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Maximum Children
              </label>
              <Input
                type="number"
                min="0"
                max="10"
                value={formData.max_children.toString()}
                onChange={(e) =>
                  handleInputChange(
                    "max_children",
                    parseInt(e.target.value) || 0
                  )
                }
                className="w-full h-9 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Maximum Infants
              </label>
              <Input
                type="number"
                min="0"
                max="5"
                value={formData.max_infants.toString()}
                onChange={(e) =>
                  handleInputChange(
                    "max_infants",
                    parseInt(e.target.value) || 0
                  )
                }
                className="w-full h-9 text-sm"
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Maximum Occupancy
              </label>
              <Input
                type="number"
                value={formData.max_occupancy.toString()}
                className="w-full h-9 text-sm bg-gray-50"
                readOnly
                disabled
              />
              <p className="text-xs text-gray-500 mt-1">
                Total occupancy (adults + children + infants) - automatically
                calculated
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Maximum Extra Beds
              </label>
              <Input
                type="number"
                min="0"
                max="5"
                value={formData.max_extra_beds.toString()}
                onChange={(e) =>
                  handleInputChange(
                    "max_extra_beds",
                    parseInt(e.target.value) || 0
                  )
                }
                className="w-full h-9 text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                Additional beds for extra guests
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Maximum Cots
              </label>
              <Input
                type="number"
                min="0"
                max="3"
                value={formData.max_cots.toString()}
                onChange={(e) =>
                  handleInputChange("max_cots", parseInt(e.target.value) || 0)
                }
                className="w-full h-9 text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                Baby cots for infants/toddlers
              </p>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Extra Adults Beyond Capacity
              </label>
              <Input
                type="number"
                min="0"
                max="5"
                value={formData.max_adults_beyond_capacity.toString()}
                onChange={(e) =>
                  handleInputChange(
                    "max_adults_beyond_capacity",
                    parseInt(e.target.value) || 0
                  )
                }
                className="w-full h-9 text-sm"
              />
              <p className="text-xs text-gray-500 mt-1">
                Additional adults allowed beyond base capacity (with surcharge)
              </p>
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Amenities
            </label>
            <Textarea
              value={amenitiesInput}
              onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                setAmenitiesInput(e.target.value)
              }
              placeholder="e.g., WiFi, Air Conditioning, TV, Mini Bar (comma separated)"
              className="w-full"
              rows={3}
            />
            <p className="text-xs text-gray-500 mt-1">
              List the amenities available in this room type, separated by
              commas
            </p>
          </div>

          {/* Room Images Section */}
          <div className="mt-6 border rounded-lg p-4 room-config-media-section">
            <Heading level="h3" className="text-lg font-medium mb-4">
              Room Images
            </Heading>
            <RoomConfigMediaSection form={form} />
          </div>
        </div>

        {/* Hidden button that can be triggered from the modal footer */}
        <button
          id="room-config-save-button"
          type="button"
          onClick={handleSubmit}
          disabled={isSubmitting || !formData.name}
          style={{ display: "none" }}
        >
          Save
        </button>
      </div>
    </div>
  );
};

export default SimpleRoomConfigForm;
