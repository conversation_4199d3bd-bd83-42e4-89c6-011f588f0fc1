import { MedusaService, Modules } from "@camped-ai/framework/utils";
import { MedusaError } from "@camped-ai/framework/utils";
import {
  IProductModuleService,
  MedusaContainer,
} from "@camped-ai/framework/types";
import { HOTEL_MODULE } from "../hotel";
import { DESTINATION_MODULE } from "../destination";
import {
  AddOnServiceType,
  AddOnServiceLevel,
  AddOnPricingType,
  CurrencyPrice,
  MultiCurrencyPricing,
  AddOnServiceMetadata,
  AddOnServiceInput,
  AddOnServiceResponse,
  CurrencyContext,
  LegacyPriceData,
} from "./types";
import {
  validateAddOnServiceInput,
  sanitizePricingInput,
  validateMultiCurrencyPricing,
  getValidationErrorMessages,
} from "./validation";

class AddOnServiceModuleService extends MedusaService({}) {
  protected productService_: IProductModuleService | null = null;
  protected container: MedusaContainer;

  constructor(container: MedusaContainer) {
    super(container);
    this.container = container;
    try {
      this.productService_ = container.resolve(Modules.PRODUCT);
    } catch (error) {
      console.warn("Could not resolve product service:", error.message);
    }
  }

  /**
   * Utility method to normalize pricing input to multi-currency format
   */
  private normalizePricingInput(data: AddOnServiceInput): {
    prices: MultiCurrencyPricing;
    default_currency: string;
  } {
    // If multi-currency prices are provided, use them
    if (data.prices && Object.keys(data.prices).length > 0) {
      return {
        prices: data.prices,
        default_currency: data.default_currency || Object.keys(data.prices)[0],
      };
    }

    // Convert legacy single-currency format to multi-currency
    const currency = data.currency_code || data.default_currency || "USD";
    const currencyPrice: CurrencyPrice = {};

    if (data.adult_price !== undefined) {
      currencyPrice.adult_price = data.adult_price;
    }
    if (data.child_price !== undefined) {
      currencyPrice.child_price = data.child_price;
    }
    if (data.package_price !== undefined) {
      currencyPrice.package_price = data.package_price;
    }
    if (data.per_day_adult_price !== undefined) {
      currencyPrice.per_day_adult_price = data.per_day_adult_price;
    }
    if (data.per_day_child_price !== undefined) {
      currencyPrice.per_day_child_price = data.per_day_child_price;
    }

    return {
      prices: { [currency]: currencyPrice },
      default_currency: currency,
    };
  }

  /**
   * Utility method to get prices for a specific currency with fallback
   */
  private getPricesForCurrency(
    prices: MultiCurrencyPricing,
    requestedCurrency: string,
    defaultCurrency: string
  ): CurrencyPrice {
    // Try requested currency first
    if (prices[requestedCurrency]) {
      return prices[requestedCurrency];
    }

    // Fall back to default currency
    if (prices[defaultCurrency]) {
      return prices[defaultCurrency];
    }

    // Fall back to first available currency
    const firstCurrency = Object.keys(prices)[0];
    if (firstCurrency && prices[firstCurrency]) {
      return prices[firstCurrency];
    }

    // Return empty price object if no prices found
    return {};
  }

  /**
   * Utility method to create variant prices for all currencies
   */
  private createVariantPricesArray(
    prices: MultiCurrencyPricing,
    priceType: 'adult_price' | 'child_price' | 'package_price' | 'per_day_adult_price' | 'per_day_child_price'
  ): Array<{ amount: number; currency_code: string }> {
    const variantPrices: Array<{ amount: number; currency_code: string }> = [];

    for (const [currencyCode, currencyPrice] of Object.entries(prices)) {
      const amount = currencyPrice[priceType];
      if (amount !== undefined && amount !== null) {
        variantPrices.push({
          amount,
          currency_code: currencyCode,
        });
      }
    }

    return variantPrices;
  }

  get productService(): IProductModuleService {
    if (!this.productService_) {
      try {
        // Try to resolve the product service
        this.productService_ = this.container.resolve(Modules.PRODUCT);
      } catch (error) {
        console.warn("Error resolving product service:", error.message);
        // Return a mock service as fallback
        return {
          createProducts: async () => ({ id: "mock-product-id" }),
          createProductVariants: async () => ({ id: "mock-variant-id" }),
          retrieveProduct: async () => ({
            id: "mock-product-id",
            title: "Mock Product",
            description: "",
            metadata: { add_on_service: true },
            variants: [
              {
                id: "mock-adult-variant",
                metadata: { price_type: "adult" },
                prices: [{ amount: 1000, currency_code: "USD" }],
              },
              {
                id: "mock-child-variant",
                metadata: { price_type: "child" },
                prices: [{ amount: 500, currency_code: "USD" }],
              },
            ],
          }),
          listAndCountProducts: async () => [[], 0],
          listAndCountProductVariants: async () => [[], 0],
          update: async (id, data) => {
            console.log(`Mock update for product ${id}:`, data);
            return { id, ...data };
          },
          updateProduct: async (id, data) => {
            console.log(`Mock updateProduct for ${id}:`, data);
            return { id, ...data };
          },
          updateProductVariant: async (id, data) => {
            console.log(`Mock updateProductVariant for ${id}:`, data);
            return { id, ...data };
          },
          updateVariant: async (id, data) => {
            console.log(`Mock updateVariant for ${id}:`, data);
            return { id, ...data };
          },
          updateProducts: async (products) => {
            console.log(`Mock updateProducts:`, products);
            return products;
          },
          deleteProduct: async () => ({}),
          deleteProducts: async () => ({}),
        } as unknown as IProductModuleService;
      }
    }
    return this.productService_;
  }

  /**
   * Create a new add-on service
   * @param data - The add-on service data
   */
  async createAddOnService(data: AddOnServiceInput): Promise<AddOnServiceResponse> {
    try {
      // Validate and sanitize input
      const query = this.container?.resolve("query");
      const validation = await validateAddOnServiceInput(data, query);
      if (!validation.is_valid) {
        const errorMessages = getValidationErrorMessages(validation);
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          `Validation failed: ${errorMessages.join(", ")}`
        );
      }

      // Log warnings if any
      if (validation.warnings.length > 0) {
        console.warn("Add-on service validation warnings:", validation.warnings);
      }

      // Validate required fields
      if (!data.name) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Name is required for add-on services"
        );
      }

      if (!data.service_level) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Service level is required for add-on services"
        );
      }

      if (data.service_level === AddOnServiceLevel.HOTEL && !data.hotel_id) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Hotel ID is required for hotel-level add-on services"
        );
      }

      // Validate hotel_id format
      if (
        data.service_level === AddOnServiceLevel.HOTEL &&
        Array.isArray(data.hotel_id) &&
        data.hotel_id.length === 0
      ) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "At least one hotel ID is required for hotel-level add-on services"
        );
      }

      if (
        data.service_level === AddOnServiceLevel.DESTINATION &&
        !data.destination_id
      ) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          "Destination ID is required for destination-level add-on services"
        );
      }

      // Generate a unique handle for the product if not provided
      let productHandle = data.handle;

      if (!productHandle) {
        const timestamp = new Date().getTime();
        const randomString = Math.random().toString(36).substring(2, 8);
        // Sanitize the name to create a URL-friendly base
        const sanitizedName = data.name
          ? data.name
              .toLowerCase()
              .replace(/[^a-z0-9]/g, "-")
              .replace(/-+/g, "-")
              .replace(/^-|-$/g, "")
          : "unnamed-service";
        productHandle = `${sanitizedName}-${data.service_level}-${timestamp}-${randomString}`;

        console.log(
          `Creating add-on service with generated handle: ${productHandle}`
        );
      } else {
        console.log(
          `Creating add-on service with provided handle: ${productHandle}`
        );
      }

      // Normalize pricing input to multi-currency format
      const { prices, default_currency } = this.normalizePricingInput(data);

      // Prepare metadata with additional information
      console.log("Creating add-on service with data:", data);
      console.log("Pricing type from data:", data.pricing_type);
      console.log("Normalized pricing:", { prices, default_currency });

      const metadata: AddOnServiceMetadata = {
        add_on_service: true,
        service_type: data.type || "general",
        service_level: data.service_level,
        max_capacity: data.max_capacity === null ? 999999 : data.max_capacity,
        is_active: data.is_active !== false,
        start_date: data.start_date,
        end_date: data.end_date,
        images: data.images || [],
        pricing_type: data.pricing_type || AddOnPricingType.PER_PERSON,
        category_id: data.category_id || null,

        // Multi-currency pricing
        prices,
        default_currency,

        // Legacy fields for backward compatibility
        currency_code: data.currency_code || default_currency,
      };

      console.log("Metadata pricing type set to:", metadata.pricing_type);

      // Add hotel information
      if (data.service_level === AddOnServiceLevel.HOTEL && data.hotel_id) {
        metadata.hotel_id = data.hotel_id;

        // Store hotel name if provided
        if (data.hotel_name) {
          metadata.hotel_name = data.hotel_name;
        } else {
          // Try to fetch hotel name
          try {
            if (
              this.container &&
              typeof this.container.resolve === "function"
            ) {
              const hotelService = this.container.resolve(HOTEL_MODULE);
              if (
                hotelService &&
                typeof hotelService.retrieveHotel === "function"
              ) {
                // Handle both string and array hotel_id
                if (Array.isArray(data.hotel_id)) {
                  // If it's an array, use the first hotel ID to get a name
                  if (data.hotel_id.length > 0) {
                    const hotel = await hotelService.retrieveHotel(
                      data.hotel_id[0]
                    );
                    if (hotel && hotel.name) {
                      // If there are multiple hotels, indicate that in the name
                      const hotelName =
                        data.hotel_id.length > 1
                          ? `${hotel.name} and ${data.hotel_id.length - 1} more`
                          : hotel.name;
                      metadata.hotel_name = hotelName;
                      console.log(
                        `Stored hotel name in metadata: ${hotelName}`
                      );
                    }
                  }
                } else {
                  // Single hotel ID
                  const hotel = await hotelService.retrieveHotel(data.hotel_id);
                  if (hotel && hotel.name) {
                    metadata.hotel_name = hotel.name;
                    console.log(`Stored hotel name in metadata: ${hotel.name}`);
                  }
                }
              }
            }
          } catch (error) {
            console.warn(
              "Could not fetch hotel name for metadata:",
              error.message
            );
          }
        }
      }

      // Add destination information
      if (
        data.service_level === AddOnServiceLevel.DESTINATION &&
        data.destination_id
      ) {
        // Store the destination_id in metadata
        // Keep it as an array if it's an array
        metadata.destination_id = data.destination_id;
        console.log(`Storing destination_id in metadata:`, data.destination_id);

        // Store destination name if provided
        if (data.destination_name) {
          metadata.destination_name = data.destination_name;
        } else {
          // Try to fetch destination name(s)
          try {
            if (
              this.container &&
              typeof this.container.resolve === "function"
            ) {
              const destinationService =
                this.container.resolve(DESTINATION_MODULE);

              if (Array.isArray(data.destination_id)) {
                // For array of destination IDs
                if (
                  destinationService &&
                  typeof destinationService.list === "function"
                ) {
                  const destinationList = await destinationService.list({});
                  const destinations = Array.isArray(destinationList)
                    ? destinationList
                    : destinationList?.data || [];

                  if (destinations.length > 0) {
                    const destinationNames = data.destination_id.map(
                      (id: string) => {
                        const destination = destinations.find(
                          (d: any) => d.id === id
                        );
                        return destination?.name || id;
                      }
                    );

                    metadata.destination_name = destinationNames.join(", ");
                    console.log(
                      `Stored destination names in metadata: ${metadata.destination_name}`
                    );
                  }
                }
              } else {
                // For single destination ID
                if (
                  destinationService &&
                  typeof destinationService.retrieve === "function"
                ) {
                  try {
                    const destination = await destinationService.retrieve(
                      data.destination_id
                    );
                    if (destination && destination.name) {
                      metadata.destination_name = destination.name;
                      console.log(
                        `Stored destination name in metadata: ${destination.name}`
                      );
                    }
                  } catch (error) {
                    console.warn(
                      "Could not fetch destination name for metadata:",
                      error.message
                    );
                  }
                }
              }
            }
          } catch (error) {
            console.warn(
              "Error fetching destination data for metadata:",
              error.message
            );
          }
        }
      }

      // Store legacy single-currency prices for backward compatibility
      const defaultCurrencyPrices = prices[default_currency] || {};

      if (defaultCurrencyPrices.adult_price !== undefined) {
        metadata.adult_price = defaultCurrencyPrices.adult_price;
        metadata.adult_price_formatted = `${(defaultCurrencyPrices.adult_price / 100).toFixed(2)} ${default_currency}`;
        console.log(`Stored legacy adult price in metadata: ${metadata.adult_price_formatted}`);
      }

      if (defaultCurrencyPrices.child_price !== undefined) {
        metadata.child_price = defaultCurrencyPrices.child_price;
        metadata.child_price_formatted = `${(defaultCurrencyPrices.child_price / 100).toFixed(2)} ${default_currency}`;
        console.log(`Stored legacy child price in metadata: ${metadata.child_price_formatted}`);
      }

      if (defaultCurrencyPrices.package_price !== undefined) {
        metadata.package_price = defaultCurrencyPrices.package_price;
        metadata.package_price_formatted = `${(defaultCurrencyPrices.package_price / 100).toFixed(2)} ${default_currency}`;
        console.log(`Stored legacy package price in metadata: ${metadata.package_price_formatted}`);
      }

      if (defaultCurrencyPrices.per_day_adult_price !== undefined) {
        metadata.per_day_adult_price = defaultCurrencyPrices.per_day_adult_price;
        metadata.per_day_adult_price_formatted = `${(defaultCurrencyPrices.per_day_adult_price / 100).toFixed(2)} ${default_currency} per day`;
        console.log(`Stored legacy per-day adult price in metadata: ${metadata.per_day_adult_price_formatted}`);
      }

      if (defaultCurrencyPrices.per_day_child_price !== undefined) {
        metadata.per_day_child_price = defaultCurrencyPrices.per_day_child_price;
        metadata.per_day_child_price_formatted = `${(defaultCurrencyPrices.per_day_child_price / 100).toFixed(2)} ${default_currency} per day`;
        console.log(`Stored legacy per-day child price in metadata: ${metadata.per_day_child_price_formatted}`);
      }

      // Create a product for this add-on service
      console.log("Final metadata before product creation:", metadata);

      const productData = {
        title: data.name,
        description: data.description || "",
        handle: productHandle, // Set a unique handle to avoid conflicts
        status: "published",
        metadata,
      };

      console.log("Product data being sent:", productData);

      // Create the product
      // Use any type assertion to bypass TypeScript errors
      const productServiceAny = this.productService as any;
      const product = await productServiceAny.createProducts(productData);

      console.log("Created product:", product);
      console.log("Created product metadata:", product.metadata);

      // Create variants for adult and child pricing
      const variants = [];

      // Log the product ID for debugging
      console.log(`Creating variants for product ID: ${product.id}`);

      // Prepare variant data based on pricing type with multi-currency support
      const variantsToCreate = [];

      if (metadata.pricing_type === AddOnPricingType.PACKAGE) {
        // Package pricing - create single variant with multi-currency prices
        const packagePrices = this.createVariantPricesArray(prices, 'package_price');
        if (packagePrices.length > 0) {
          variantsToCreate.push({
            title: `${data.name} - Package`,
            product_id: product.id,
            options: [],
            prices: packagePrices,
            metadata: {
              add_on_service: true,
              price_type: "package",
              pricing_type: "package",
            },
            inventory_quantity:
              data.max_capacity === null ? 999999 : data.max_capacity,
            manage_inventory: data.max_capacity !== null,
          });
        }
      } else if (metadata.pricing_type === AddOnPricingType.USAGE_BASED) {
        // Usage-based pricing - create adult and child variants with per-day pricing
        // Adult variant
        const adultPrices = this.createVariantPricesArray(prices, 'per_day_adult_price');
        if (adultPrices.length > 0) {
          variantsToCreate.push({
            title: `${data.name} - Adult (Per Day)`,
            product_id: product.id,
            options: [],
            prices: adultPrices,
            metadata: {
              add_on_service: true,
              price_type: "adult",
              pricing_type: "usage_based",
            },
            inventory_quantity:
              data.max_capacity === null ? 999999 : data.max_capacity,
            manage_inventory: data.max_capacity !== null,
          });
        }

        // Child variant
        const childPrices = this.createVariantPricesArray(prices, 'per_day_child_price');
        if (childPrices.length > 0) {
          variantsToCreate.push({
            title: `${data.name} - Child (Per Day)`,
            product_id: product.id,
            options: [],
            prices: childPrices,
            metadata: {
              add_on_service: true,
              price_type: "child",
              pricing_type: "usage_based",
            },
            inventory_quantity:
              data.max_capacity === null ? 999999 : data.max_capacity,
            manage_inventory: data.max_capacity !== null,
          });
        }
      } else {
        // Per-person pricing - create adult and child variants with multi-currency support
        // Adult variant
        const adultPrices = this.createVariantPricesArray(prices, 'adult_price');
        if (adultPrices.length > 0) {
          variantsToCreate.push({
            title: `${data.name} - Adult`,
            product_id: product.id,
            options: [],
            prices: adultPrices,
            metadata: {
              add_on_service: true,
              price_type: "adult",
              pricing_type: "per_person",
            },
            inventory_quantity:
              data.max_capacity === null ? 999999 : data.max_capacity,
            manage_inventory: data.max_capacity !== null,
          });
        }

        // Child variant
        const childPrices = this.createVariantPricesArray(prices, 'child_price');
        if (childPrices.length > 0) {
          variantsToCreate.push({
            title: `${data.name} - Child`,
            product_id: product.id,
            options: [],
            prices: childPrices,
            metadata: {
              add_on_service: true,
              price_type: "child",
              pricing_type: "per_person",
            },
            inventory_quantity:
              data.max_capacity === null ? 999999 : data.max_capacity,
            manage_inventory: data.max_capacity !== null,
          });
        }
      }

      // Create all variants at once if there are any to create
      if (variantsToCreate.length > 0) {
        try {
          console.log(
            `Creating ${variantsToCreate.length} variants for product ${product.id}`
          );
          const createdVariants =
            await this.productService.createProductVariants(variantsToCreate);
          variants.push(...createdVariants);
        } catch (error) {
          console.error(`Error creating variants: ${error.message}`);
          throw new MedusaError(
            MedusaError.Types.DB_ERROR,
            `Failed to create variants: ${error.message}`
          );
        }
      }

      // Return the created service
      return this.retrieveAddOnService(product.id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create add-on service: ${error.message}`
      );
    }
  }

  /**
   * List add-on services with filtering and pagination
   * @param selector - Filter criteria
   * @param config - Pagination and sorting options
   * @param options - Additional options including currency_code
   */
  async listAddOnServices(
    selector: any = {},
    config: any = { skip: 0, take: 20 },
    options: any = {}
  ): Promise<[AddOnServiceResponse[], number]> {
    try {
      // Get all add-on service products first (metadata filtering might not work properly)
      let products: any[] = [];
      let count = 0;

      try {
        // Get all products that are add-on services
        const response: any = await this.productService.listAndCountProducts(
          {
            metadata: {
              add_on_service: true,
            },
          },
          {
            ...config,
            relations: ["variants"],
            skip: 0, // Get all, we'll filter manually
            take: 1000, // Large number to get all add-ons
          }
        );

        // Process the response
        if (Array.isArray(response)) {
          products = response[0] || [];
          count = response[1] || 0;
        } else if (response && typeof response === "object") {
          products = response.data || [];
          count = response.count || 0;
        }

        console.log(
          `Found ${products.length} total add-on products before filtering`
        );

        // Now filter manually to handle array hotel_id and destination_id values
        products = products.filter((product: any) => {
          const metadata = product.metadata || {};

          // Filter by service level
          if (
            selector.service_level &&
            metadata.service_level !== selector.service_level
          ) {
            return false;
          }

          // Filter by hotel_id (handle both array and string)
          if (selector.hotel_id) {
            const hotelId = metadata.hotel_id;
            if (Array.isArray(hotelId)) {
              if (!hotelId.includes(selector.hotel_id)) {
                return false;
              }
            } else if (hotelId !== selector.hotel_id) {
              return false;
            }
          }

          // Filter by destination_id (handle both array and string)
          if (selector.destination_id) {
            const destinationId = metadata.destination_id;
            if (Array.isArray(destinationId)) {
              if (!destinationId.includes(selector.destination_id)) {
                return false;
              }
            } else if (destinationId !== selector.destination_id) {
              return false;
            }
          }

          // Filter by service type
          if (selector.type && metadata.service_type !== selector.type) {
            return false;
          }

          // Filter by active status
          if (
            selector.is_active !== undefined &&
            metadata.is_active !== selector.is_active
          ) {
            return false;
          }

          return true;
        });

        console.log(
          `Filtered to ${products.length} add-on products after manual filtering`
        );

        // Apply pagination to filtered results
        const startIndex = config.skip || 0;
        const endIndex = startIndex + (config.take || 20);
        const paginatedProducts = products.slice(startIndex, endIndex);

        products = paginatedProducts;
        count = products.length; // Update count to filtered count
      } catch (error) {
        console.error("Failed to fetch products:", error);
        return [[], 0];
      }

      // Fetch hotel and destination data to get names
      let hotels: any[] = [];
      let destinations: any[] = [];

      try {
        // Try to fetch hotels
        if (this.container && typeof this.container.resolve === "function") {
          try {
            const hotelService = this.container.resolve(HOTEL_MODULE);
            if (hotelService && typeof hotelService.listHotels === "function") {
              const hotelList = await hotelService.listHotels({});
              hotels = Array.isArray(hotelList)
                ? hotelList
                : hotelList?.data || [];
              console.log(
                `Fetched ${hotels.length} hotels for add-on service enrichment`
              );
            }
          } catch (error) {
            console.warn("Could not fetch hotels:", error.message);
          }

          // Try to fetch destinations
          try {
            const destinationService = this.container.resolve("destination");

            // Check if we have destination IDs in the options
            if (
              options &&
              options.destination_ids &&
              options.destination_ids.length > 0
            ) {
              console.log(
                `Using destination IDs from options: ${options.destination_ids.join(
                  ", "
                )}`
              );

              // Try to fetch destinations by IDs
              if (
                destinationService &&
                typeof destinationService.listDestinations === "function"
              ) {
                // First try to get all destinations
                const destinationList =
                  await destinationService.listDestinations({});
                destinations = Array.isArray(destinationList)
                  ? destinationList
                  : destinationList?.data || [];

                console.log(
                  `Fetched ${destinations.length} destinations for add-on service enrichment`
                );

                // Filter destinations to only include those in the options.destination_ids
                if (
                  destinations.length > 0 &&
                  options.destination_ids.length > 0
                ) {
                  const filteredDestinations = destinations.filter((d: any) =>
                    options.destination_ids.includes(d.id)
                  );

                  if (filteredDestinations.length > 0) {
                    console.log(
                      `Filtered to ${filteredDestinations.length} destinations that match the requested IDs`
                    );
                    destinations = filteredDestinations;
                  }
                }

                // Log destination IDs and names for debugging
                if (destinations.length > 0) {
                  console.log(
                    "Available destinations after filtering:",
                    destinations.map((d) => ({ id: d.id, name: d.name }))
                  );
                }
              }
            } else {
              // No destination IDs in options, fetch all destinations
              if (
                destinationService &&
                typeof destinationService.list === "function"
              ) {
                const destinationList = await destinationService.list({});
                destinations = Array.isArray(destinationList)
                  ? destinationList
                  : destinationList?.data || [];
                console.log(
                  `Fetched ${destinations.length} destinations for add-on service enrichment`
                );

                // Log destination IDs and names for debugging
                if (destinations.length > 0) {
                  console.log(
                    "Available destinations:",
                    destinations.map((d) => ({ id: d.id, name: d.name }))
                  );
                }
              }
            }
          } catch (error) {
            console.warn("Could not fetch destinations:", error.message);
          }
        }
      } catch (error) {
        console.warn("Error fetching related entities:", error.message);
      }
      console.log({ products });

      // Transform products into add-on services
      const addOnServices = products.map((product: any) => {
        // Ensure product has metadata
        const metadata = product.metadata || {};

        // Get pricing type (default to per_person for backward compatibility)
        const pricing_type = metadata.pricing_type || AddOnPricingType.PER_PERSON;

        // Handle multi-currency pricing
        const prices = metadata.prices || {};
        const default_currency = metadata.default_currency || metadata.currency_code || "USD";
        const requested_currency = options.currency_code || selector.currency_code || default_currency;

        // Get prices for the requested currency with fallback
        const currencyPrices = this.getPricesForCurrency(prices, requested_currency, default_currency);

        // Extract pricing information for the requested currency
        let adult_price = currencyPrices.adult_price || null;
        let child_price = currencyPrices.child_price || null;
        let package_price = currencyPrices.package_price || null;
        let per_day_adult_price = currencyPrices.per_day_adult_price || null;
        let per_day_child_price = currencyPrices.per_day_child_price || null;
        let currency_code = requested_currency;

        // Fallback to legacy single-currency metadata if multi-currency prices not available
        if (!adult_price && metadata.adult_price) adult_price = metadata.adult_price;
        if (!child_price && metadata.child_price) child_price = metadata.child_price;
        if (!package_price && metadata.package_price) package_price = metadata.package_price;
        if (!per_day_adult_price && metadata.per_day_adult_price) per_day_adult_price = metadata.per_day_adult_price;
        if (!per_day_child_price && metadata.per_day_child_price) per_day_child_price = metadata.per_day_child_price;
        if (!currency_code || currency_code === requested_currency) currency_code = metadata.currency_code || "USD";

        // Find variants for fallback pricing (if needed)
        const adultVariant = product.variants?.find(
          (v: any) => v?.metadata?.price_type === "adult"
        );
        const childVariant = product.variants?.find(
          (v: any) => v?.metadata?.price_type === "child"
        );
        const packageVariant = product.variants?.find(
          (v: any) => v?.metadata?.price_type === "package"
        );

        // If prices not in metadata, try to get from variants
        if (pricing_type === "package") {
          // Handle package pricing
          if (
            !package_price &&
            packageVariant &&
            (packageVariant as any).prices &&
            (packageVariant as any).prices.length > 0
          ) {
            package_price = (packageVariant as any).prices[0].amount || null;
            currency_code =
              (packageVariant as any).prices[0].currency_code || currency_code;
            console.log(
              `Found package price from variant: ${package_price} ${currency_code}`
            );
          } else if (package_price) {
            console.log(
              `Found package price from metadata: ${package_price} ${currency_code}`
            );
          }
        } else if (pricing_type === "usage_based") {
          // Handle usage-based pricing
          if (
            !per_day_adult_price &&
            adultVariant &&
            (adultVariant as any).prices &&
            (adultVariant as any).prices.length > 0
          ) {
            per_day_adult_price =
              (adultVariant as any).prices[0].amount || null;
            currency_code =
              (adultVariant as any).prices[0].currency_code || currency_code;
            console.log(
              `Found per-day adult price from variant: ${per_day_adult_price} ${currency_code}`
            );
          } else if (per_day_adult_price) {
            console.log(
              `Found per-day adult price from metadata: ${per_day_adult_price} ${currency_code}`
            );
          }

          if (
            !per_day_child_price &&
            childVariant &&
            (childVariant as any).prices &&
            (childVariant as any).prices.length > 0
          ) {
            per_day_child_price =
              (childVariant as any).prices[0].amount || null;
            if (!currency_code || currency_code === "USD") {
              currency_code =
                (childVariant as any).prices[0].currency_code || currency_code;
            }
            console.log(
              `Found per-day child price from variant: ${per_day_child_price} ${currency_code}`
            );
          } else if (per_day_child_price) {
            console.log(
              `Found per-day child price from metadata: ${per_day_child_price} ${currency_code}`
            );
          }
        } else {
          // Handle per-person pricing
          if (
            !adult_price &&
            adultVariant &&
            (adultVariant as any).prices &&
            (adultVariant as any).prices.length > 0
          ) {
            adult_price = (adultVariant as any).prices[0].amount || null;
            currency_code =
              (adultVariant as any).prices[0].currency_code || currency_code;
            console.log(
              `Found adult price from variant: ${adult_price} ${currency_code}`
            );
          } else if (adult_price) {
            console.log(
              `Found adult price from metadata: ${adult_price} ${currency_code}`
            );
          }

          if (
            !child_price &&
            childVariant &&
            (childVariant as any).prices &&
            (childVariant as any).prices.length > 0
          ) {
            child_price = (childVariant as any).prices[0].amount || null;
            if (!currency_code || currency_code === "USD") {
              currency_code =
                (childVariant as any).prices[0].currency_code || currency_code;
            }
            console.log(
              `Found child price from variant: ${child_price} ${currency_code}`
            );
          } else if (child_price) {
            console.log(
              `Found child price from metadata: ${child_price} ${currency_code}`
            );
          }
        }

        // Find hotel name if hotel_id exists
        let hotel_name = "";
        if (metadata.hotel_id) {
          const hotel = hotels.find((h: any) => h.id === metadata.hotel_id);
          hotel_name = hotel ? hotel.name : metadata.hotel_id;
        }

        // Find destination name if destination_id exists
        let destination_name = "";
        if (metadata.destination_id) {
          // Log the destination ID for debugging
          console.log(`Processing destination_id:`, metadata.destination_id);
          console.log(
            `Available destinations:`,
            destinations.map((d) => ({ id: d.id, name: d.name }))
          );

          if (Array.isArray(metadata.destination_id)) {
            // For array of destination IDs
            if (destinations && destinations.length > 0) {
              // If we have destinations data, use it to map IDs to names
              destination_name = metadata.destination_id
                .map((id: string) => {
                  const destination = destinations.find(
                    (d: any) => d.id === id
                  );
                  const result = destination ? destination.name : id;
                  console.log(
                    `Mapping destination ID ${id} to name: ${result}`
                  );
                  return result;
                })
                .join(", ");
            } else {
              // If no destinations data, just join the IDs
              destination_name = metadata.destination_id.join(", ");
              console.log(
                `No destination data available, using IDs: ${destination_name}`
              );
            }
          } else {
            // For single destination ID
            if (destinations && destinations.length > 0) {
              // If we have destinations data, find the matching one
              const destination = destinations.find(
                (d: any) => d.id === metadata.destination_id
              );
              destination_name = destination
                ? destination.name
                : metadata.destination_id;
              console.log(
                `Resolved single destination ID ${metadata.destination_id} to name: ${destination_name}`
              );
            } else {
              // If no destinations data, use the ID
              destination_name = metadata.destination_id;
              console.log(
                `No destination data available, using ID: ${destination_name}`
              );
            }
          }
          console.log(
            `Final destination name for ${metadata.destination_id}:`,
            destination_name
          );
        }

        // Store the resolved destination name in metadata for future use
        if (destination_name && metadata.destination_id) {
          metadata.destination_name = destination_name;
          console.log(
            `Storing resolved destination name in metadata: ${destination_name}`
          );

          // Schedule an update to the product metadata after returning the results
          // This avoids awaiting here which would cause TypeScript errors
          setTimeout(async () => {
            try {
              const productServiceForUpdate = this.productService as any;
              if (typeof productServiceForUpdate.update === "function") {
                await productServiceForUpdate.update(product.id, {
                  metadata: { ...metadata },
                });
                console.log(
                  `Updated product ${product.id} metadata with destination name`
                );
              }
            } catch (error) {
              console.warn(
                "Could not update product metadata with destination name:",
                error.message
              );
            }
          }, 0);
        }

        return {
          id: product.id,
          name: product.title || "Unnamed Service",
          description: product.description || "",
          type: metadata.service_type || "general",
          service_level: metadata.service_level || AddOnServiceLevel.HOTEL,
          hotel_id: metadata.hotel_id || null,
          hotel_name: metadata.hotel_name || hotel_name,
          destination_id: metadata.destination_id || null,
          destination_name: metadata.destination_name || destination_name,
          category_id: metadata.category_id || null,
          product_id: product.id,
          is_active: metadata.is_active !== false,
          start_date: metadata.start_date || null,
          end_date: metadata.end_date || null,
          max_capacity: metadata.max_capacity || null,
          current_capacity: metadata.current_capacity || 0,

          // Pricing information for requested currency
          pricing_type: pricing_type as AddOnPricingType,
          adult_price,
          child_price,
          package_price,
          per_day_adult_price,
          per_day_child_price,
          currency_code,

          // Multi-currency pricing data
          prices: prices,
          default_currency: default_currency,

          images: metadata.images || [],
          created_at: product.created_at,
          updated_at: product.updated_at,
          metadata: metadata, // Include the full metadata in the response
        };
      });

      return [addOnServices, count];
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list add-on services: ${error.message}`
      );
    }
  }

  /**
   * Get an add-on service by ID
   * @param id - The product ID of the add-on service
   * @param currencyCode - Optional currency code to get prices in specific currency
   */
  async retrieveAddOnService(id: string, currencyCode?: string): Promise<AddOnServiceResponse> {
    try {
      // Get the product
      const product = await this.productService.retrieveProduct(id, {
        relations: ["variants"],
      });

      if (!product.metadata?.add_on_service) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product ${id} is not an add-on service`
        );
      }

      // Find adult, child, and package variants
      const adultVariant = product.variants?.find(
        (v: any) => v?.metadata?.price_type === "adult"
      );
      const childVariant = product.variants?.find(
        (v: any) => v?.metadata?.price_type === "child"
      );
      const packageVariant = product.variants?.find(
        (v: any) => v?.metadata?.price_type === "package"
      );

      // Ensure product has metadata
      const metadata = product.metadata || {};

      // Get pricing type (default to per_person for backward compatibility)
      const pricing_type = metadata.pricing_type || AddOnPricingType.PER_PERSON;

      // Handle multi-currency pricing
      const prices = metadata.prices || {};
      const default_currency = metadata.default_currency || metadata.currency_code || "USD";
      const requested_currency = currencyCode || default_currency;

      // Get prices for the requested currency with fallback
      const currencyPrices = this.getPricesForCurrency(prices, requested_currency, default_currency);

      // Extract pricing information for the requested currency
      let adult_price = currencyPrices.adult_price || null;
      let child_price = currencyPrices.child_price || null;
      let package_price = currencyPrices.package_price || null;
      let per_day_adult_price = currencyPrices.per_day_adult_price || null;
      let per_day_child_price = currencyPrices.per_day_child_price || null;
      let currency_code = requested_currency;

      // Fallback to legacy single-currency metadata if multi-currency prices not available
      if (!adult_price && metadata.adult_price) adult_price = metadata.adult_price;
      if (!child_price && metadata.child_price) child_price = metadata.child_price;
      if (!package_price && metadata.package_price) package_price = metadata.package_price;
      if (!per_day_adult_price && metadata.per_day_adult_price) per_day_adult_price = metadata.per_day_adult_price;
      if (!per_day_child_price && metadata.per_day_child_price) per_day_child_price = metadata.per_day_child_price;
      if (!currency_code || currency_code === requested_currency) currency_code = metadata.currency_code || "USD";

      // If prices not in metadata, try to get from variants based on pricing type
      if (pricing_type === "package") {
        // Handle package pricing
        if (
          !package_price &&
          packageVariant &&
          (packageVariant as any).prices &&
          (packageVariant as any).prices.length > 0
        ) {
          package_price = (packageVariant as any).prices[0].amount || null;
          currency_code =
            (packageVariant as any).prices[0].currency_code || currency_code;
          console.log(
            `Found package price from variant: ${package_price} ${currency_code}`
          );
        } else if (package_price) {
          console.log(
            `Found package price from metadata: ${package_price} ${currency_code}`
          );
        }
      } else if (pricing_type === "usage_based") {
        // Handle usage-based pricing
        if (
          !per_day_adult_price &&
          adultVariant &&
          (adultVariant as any).prices &&
          (adultVariant as any).prices.length > 0
        ) {
          per_day_adult_price = (adultVariant as any).prices[0].amount || null;
          currency_code =
            (adultVariant as any).prices[0].currency_code || currency_code;
          console.log(
            `Found per-day adult price from variant: ${per_day_adult_price} ${currency_code}`
          );
        } else if (per_day_adult_price) {
          console.log(
            `Found per-day adult price from metadata: ${per_day_adult_price} ${currency_code}`
          );
        }

        if (
          !per_day_child_price &&
          childVariant &&
          (childVariant as any).prices &&
          (childVariant as any).prices.length > 0
        ) {
          per_day_child_price = (childVariant as any).prices[0].amount || null;
          if (!currency_code || currency_code === "USD") {
            currency_code =
              (childVariant as any).prices[0].currency_code || currency_code;
          }
          console.log(
            `Found per-day child price from variant: ${per_day_child_price} ${currency_code}`
          );
        } else if (per_day_child_price) {
          console.log(
            `Found per-day child price from metadata: ${per_day_child_price} ${currency_code}`
          );
        }
      } else {
        // Handle per-person pricing
        if (
          !adult_price &&
          adultVariant &&
          (adultVariant as any).prices &&
          (adultVariant as any).prices.length > 0
        ) {
          adult_price = (adultVariant as any).prices[0].amount || null;
          currency_code =
            (adultVariant as any).prices[0].currency_code || currency_code;
          console.log(
            `Found adult price from variant: ${adult_price} ${currency_code}`
          );
        } else if (adult_price) {
          console.log(
            `Found adult price from metadata: ${adult_price} ${currency_code}`
          );
        }

        if (
          !child_price &&
          childVariant &&
          (childVariant as any).prices &&
          (childVariant as any).prices.length > 0
        ) {
          child_price = (childVariant as any).prices[0].amount || null;
          if (!currency_code || currency_code === "USD") {
            currency_code =
              (childVariant as any).prices[0].currency_code || currency_code;
          }
          console.log(
            `Found child price from variant: ${child_price} ${currency_code}`
          );
        } else if (child_price) {
          console.log(
            `Found child price from metadata: ${child_price} ${currency_code}`
          );
        }
      }

      // Fetch hotel and destination data to get names
      let hotel_name = "";
      let destination_name = "";

      try {
        // Try to fetch hotel name if hotel_id exists
        if (
          metadata.hotel_id &&
          this.container &&
          typeof this.container.resolve === "function"
        ) {
          try {
            const hotelService = this.container.resolve("hotelService");
            if (hotelService && hotelService.retrieve) {
              // Handle both string and array hotel_id
              if (Array.isArray(metadata.hotel_id)) {
                // If it's an array, use the first hotel ID to get a name
                if (metadata.hotel_id.length > 0) {
                  const hotel = await hotelService.retrieve(
                    metadata.hotel_id[0]
                  );
                  // If there are multiple hotels, indicate that in the name
                  if (metadata.hotel_id.length > 1) {
                    hotel_name = hotel?.name
                      ? `${hotel.name} and ${metadata.hotel_id.length - 1} more`
                      : metadata.hotel_id[0];
                  } else {
                    hotel_name = hotel?.name || metadata.hotel_id[0];
                  }
                }
              } else {
                // Single hotel ID
                const hotel = await hotelService.retrieve(metadata.hotel_id);
                hotel_name = hotel?.name || metadata.hotel_id;
              }
            }
          } catch (error) {
            console.warn("Could not fetch hotel:", error.message);
          }
        }

        // Try to fetch destination name if destination_id exists
        if (
          metadata.destination_id &&
          this.container &&
          typeof this.container.resolve === "function"
        ) {
          try {
            const destinationService =
              this.container.resolve("destinationService");
            if (destinationService) {
              if (Array.isArray(metadata.destination_id)) {
                // Handle array of destination IDs
                try {
                  // First try to get all destinations at once
                  const allDestinations = await destinationService.list({});
                  const destinations = Array.isArray(allDestinations)
                    ? allDestinations
                    : allDestinations?.data || [];

                  // Map IDs to names
                  const destinationNames = metadata.destination_id.map(
                    (id: string) => {
                      const destination = destinations.find(
                        (d: any) => d.id === id
                      );
                      return destination?.name || id;
                    }
                  );

                  destination_name = destinationNames.join(", ");
                  console.log("Destination names:", destination_name);
                } catch (error) {
                  console.warn(
                    "Error fetching all destinations:",
                    error.message
                  );

                  // Fallback to individual fetches
                  const destinationPromises = metadata.destination_id.map(
                    async (id: string) => {
                      try {
                        const destination = await destinationService.retrieve(
                          id
                        );
                        return destination?.name || id;
                      } catch {
                        return id;
                      }
                    }
                  );
                  const destinationNames = await Promise.all(
                    destinationPromises
                  );
                  destination_name = destinationNames.join(", ");
                }
              } else {
                // Handle single destination ID
                try {
                  // First try to get all destinations
                  const allDestinations = await destinationService.list({});
                  const destinations = Array.isArray(allDestinations)
                    ? allDestinations
                    : allDestinations?.data || [];

                  // Find the matching destination
                  const destination = destinations.find(
                    (d: any) => d.id === metadata.destination_id
                  );
                  destination_name =
                    destination?.name || metadata.destination_id;
                  console.log("Single destination name:", destination_name);
                } catch (error) {
                  console.warn(
                    "Error fetching all destinations:",
                    error.message
                  );

                  // Fallback to individual fetch
                  try {
                    const destination = await destinationService.retrieve(
                      metadata.destination_id
                    );
                    destination_name =
                      destination?.name || metadata.destination_id;
                  } catch {
                    destination_name = metadata.destination_id;
                  }
                }
              }
            }
          } catch (error) {
            console.warn("Could not fetch destination:", error.message);
          }
        }
      } catch (error) {
        console.warn("Error fetching related entities:", error.message);
      }

      // Store the resolved destination name in metadata for future use
      if (destination_name && metadata.destination_id) {
        metadata.destination_name = destination_name;
        console.log(
          `Storing resolved destination name in metadata: ${destination_name}`
        );

        // Update the product metadata to store the destination name
        try {
          const productServiceForUpdate = this.productService as any;
          if (typeof productServiceForUpdate.update === "function") {
            await productServiceForUpdate.update(product.id, {
              metadata: { ...metadata },
            });
            console.log(`Updated product metadata with destination name`);
          }
        } catch (error) {
          console.warn(
            "Could not update product metadata with destination name:",
            error.message
          );
        }
      }

      return {
        id: product.id,
        name: product.title || "Unnamed Service",
        description: product.description || "",
        type: metadata.service_type || "general",
        service_level: metadata.service_level || AddOnServiceLevel.HOTEL,
        hotel_id: metadata.hotel_id || null,
        hotel_name: metadata.hotel_name || hotel_name,
        destination_id: metadata.destination_id || null,
        destination_name: metadata.destination_name || destination_name,
        category_id: metadata.category_id || null,
        product_id: product.id,
        is_active: metadata.is_active !== false,
        start_date: metadata.start_date || null,
        end_date: metadata.end_date || null,
        max_capacity: metadata.max_capacity || null,
        current_capacity: metadata.current_capacity || 0,

        // Pricing information for requested currency
        pricing_type: pricing_type as AddOnPricingType,
        adult_price,
        child_price,
        package_price,
        per_day_adult_price,
        per_day_child_price,
        currency_code,

        // Multi-currency pricing data
        prices: prices,
        default_currency: default_currency,

        images: metadata.images || [],
        created_at: product.created_at,
        updated_at: product.updated_at,
        metadata: metadata, // Include the full metadata in the response
      };
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to retrieve add-on service: ${error.message}`
      );
    }
  }
  /**
   * Update an add-on service
   * @param id - The product ID of the add-on service
   * @param data - The data to update
   */
  async updateAddOnService(id: string, data: Partial<AddOnServiceInput>): Promise<AddOnServiceResponse> {
    try {
      // Get the existing product
      const product = await this.productService.retrieveProduct(id, {
        relations: ["variants"],
      });

      // Validate input if pricing data is being updated
      if (data.prices || data.adult_price !== undefined || data.child_price !== undefined ||
          data.package_price !== undefined || data.per_day_adult_price !== undefined ||
          data.per_day_child_price !== undefined) {

        // Create a temporary full input for validation
        const existingMetadata = product.metadata || {};
        const fullInput: AddOnServiceInput = {
          name: product.title || "Unnamed Service",
          service_level: (existingMetadata.service_level as AddOnServiceLevel) || AddOnServiceLevel.HOTEL,
          pricing_type: data.pricing_type || (existingMetadata.pricing_type as AddOnPricingType) || AddOnPricingType.PER_PERSON,
          ...data,
        };

        const query = this.container?.resolve("query");
        const validation = await validateAddOnServiceInput(fullInput, query);
        if (!validation.is_valid) {
          const errorMessages = getValidationErrorMessages(validation);
          throw new MedusaError(
            MedusaError.Types.INVALID_DATA,
            `Validation failed: ${errorMessages.join(", ")}`
          );
        }

        // Log warnings if any
        if (validation.warnings.length > 0) {
          console.warn("Add-on service update validation warnings:", validation.warnings);
        }
      }

      if (!product.metadata?.add_on_service) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product ${id} is not an add-on service`
        );
      }

      // Update the product
      const updateData: any = {};
      if (data.name) updateData.title = data.name;
      if (data.description !== undefined)
        updateData.description = data.description;

      // Handle multi-currency pricing updates
      let updatedPrices: MultiCurrencyPricing | undefined;
      let updatedDefaultCurrency: string | undefined;

      if (data.prices || data.currency_code || data.adult_price !== undefined ||
          data.child_price !== undefined || data.package_price !== undefined ||
          data.per_day_adult_price !== undefined || data.per_day_child_price !== undefined) {

        // Get existing pricing data
        const existingPrices = (product.metadata?.prices as MultiCurrencyPricing) || {};
        const existingDefaultCurrency = (product.metadata?.default_currency as string) || (product.metadata?.currency_code as string) || "USD";

        if (data.prices) {
          // Direct multi-currency update
          updatedPrices = { ...existingPrices, ...data.prices };
          updatedDefaultCurrency = data.default_currency || existingDefaultCurrency;
        } else {
          // Legacy single-currency update - convert to multi-currency format
          const targetCurrency = data.currency_code || existingDefaultCurrency;
          const existingCurrencyPrices = existingPrices[targetCurrency] || {};

          const updatedCurrencyPrices: CurrencyPrice = { ...existingCurrencyPrices };

          if (data.adult_price !== undefined) updatedCurrencyPrices.adult_price = data.adult_price;
          if (data.child_price !== undefined) updatedCurrencyPrices.child_price = data.child_price;
          if (data.package_price !== undefined) updatedCurrencyPrices.package_price = data.package_price;
          if (data.per_day_adult_price !== undefined) updatedCurrencyPrices.per_day_adult_price = data.per_day_adult_price;
          if (data.per_day_child_price !== undefined) updatedCurrencyPrices.per_day_child_price = data.per_day_child_price;

          updatedPrices = { ...existingPrices, [targetCurrency]: updatedCurrencyPrices };
          updatedDefaultCurrency = data.default_currency || targetCurrency;
        }
      }

      // Update metadata
      const metadata: AddOnServiceMetadata = { ...product.metadata };
      if (data.type) metadata.service_type = data.type;
      if (data.service_level) metadata.service_level = data.service_level;
      if (data.hotel_id) metadata.hotel_id = data.hotel_id;
      if (data.category_id !== undefined) metadata.category_id = data.category_id;

      // Store hotel name in metadata if available
      if (data.hotel_name) {
        metadata.hotel_name = data.hotel_name;
      } else if (data.hotel_id && !metadata.hotel_name) {
        try {
          // Try to fetch hotel name
          if (this.container && typeof this.container.resolve === "function") {
            const hotelService = this.container.resolve("hotelService");
            if (hotelService && typeof hotelService.retrieve === "function") {
              // Handle both string and array hotel_id
              if (Array.isArray(data.hotel_id)) {
                // If it's an array, use the first hotel ID to get a name
                if (data.hotel_id.length > 0) {
                  const hotel = await hotelService.retrieve(data.hotel_id[0]);
                  if (hotel && hotel.name) {
                    // If there are multiple hotels, indicate that in the name
                    const hotelName =
                      data.hotel_id.length > 1
                        ? `${hotel.name} and ${data.hotel_id.length - 1} more`
                        : hotel.name;
                    metadata.hotel_name = hotelName;
                    console.log(`Stored hotel name in metadata: ${hotelName}`);
                  }
                }
              } else {
                // Single hotel ID
                const hotel = await hotelService.retrieve(data.hotel_id);
                if (hotel && hotel.name) {
                  metadata.hotel_name = hotel.name;
                  console.log(`Stored hotel name in metadata: ${hotel.name}`);
                }
              }
            }
          }
        } catch (error) {
          console.warn(
            "Could not fetch hotel name for metadata:",
            error.message
          );
        }
      }

      // Handle destination data
      if (data.destination_id) {
        metadata.destination_id = data.destination_id;

        // Store destination name in metadata if available
        if (data.destination_name) {
          metadata.destination_name = data.destination_name;
          console.log(
            `Using provided destination name for metadata: ${data.destination_name}`
          );
        } else {
          try {
            // Try to fetch destination name(s)
            if (
              this.container &&
              typeof this.container.resolve === "function"
            ) {
              const destinationService =
                this.container.resolve("destinationService");

              if (Array.isArray(data.destination_id)) {
                // For array of destination IDs
                if (
                  destinationService &&
                  typeof destinationService.list === "function"
                ) {
                  const destinationList = await destinationService.list({});
                  const destinations = Array.isArray(destinationList)
                    ? destinationList
                    : destinationList?.data || [];

                  if (destinations.length > 0) {
                    const destinationNames = data.destination_id.map(
                      (id: string) => {
                        const destination = destinations.find(
                          (d: any) => d.id === id
                        );
                        return destination?.name || id;
                      }
                    );

                    metadata.destination_name = destinationNames.join(", ");
                    console.log(
                      `Stored destination names in metadata: ${metadata.destination_name}`
                    );
                  }
                }
              } else {
                // For single destination ID
                if (
                  destinationService &&
                  typeof destinationService.retrieve === "function"
                ) {
                  try {
                    const destination = await destinationService.retrieve(
                      data.destination_id
                    );
                    if (destination && destination.name) {
                      metadata.destination_name = destination.name;
                      console.log(
                        `Stored destination name in metadata: ${destination.name}`
                      );
                    }
                  } catch (error) {
                    console.warn(
                      "Could not fetch destination name for metadata:",
                      error.message
                    );
                  }
                }
              }
            }
          } catch (error) {
            console.warn(
              "Error fetching destination data for metadata:",
              error.message
            );
          }
        }
      }

      if (data.is_active !== undefined) metadata.is_active = data.is_active;
      if (data.start_date) metadata.start_date = data.start_date;
      if (data.end_date) metadata.end_date = data.end_date;
      if (data.max_capacity !== undefined) {
        metadata.max_capacity =
          data.max_capacity === null ? 999999 : data.max_capacity;
      }
      if (data.images) metadata.images = data.images;

      // Update pricing type if provided
      if (data.pricing_type) {
        metadata.pricing_type = data.pricing_type as AddOnPricingType;
      }

      // Update multi-currency pricing data
      if (updatedPrices) {
        metadata.prices = updatedPrices;
        metadata.default_currency = updatedDefaultCurrency;

        // Update legacy single-currency fields for backward compatibility
        const defaultCurrencyPrices = updatedPrices[updatedDefaultCurrency!] || {};

        if (defaultCurrencyPrices.adult_price !== undefined) {
          metadata.adult_price = defaultCurrencyPrices.adult_price;
          metadata.adult_price_formatted = `${(defaultCurrencyPrices.adult_price / 100).toFixed(2)} ${updatedDefaultCurrency}`;
        }

        if (defaultCurrencyPrices.child_price !== undefined) {
          metadata.child_price = defaultCurrencyPrices.child_price;
          metadata.child_price_formatted = `${(defaultCurrencyPrices.child_price / 100).toFixed(2)} ${updatedDefaultCurrency}`;
        }

        if (defaultCurrencyPrices.package_price !== undefined) {
          metadata.package_price = defaultCurrencyPrices.package_price;
          metadata.package_price_formatted = `${(defaultCurrencyPrices.package_price / 100).toFixed(2)} ${updatedDefaultCurrency}`;
        }

        if (defaultCurrencyPrices.per_day_adult_price !== undefined) {
          metadata.per_day_adult_price = defaultCurrencyPrices.per_day_adult_price;
          metadata.per_day_adult_price_formatted = `${(defaultCurrencyPrices.per_day_adult_price / 100).toFixed(2)} ${updatedDefaultCurrency} per day`;
        }

        if (defaultCurrencyPrices.per_day_child_price !== undefined) {
          metadata.per_day_child_price = defaultCurrencyPrices.per_day_child_price;
          metadata.per_day_child_price_formatted = `${(defaultCurrencyPrices.per_day_child_price / 100).toFixed(2)} ${updatedDefaultCurrency} per day`;
        }

        // Update legacy currency code
        metadata.currency_code = updatedDefaultCurrency;

        console.log(`Updated multi-currency pricing for default currency ${updatedDefaultCurrency}`);
      }

      updateData.metadata = metadata;

      // Update the product
      console.log(
        `Updating product ${id} with data:`,
        JSON.stringify(updateData, null, 2)
      );

      // Log metadata for debugging
      console.log(
        `Metadata for product ${id}:`,
        JSON.stringify(metadata, null, 2)
      );

      // Use the correct product module service method
      try {
        console.log(
          "Updating product using productModuleService.updateProducts"
        );
        console.log("Update data:", JSON.stringify(updateData, null, 2));

        await this.productService.updateProducts(id, {
          title: updateData.title,
          description: updateData.description,
          metadata: updateData.metadata,
        });

        console.log("Product updated successfully");
      } catch (error) {
        console.error("Error updating product:", error);
        console.error("Error stack:", error.stack);
        throw new Error(`Failed to update product: ${error.message}`);
      }

      // Update variants with multi-currency pricing if needed
      if (updatedPrices) {
        try {
          const adultVariant = product.variants?.find(
            (v: any) => v?.metadata?.price_type === "adult"
          );
          const childVariant = product.variants?.find(
            (v: any) => v?.metadata?.price_type === "child"
          );
          const packageVariant = product.variants?.find(
            (v: any) => v?.metadata?.price_type === "package"
          );

          // Update adult variant with multi-currency prices
          if (adultVariant) {
            const adultPrices = this.createVariantPricesArray(updatedPrices, 'adult_price');
            if (adultPrices.length > 0) {
              console.log(`Updating adult variant ${adultVariant.id} with multi-currency prices:`, adultPrices);
              // Note: Variant price updates are complex in Medusa, so we rely on metadata for now
              // In a full implementation, you might need to recreate variants or use Medusa's pricing module
            }
          }

          // Update child variant with multi-currency prices
          if (childVariant) {
            const childPrices = this.createVariantPricesArray(updatedPrices, 'child_price');
            if (childPrices.length > 0) {
              console.log(`Updating child variant ${childVariant.id} with multi-currency prices:`, childPrices);
            }
          }

          // Update package variant with multi-currency prices
          if (packageVariant) {
            const packagePrices = this.createVariantPricesArray(updatedPrices, 'package_price');
            if (packagePrices.length > 0) {
              console.log(`Updating package variant ${packageVariant.id} with multi-currency prices:`, packagePrices);
            }
          }

          console.log("Multi-currency variant price updates completed (stored in metadata)");
        } catch (error) {
          console.error("Error updating variant prices:", error);
          // Don't throw here as the main metadata update was successful
        }
      }

      // Return the updated service
      return this.retrieveAddOnService(id);
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update add-on service: ${error.message}`
      );
    }
  }

  /**
   * Upload and add an image to an add-on service
   * @param id - The product ID of the add-on service
   * @param imageUrl - The URL of the uploaded image
   */
  async uploadImage(id: string, imageUrl: string) {
    try {
      // Get the existing add-on service
      const existingService = await this.retrieveAddOnService(id);

      if (!existingService) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Add-on service with id ${id} not found`
        );
      }

      // Add the new image to existing images
      const existingImages = existingService.images || [];
      const updatedData = {
        images: [...existingImages, imageUrl], // Append new image to existing images
      };

      // Update the add-on service
      const updatedService = await this.updateAddOnService(id, updatedData);

      return updatedService;
    } catch (error) {
      console.error(`Error uploading image for add-on service ${id}:`, error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to upload image for add-on service: ${error.message}`
      );
    }
  }

  /**
   * Update images for an add-on service (replaces all images)
   * @param id - The product ID of the add-on service
   * @param imageUrls - Array of image URLs to set
   */
  async updateImages(id: string, imageUrls: string[]) {
    try {
      // Get the existing add-on service
      const existingService = await this.retrieveAddOnService(id);

      if (!existingService) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Add-on service with id ${id} not found`
        );
      }

      // Replace all images with the new array
      const updatedData = {
        images: imageUrls,
      };

      // Update the add-on service
      const updatedService = await this.updateAddOnService(id, updatedData);

      return updatedService;
    } catch (error) {
      console.error(`Error updating images for add-on service ${id}:`, error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update images for add-on service: ${error.message}`
      );
    }
  }

  /**
   * Delete an add-on service
   * @param id - The product ID of the add-on service
   */
  async deleteAddOnService(id: string) {
    try {
      // Get the product to verify it's an add-on service
      const product = await this.productService.retrieveProduct(id);

      if (!product.metadata?.add_on_service) {
        throw new MedusaError(
          MedusaError.Types.NOT_FOUND,
          `Product ${id} is not an add-on service`
        );
      }

      // Log the product service methods for debugging
      console.log(
        "Available product service methods:",
        Object.keys(this.productService)
      );

      // Delete the product using the correct method
      // In Medusa, the method is usually deleteProducts() for deleting a single product
      try {
        console.log(`Deleting product ${id} using deleteProducts() method`);
        await this.productService.deleteProducts([id]);
      } catch (deleteError) {
        console.error(`Error using deleteProducts method:`, deleteError);

        // Try alternative methods as a fallback
        // Use any type assertion to bypass TypeScript errors
        const productServiceAny = this.productService as any;

        if (typeof productServiceAny.delete === "function") {
          console.log(`Falling back to delete() method for product ${id}`);
          await productServiceAny.delete(id);
        } else if (typeof productServiceAny.deleteProduct === "function") {
          console.log(
            `Falling back to deleteProduct() method for product ${id}`
          );
          await productServiceAny.deleteProduct(id);
        } else if (typeof productServiceAny.remove === "function") {
          console.log(`Falling back to remove() method for product ${id}`);
          await productServiceAny.remove(id);
        } else {
          throw new Error("No suitable delete method found on product service");
        }
      }

      return { id };
    } catch (error) {
      console.error(`Error in deleteAddOnService:`, error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete add-on service: ${error.message}`
      );
    }
  }
}

export default AddOnServiceModuleService;
