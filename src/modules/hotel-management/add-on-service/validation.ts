import {
  MultiCurrencyPricing,
  CurrencyPrice,
  AddOnPricingType,
  AddOnServiceInput,
  PriceValidationResult,
} from "./types";

/**
 * Validation utilities for multi-currency add-on pricing
 */

/**
 * Get supported currencies from store configuration
 */
export async function getSupportedCurrencies(query: any): Promise<string[]> {
  try {
    // Get store currencies from admin API
    const storeResponse = await query.graph({
      entity: "store",
      fields: ["supported_currencies.currency_code"],
    });

    const store = storeResponse?.data?.[0];
    if (store?.supported_currencies?.length > 0) {
      return store.supported_currencies.map((c: any) => c.currency_code.toUpperCase());
    }

    // Fallback to common currencies if no store currencies configured
    return ["USD", "EUR", "GBP", "CHF", "CAD", "AUD"];
  } catch (error) {
    console.warn("Failed to fetch store currencies:", error);
    return ["USD", "EUR", "GBP", "CHF", "CAD", "AUD"];
  }
}

/**
 * Get default currency from store configuration
 */
export async function getDefaultCurrency(query: any): Promise<string> {
  try {
    const storeResponse = await query.graph({
      entity: "store",
      fields: ["supported_currencies.currency_code", "supported_currencies.is_default"],
    });

    const store = storeResponse?.data?.[0];
    if (store?.supported_currencies?.length > 0) {
      const defaultCurrency = store.supported_currencies.find((c: any) => c.is_default);
      return defaultCurrency?.currency_code?.toUpperCase() || store.supported_currencies[0]?.currency_code?.toUpperCase() || "USD";
    }

    return "USD";
  } catch (error) {
    console.warn("Failed to fetch default currency:", error);
    return "USD";
  }
}

/**
 * Validate currency code against store configuration
 */
export async function validateCurrencyCode(currencyCode: string, query: any): Promise<boolean> {
  try {
    const supportedCurrencies = await getSupportedCurrencies(query);
    return supportedCurrencies.includes(currencyCode.toUpperCase());
  } catch (error) {
    console.warn("Currency validation failed:", error);
    return validateCurrencyCodeFormat(currencyCode);
  }
}

/**
 * Fallback validation for currency code format (when query not available)
 */
export function validateCurrencyCodeFormat(currencyCode: string): boolean {
  return /^[A-Z]{3}$/.test(currencyCode.toUpperCase());
}

/**
 * Validate individual currency price based on pricing type
 */
export function validateCurrencyPrice(
  currencyPrice: CurrencyPrice,
  pricingType: AddOnPricingType,
  currencyCode: string
): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];

  switch (pricingType) {
    case AddOnPricingType.PACKAGE:
      if (!currencyPrice.package_price || currencyPrice.package_price <= 0) {
        errors.push(`Package price is required and must be greater than 0 for ${currencyCode}`);
      }
      break;

    case AddOnPricingType.USAGE_BASED:
      const hasValidAdultPrice = currencyPrice.per_day_adult_price && currencyPrice.per_day_adult_price > 0;
      const hasValidChildPrice = currencyPrice.per_day_child_price && currencyPrice.per_day_child_price > 0;
      
      if (!hasValidAdultPrice && !hasValidChildPrice) {
        errors.push(`At least one per-day price (adult or child) is required for ${currencyCode}`);
      }
      break;

    case AddOnPricingType.PER_PERSON:
    default:
      const hasValidAdult = currencyPrice.adult_price && currencyPrice.adult_price > 0;
      const hasValidChild = currencyPrice.child_price && currencyPrice.child_price > 0;
      
      if (!hasValidAdult && !hasValidChild) {
        errors.push(`At least one price (adult or child) is required for ${currencyCode}`);
      }
      break;
  }

  // Validate that all prices are positive numbers
  Object.entries(currencyPrice).forEach(([priceType, value]) => {
    if (value !== undefined && value !== null) {
      if (typeof value !== 'number' || value < 0) {
        errors.push(`${priceType} must be a positive number for ${currencyCode}`);
      }
      if (value > 999999) {
        errors.push(`${priceType} is too large for ${currencyCode} (max: 999,999)`);
      }
    }
  });

  return { isValid: errors.length === 0, errors };
}

/**
 * Validate multi-currency pricing structure
 */
export async function validateMultiCurrencyPricing(
  prices: MultiCurrencyPricing,
  pricingType: AddOnPricingType,
  defaultCurrency?: string,
  query?: any
): Promise<PriceValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if prices object exists and has at least one currency
  if (!prices || Object.keys(prices).length === 0) {
    errors.push("At least one currency price is required");
    return { is_valid: false, errors, warnings };
  }

  // Get supported currencies from store if query is available
  let supportedCurrencies: string[] = [];
  if (query) {
    try {
      supportedCurrencies = await getSupportedCurrencies(query);
    } catch (error) {
      warnings.push("Could not fetch store currencies, using format validation only");
    }
  }

  // Validate each currency
  for (const [currencyCode, currencyPrice] of Object.entries(prices)) {
    // Validate currency code
    let isValidCurrency = false;
    if (query && supportedCurrencies.length > 0) {
      isValidCurrency = supportedCurrencies.includes(currencyCode.toUpperCase());
      if (!isValidCurrency) {
        errors.push(`Currency ${currencyCode} is not supported by the store. Supported currencies: ${supportedCurrencies.join(", ")}`);
        continue;
      }
    } else {
      // Fallback to format validation
      isValidCurrency = validateCurrencyCodeFormat(currencyCode);
      if (!isValidCurrency) {
        errors.push(`Invalid currency code format: ${currencyCode}`);
        continue;
      }
    }

    // Validate currency price
    const validation = validateCurrencyPrice(currencyPrice, pricingType, currencyCode);
    if (!validation.isValid) {
      errors.push(...validation.errors);
    }
  }

  // Validate default currency
  if (defaultCurrency) {
    let isValidDefaultCurrency = false;
    if (query && supportedCurrencies.length > 0) {
      isValidDefaultCurrency = supportedCurrencies.includes(defaultCurrency.toUpperCase());
      if (!isValidDefaultCurrency) {
        errors.push(`Default currency ${defaultCurrency} is not supported by the store`);
      }
    } else {
      isValidDefaultCurrency = validateCurrencyCodeFormat(defaultCurrency);
      if (!isValidDefaultCurrency) {
        errors.push(`Invalid default currency code format: ${defaultCurrency}`);
      }
    }

    if (isValidDefaultCurrency && !prices[defaultCurrency]) {
      errors.push(`Default currency ${defaultCurrency} must have pricing data`);
    }
  } else {
    warnings.push("No default currency specified, using first available currency");
  }

  // Check for common currency combinations
  const currencyCodes = Object.keys(prices);
  if (currencyCodes.length > 10) {
    warnings.push("Large number of currencies may impact performance");
  }

  return {
    is_valid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Validate add-on service input with multi-currency support
 */
export async function validateAddOnServiceInput(
  input: AddOnServiceInput,
  query?: any
): Promise<PriceValidationResult> {
  const errors: string[] = [];
  const warnings: string[] = [];

  // Check if we have multi-currency or legacy pricing
  const hasMultiCurrencyPrices = input.prices && Object.keys(input.prices).length > 0;
  const hasLegacyPrices = 
    input.adult_price !== undefined ||
    input.child_price !== undefined ||
    input.package_price !== undefined ||
    input.per_day_adult_price !== undefined ||
    input.per_day_child_price !== undefined;

  if (!hasMultiCurrencyPrices && !hasLegacyPrices) {
    errors.push("Either multi-currency prices or legacy pricing must be provided");
    return { is_valid: false, errors, warnings };
  }

  // Validate multi-currency pricing if provided
  if (hasMultiCurrencyPrices) {
    const validation = await validateMultiCurrencyPricing(
      input.prices!,
      input.pricing_type || AddOnPricingType.PER_PERSON,
      input.default_currency,
      query
    );

    errors.push(...validation.errors);
    warnings.push(...validation.warnings);
  }

  // Validate legacy pricing if provided (for backward compatibility)
  if (hasLegacyPrices && !hasMultiCurrencyPrices) {
    const currencyCode = input.currency_code || "USD";

    // Validate currency code against store configuration
    if (query) {
      const isValid = await validateCurrencyCode(currencyCode, query);
      if (!isValid) {
        const supportedCurrencies = await getSupportedCurrencies(query);
        errors.push(`Currency ${currencyCode} is not supported by the store. Supported currencies: ${supportedCurrencies.join(", ")}`);
      }
    } else {
      // Fallback to format validation
      if (!validateCurrencyCodeFormat(currencyCode)) {
        errors.push(`Invalid currency code format: ${currencyCode}`);
      }
    }

    const pricingType = input.pricing_type || AddOnPricingType.PER_PERSON;
    
    switch (pricingType) {
      case AddOnPricingType.PACKAGE:
        if (!input.package_price || input.package_price <= 0) {
          errors.push("Package price is required and must be greater than 0");
        }
        break;

      case AddOnPricingType.USAGE_BASED:
        const hasValidAdultPrice = input.per_day_adult_price && input.per_day_adult_price > 0;
        const hasValidChildPrice = input.per_day_child_price && input.per_day_child_price > 0;
        
        if (!hasValidAdultPrice && !hasValidChildPrice) {
          errors.push("At least one per-day price (adult or child) is required");
        }
        break;

      case AddOnPricingType.PER_PERSON:
      default:
        const hasValidAdult = input.adult_price && input.adult_price > 0;
        const hasValidChild = input.child_price && input.child_price > 0;
        
        if (!hasValidAdult && !hasValidChild) {
          errors.push("At least one price (adult or child) is required");
        }
        break;
    }
  }

  // Warn if both multi-currency and legacy pricing are provided
  if (hasMultiCurrencyPrices && hasLegacyPrices) {
    warnings.push("Both multi-currency and legacy pricing provided. Multi-currency pricing will take precedence.");
  }

  return {
    is_valid: errors.length === 0,
    errors,
    warnings,
  };
}

/**
 * Sanitize and normalize pricing input
 */
export function sanitizePricingInput(input: AddOnServiceInput): AddOnServiceInput {
  const sanitized = { ...input };

  // Normalize currency codes to uppercase
  if (sanitized.currency_code) {
    sanitized.currency_code = sanitized.currency_code.toUpperCase();
  }
  if (sanitized.default_currency) {
    sanitized.default_currency = sanitized.default_currency.toUpperCase();
  }

  // Normalize multi-currency prices
  if (sanitized.prices) {
    const normalizedPrices: MultiCurrencyPricing = {};
    
    for (const [currencyCode, currencyPrice] of Object.entries(sanitized.prices)) {
      const normalizedCurrency = currencyCode.toUpperCase();
      const normalizedPrice: CurrencyPrice = {};
      
      // Round prices to 2 decimal places and convert to cents
      Object.entries(currencyPrice).forEach(([priceType, value]) => {
        if (value !== undefined && value !== null && typeof value === 'number') {
          normalizedPrice[priceType as keyof CurrencyPrice] = Math.round(value * 100);
        }
      });
      
      normalizedPrices[normalizedCurrency] = normalizedPrice;
    }
    
    sanitized.prices = normalizedPrices;
  }

  // Round legacy prices to cents
  if (sanitized.adult_price) {
    sanitized.adult_price = Math.round(sanitized.adult_price * 100);
  }
  if (sanitized.child_price) {
    sanitized.child_price = Math.round(sanitized.child_price * 100);
  }
  if (sanitized.package_price) {
    sanitized.package_price = Math.round(sanitized.package_price * 100);
  }
  if (sanitized.per_day_adult_price) {
    sanitized.per_day_adult_price = Math.round(sanitized.per_day_adult_price * 100);
  }
  if (sanitized.per_day_child_price) {
    sanitized.per_day_child_price = Math.round(sanitized.per_day_child_price * 100);
  }

  return sanitized;
}

/**
 * Format price for display
 */
export function formatPrice(amount: number, currencyCode: string): string {
  try {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currencyCode,
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(amount / 100);
  } catch (error) {
    // Fallback if currency is not supported by Intl.NumberFormat
    return `${currencyCode} ${(amount / 100).toFixed(2)}`;
  }
}

/**
 * Get validation error messages for display
 */
export function getValidationErrorMessages(validation: PriceValidationResult): string[] {
  const messages: string[] = [];
  
  if (validation.errors.length > 0) {
    messages.push("Pricing validation errors:");
    messages.push(...validation.errors.map(error => `• ${error}`));
  }
  
  if (validation.warnings.length > 0) {
    messages.push("Pricing validation warnings:");
    messages.push(...validation.warnings.map(warning => `• ${warning}`));
  }
  
  return messages;
}
