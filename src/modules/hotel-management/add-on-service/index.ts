import { Module } from "@camped-ai/framework/utils";
import AddOnServiceModuleService from "./service";

export const ADD_ON_SERVICE_MODULE = "add_on_service";
export const ADD_ON_SERVICE = "addOnServiceService";

// Export types
export {
  AddOnServiceType,
  AddOnServiceLevel,
  AddOnPricingType,
  CurrencyPrice,
  MultiCurrencyPricing,
  AddOnServiceMetadata,
  AddOnServiceInput,
  AddOnServiceResponse,
  CurrencyContext,
  LegacyPriceData,
  CreateAddOnServiceRequest,
  UpdateAddOnServiceRequest,
  ListAddOnServicesRequest,
  ListAddOnServicesResponse,
  StoreAddOnServiceResponse,
} from "./types";

// Create the module
const addOnServiceModule = Module(ADD_ON_SERVICE_MODULE, {
  service: AddOnServiceModuleService,
});

// Export the module
export default addOnServiceModule;
