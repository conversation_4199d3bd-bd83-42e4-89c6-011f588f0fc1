// Define add-on service types - simplified to string
export type AddOnServiceType = string;

// Define add-on service levels
export enum AddOnServiceLevel {
  HOTEL = "hotel",
  DESTINATION = "destination",
}

// Define pricing types
export enum AddOnPricingType {
  PER_PERSON = "per_person",
  PACKAGE = "package",
  USAGE_BASED = "usage_based",
}

// Multi-currency pricing structure
export interface CurrencyPrice {
  adult_price?: number;
  child_price?: number;
  package_price?: number;
  per_day_adult_price?: number;
  per_day_child_price?: number;
}

export interface MultiCurrencyPricing {
  [currencyCode: string]: CurrencyPrice;
}

// Enhanced metadata structure for multi-currency support
export interface AddOnServiceMetadata {
  add_on_service: boolean;
  service_type: string;
  service_level: AddOnServiceLevel;
  max_capacity?: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
  images?: string[];
  pricing_type: AddOnPricingType;
  category_id?: string;
  hotel_id?: string | string[];
  hotel_name?: string;
  destination_id?: string | string[];
  destination_name?: string;
  
  // Multi-currency pricing
  prices?: MultiCurrencyPricing;
  default_currency?: string;
  
  // Legacy single-currency fields (for backward compatibility)
  currency_code?: string;
  adult_price?: number;
  child_price?: number;
  package_price?: number;
  per_day_adult_price?: number;
  per_day_child_price?: number;
  
  // Formatted price strings for display
  adult_price_formatted?: string;
  child_price_formatted?: string;
  package_price_formatted?: string;
  per_day_adult_price_formatted?: string;
  per_day_child_price_formatted?: string;
}

// Input data structure for creating/updating add-on services
export interface AddOnServiceInput {
  name: string;
  description?: string;
  type?: string;
  service_level: AddOnServiceLevel;
  hotel_id?: string | string[];
  destination_id?: string | string[];
  category_id?: string;
  is_active?: boolean;
  start_date?: string;
  end_date?: string;
  max_capacity?: number;
  pricing_type: AddOnPricingType;
  images?: string[];
  handle?: string;
  
  // Multi-currency pricing input
  prices?: MultiCurrencyPricing;
  default_currency?: string;
  
  // Legacy single-currency input (for backward compatibility)
  currency_code?: string;
  adult_price?: number;
  child_price?: number;
  package_price?: number;
  per_day_adult_price?: number;
  per_day_child_price?: number;
}

// Response structure for add-on services
export interface AddOnServiceResponse {
  id: string;
  name: string;
  description?: string;
  type: string;
  service_level: AddOnServiceLevel;
  hotel_id?: string | string[];
  hotel_name?: string;
  destination_id?: string | string[];
  destination_name?: string;
  category_id?: string;
  product_id: string;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
  max_capacity?: number;
  current_capacity?: number;
  pricing_type: AddOnPricingType;
  images?: string[];
  created_at: string;
  updated_at: string;
  
  // Multi-currency pricing response
  prices?: MultiCurrencyPricing;
  default_currency?: string;
  
  // Current currency context (for API responses)
  currency_code?: string;
  adult_price?: number;
  child_price?: number;
  package_price?: number;
  per_day_adult_price?: number;
  per_day_child_price?: number;
  
  metadata?: AddOnServiceMetadata;
}

// Utility types for currency operations
export interface CurrencyContext {
  requested_currency: string;
  default_currency: string;
  supported_currencies: string[];
}

// Price conversion utilities
export interface PriceConversionOptions {
  from_currency: string;
  to_currency: string;
  amount: number;
  exchange_rates?: { [key: string]: number };
}

// Validation types
export interface PriceValidationResult {
  is_valid: boolean;
  errors: string[];
  warnings: string[];
}

// Migration types for backward compatibility
export interface LegacyPriceData {
  currency_code: string;
  adult_price?: number;
  child_price?: number;
  package_price?: number;
  per_day_adult_price?: number;
  per_day_child_price?: number;
}

export interface MigrationResult {
  success: boolean;
  migrated_count: number;
  errors: string[];
  warnings: string[];
}

// API request/response types
export interface CreateAddOnServiceRequest extends AddOnServiceInput {}

export interface UpdateAddOnServiceRequest extends Partial<AddOnServiceInput> {
  id: string;
}

export interface ListAddOnServicesRequest {
  service_level?: AddOnServiceLevel;
  hotel_id?: string;
  destination_id?: string;
  type?: string;
  is_active?: boolean;
  currency_code?: string;
  limit?: number;
  offset?: number;
}

export interface ListAddOnServicesResponse {
  add_on_services: AddOnServiceResponse[];
  count: number;
  limit: number;
  offset: number;
}

// Store API specific types
export interface StoreAddOnServiceResponse {
  id: string;
  name: string;
  description: string;
  service_type: string;
  service_level: AddOnServiceLevel;
  category_id?: string;
  pricing_type: AddOnPricingType;
  adult_price: number;
  child_price: number;
  package_price?: number;
  per_day_adult_price: number;
  per_day_child_price: number;
  currency_code: string;
  max_capacity?: number;
  is_active: boolean;
  start_date?: string;
  end_date?: string;
  images?: string[];
}
