import { MedusaService } from "@camped-ai/framework/utils";
import { RoomInventory, RoomInventoryStatus } from "./models/room-inventory";
import { Modules } from "@camped-ai/framework/utils";
import { updateSharedRoomInventory } from "./utils/shared-room-inventory";
import { format } from "date-fns";

class RoomInventoryService extends MedusaService({
  RoomInventory,
}) {
  protected readonly container: any;
  protected readonly productService: any;
  protected readonly productVariantService: any;

  constructor(container: any) {
    super(container);
    this.container = container;
    this.productService = null;
    this.productVariantService = null;

    if (container) {
      try {
        this.productService = container.resolve("productService");
      } catch (e) {
        console.log(
          "Product service not available, some functionality may be limited"
        );
      }

      try {
        this.productVariantService = container.resolve("productVariantService");
      } catch (e) {
        console.log(
          "Product variant service not available, some functionality may be limited"
        );
      }
    }
  }

  // The createRoomInventories method is already provided by MedusaService
  // We don't need to implement it here as it would cause an infinite recursion

  /**
   * Check availability for a specific room (variant)
   * @param roomId - The ID of the room (variant_id)
   * @param startDate - The start date of the availability period
   * @param endDate - The end date of the availability period
   * @param includeDetails - Whether to include detailed inventory entries in the response
   */
  async checkAvailability(
    roomId: string,
    startDate: Date | string,
    endDate: Date | string,
    includeDetails = false
  ): Promise<any> {
    try {
      // Convert dates to Date objects if they're strings
      const start =
        typeof startDate === "string" ? new Date(startDate) : startDate;
      const end = typeof endDate === "string" ? new Date(endDate) : endDate;

      // Validate dates
      if (start > end) {
        throw new Error("Start date must be before end date");
      }

      // Query the room_inventory table for this room
      // For database query, we'll use a broader range to catch entries with different time components
      // We'll filter more precisely after fetching the entries
      const queryStartDate = new Date(start);
      queryStartDate.setHours(0, 0, 0, 0);

      const queryEndDate = new Date(end);
      queryEndDate.setHours(23, 59, 59, 999);

      const inventoryEntries = await this.listRoomInventories({
        inventory_item_id: roomId,
        from_date: { $lte: queryEndDate }, // Entry starts before or on our end date
        to_date: { $gte: queryStartDate }, // Entry ends after or on our start date
      });

      console.log("checkAvailability - inventoryEntries:", inventoryEntries);

      // Find entries that match our date range, ignoring time components
      const containedEntries = inventoryEntries.filter((entry: any) => {
        // Normalize dates by removing time component for comparison
        const entryFromDate = new Date(entry.from_date);
        entryFromDate.setHours(0, 0, 0, 0);

        const entryToDate = new Date(entry.to_date);
        entryToDate.setHours(0, 0, 0, 0);

        const startDate = new Date(start);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(end);
        endDate.setHours(0, 0, 0, 0);

        // Compare dates without time component
        return (
          entryFromDate.getTime() >= startDate.getTime() &&
          entryToDate.getTime() <= endDate.getTime()
        );
      });

      // If we have contained entries, use only those for availability check
      if (containedEntries.length > 0) {
        console.log(
          "Found entries contained within date range for availability check"
        );
        // Replace the inventory entries with only the contained entries
        inventoryEntries.length = 0;
        containedEntries.forEach((entry: any) => inventoryEntries.push(entry));
      }

      // If no entries found, the room is unavailable for these dates
      if (inventoryEntries.length === 0) {
        return {
          available: false,
          unavailableDates: [start.toISOString().split("T")[0]],
          unavailableReasons: [
            {
              date: start.toISOString().split("T")[0],
              reason: "No inventory entries found for these dates",
            },
          ],
        };
      }

      // Check if any entry has status other than "available"
      const unavailableEntries = inventoryEntries.filter(
        (entry: any) =>
          entry.status !== "available" || entry.available_quantity < 1
      );

      if (unavailableEntries.length > 0) {
        const unavailableDates = unavailableEntries.map((entry: any) => {
          const fromDate = new Date(entry.from_date);
          return fromDate.toISOString().split("T")[0];
        });

        const unavailableReasons = unavailableEntries.map((entry: any) => {
          const fromDate = new Date(entry.from_date);
          return {
            date: fromDate.toISOString().split("T")[0],
            reason: `Room is ${entry.status} for these dates`,
          };
        });

        return {
          available: false,
          unavailableDates,
          unavailableReasons,
          ...(includeDetails && { inventoryEntries }),
        };
      }

      // If we got here, the room is available
      return {
        available: true,
        ...(includeDetails && { inventoryEntries }),
      };
    } catch (error: any) {
      throw new Error(`Failed to check availability: ${error.message}`);
    }
  }

  /**
   * Update the status of a room for a specific date range
   * @param roomId - The ID of the room (variant_id)
   * @param startDate - The start date of the period
   * @param endDate - The end date of the period
   * @param status - The new status ('available', 'reserved', 'booked', 'maintenance', 'cleaning', 'reserved_unassigned', 'cart_reserved')
   * @param notes - Optional notes about the status change
   * @param bookingInfo - Optional booking information to include in notes
   * @param expiresAt - Optional expiration date for cart reservations
   * @param orderId - Optional order ID to associate with this inventory entry
   * @param cartId - Optional cart ID to associate with this inventory entry
   */
  async updateInventoryStatus(
    roomId: string,
    startDate: Date | string,
    endDate: Date | string,
    status: string,
    notes: string | null = null,
    bookingInfo: Record<string, any> | null = null,
    expiresAt: Date | string | null = null,
    orderId: string | null = null,
    cartId: string | null = null,
    skipSharedRooms: boolean = false // Flag to prevent infinite recursion
  ): Promise<any[]> {
    try {
      console.log(roomId, startDate, endDate, status);
      // Validate status using the enum values as strings
      const validStatuses = Object.values(RoomInventoryStatus);
      if (!validStatuses.includes(status as any)) {
        throw new Error(
          `Invalid status: ${status}. Must be one of: ${validStatuses.join(
            ", "
          )}`
        );
      }

      // For cart reservations, add expiration info to notes
      if (status === RoomInventoryStatus.CART_RESERVED && expiresAt) {
        const expiresAtStr =
          typeof expiresAt === "string" ? expiresAt : expiresAt.toISOString();
        notes = notes
          ? `${notes} (Expires: ${expiresAtStr})`
          : `Cart reservation (Expires: ${expiresAtStr})`;
      }

      // Add booking info to notes if provided
      if (bookingInfo) {
        const bookingInfoStr = Object.entries(bookingInfo)
          .map(([key, value]) => `${key}: ${value}`)
          .join(", ");

        notes = notes ? `${notes} (${bookingInfoStr})` : bookingInfoStr;
      }

      // Convert dates to Date objects if they're strings
      const start =
        typeof startDate === "string" ? new Date(startDate) : startDate;
      const end = typeof endDate === "string" ? new Date(endDate) : endDate;

      // Validate dates
      if (start > end) {
        throw new Error("Start date must be before end date");
      }
      console.log("startEnd", start, end);

      // For database query, we'll use a broader range to catch entries with different time components
      const queryStartDate = new Date(start);
      queryStartDate.setHours(0, 0, 0, 0);

      const queryEndDate = new Date(end);
      queryEndDate.setHours(23, 59, 59, 999);

      // Find existing entries that overlap with our date range
      const existingEntries = await this.listRoomInventories({
        inventory_item_id: roomId,
        from_date: { $lte: queryEndDate }, // Entry starts before or on our end date
        to_date: { $gte: queryStartDate }, // Entry ends after or on our start date
      });
      console.log("existingEntries", existingEntries);

      // Find entries that match our date range, ignoring time components
      const containedEntries = existingEntries.filter((entry) => {
        // Normalize dates by removing time component for comparison
        const entryFromDate = new Date(entry.from_date);
        entryFromDate.setHours(0, 0, 0, 0);

        const entryToDate = new Date(entry.to_date);
        entryToDate.setHours(0, 0, 0, 0);

        const startDate = new Date(start);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(end);
        endDate.setHours(0, 0, 0, 0);

        // Compare dates without time component
        return (
          entryFromDate.getTime() >= startDate.getTime() &&
          entryToDate.getTime() <= endDate.getTime()
        );
      });

      // If we have contained entries, update only those
      if (containedEntries.length > 0) {
        console.log(
          "Found entries contained within date range, updating only those entries"
        );
        const updatedEntries = [];
        for (const entry of containedEntries) {
          const updated = await this.updateRoomInventories({
            id: entry.id,
            status,
            available_quantity: status === "available" ? 1 : 0,
            notes: notes || entry.notes,
            order_id: orderId,
            cart_id: cartId,
          });
          updatedEntries.push(updated);
        }

        // Update shared room inventory if this is not a recursive call
        if (!skipSharedRooms) {
          try {
            // Use the utility function to update shared room inventory
            await updateSharedRoomInventory(
              this.container,
              roomId,
              startDate,
              endDate,
              status,
              notes,
              orderId,
              cartId,
              true // createIfNotExists - create new records if none exist
            );
          } catch (sharedRoomError) {
            console.error(
              "Error updating shared room inventory:",
              sharedRoomError
            );
            // Continue with the main room update even if shared room update fails
          }
        }

        return updatedEntries;
      }

      // If no contained entries were found, we need to create a new inventory record
      console.log(
        `No existing inventory records found for room ${roomId}, creating new record`
      );

      // Convert dates to Date objects if they're strings
      const fromDate =
        typeof startDate === "string" ? new Date(startDate) : startDate;
      const toDate = typeof endDate === "string" ? new Date(endDate) : endDate;

      try {
        // Create a new inventory record for this room
        const newInventoryRecord = await this.createRoomInventories([
          {
            inventory_item_id: roomId,
            from_date: fromDate,
            to_date: toDate,
            status: status,
            available_quantity:
              status === "available" || status === "on_demand" ? 1 : 0,
            notes: notes || `Created on ${new Date().toISOString()}`,
            order_id: orderId,
            cart_id: cartId,
            check_in_time: "14:00", // Default check-in time
            check_out_time: "12:00", // Default check-out time
            is_noon_to_noon: true,
          },
        ]);

        console.log(
          `Successfully created new inventory record for room ${roomId}:`,
          newInventoryRecord
        );

        // Update shared room inventory if this is not a recursive call
        if (!skipSharedRooms) {
          try {
            // Use the utility function to update shared room inventory
            await updateSharedRoomInventory(
              this.container,
              roomId,
              startDate,
              endDate,
              status,
              notes,
              orderId,
              cartId,
              true // createIfNotExists - create new records if none exist
            );
          } catch (sharedRoomError) {
            console.error(
              "Error updating shared room inventory:",
              sharedRoomError
            );
            // Continue even if shared room update fails
          }
        }

        return newInventoryRecord;
      } catch (createError) {
        console.error(
          `Error creating inventory record for room ${roomId}:`,
          createError
        );
        return [];
      }
    } catch (error: any) {
      throw new Error(`Failed to update inventory status: ${error.message}`);
    }
  }

  /**
   * Release a room (make it available again)
   * @param roomId - The ID of the room (variant_id)
   * @param startDate - The start date of the period
   * @param endDate - The end date of the period
   */
  async releaseRoom(
    roomId: string,
    startDate: Date | string,
    endDate: Date | string,
    skipSharedRooms: boolean = false // Flag to prevent infinite recursion
  ): Promise<any[]> {
    try {
      // Convert dates to Date objects if they're strings
      const start =
        typeof startDate === "string" ? new Date(startDate) : startDate;
      const end = typeof endDate === "string" ? new Date(endDate) : endDate;

      // For database query, we'll use a broader range to catch entries with different time components
      const queryStartDate = new Date(start);
      queryStartDate.setHours(0, 0, 0, 0);

      const queryEndDate = new Date(end);
      queryEndDate.setHours(23, 59, 59, 999);

      // Find existing entries for this room and date range
      const existingEntries = await this.listRoomInventories({
        inventory_item_id: roomId,
        from_date: { $lte: queryEndDate }, // Entry starts before or on our end date
        to_date: { $gte: queryStartDate }, // Entry ends after or on our start date
      });

      console.log("Releasing room - existingEntries:", existingEntries);

      if (existingEntries.length === 0) {
        // No reservation or booking found, nothing to release
        console.log("No entries found to release");
        return [];
      }

      // Find entries that match our date range, ignoring time components
      const containedEntries = existingEntries.filter((entry) => {
        // Normalize dates by removing time component for comparison
        const entryFromDate = new Date(entry.from_date);
        entryFromDate.setHours(0, 0, 0, 0);

        const entryToDate = new Date(entry.to_date);
        entryToDate.setHours(0, 0, 0, 0);

        const startDate = new Date(start);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(end);
        endDate.setHours(0, 0, 0, 0);

        // Compare dates without time component
        return (
          entryFromDate.getTime() >= startDate.getTime() &&
          entryToDate.getTime() <= endDate.getTime()
        );
      });

      // If we have contained entries, update only those
      if (containedEntries.length > 0) {
        console.log(
          "Found entries contained within date range, releasing only those entries"
        );
        const updatedEntries = [];
        for (const entry of containedEntries) {
          const updated = await this.updateRoomInventories({
            id: entry.id,
            status: RoomInventoryStatus.AVAILABLE,
            available_quantity: 1,
            notes: `Released on ${new Date().toISOString()}. Previous status: ${
              entry.status
            }`,
            order_id: null,
            cart_id: null,
          });
          updatedEntries.push(updated);
        }

        // Update shared room inventory if this is not a recursive call
        if (!skipSharedRooms) {
          try {
            // Use the utility function to update shared room inventory
            await updateSharedRoomInventory(
              this.container,
              roomId,
              startDate,
              endDate,
              RoomInventoryStatus.AVAILABLE,
              `Released on ${new Date().toISOString()} (shared room update)`,
              null,
              null,
              true // createIfNotExists - create new records if none exist
            );
          } catch (sharedRoomError) {
            console.error(
              "Error updating shared room inventory:",
              sharedRoomError
            );
            // Continue with the main room update even if shared room update fails
          }
        }

        return updatedEntries;
      }

      // If no contained entries, we'll create a new available entry for this date range
      console.log(
        "No contained entries found, creating new available inventory entry"
      );
      const newEntry = await this.createRoomInventories([
        {
          inventory_item_id: roomId,
          from_date: start,
          to_date: end,
          status: RoomInventoryStatus.AVAILABLE,
          available_quantity: 1,
          notes: `Released on ${new Date().toISOString()}. No exact match found.`,
        },
      ]);

      // Update shared room inventory if this is not a recursive call
      if (!skipSharedRooms) {
        try {
          // Use the utility function to update shared room inventory
          await updateSharedRoomInventory(
            this.container,
            roomId,
            startDate,
            endDate,
            RoomInventoryStatus.AVAILABLE,
            `Released on ${new Date().toISOString()} (shared room update)`,
            null,
            null,
            true // createIfNotExists - create new records if none exist
          );
        } catch (sharedRoomError) {
          console.error(
            "Error updating shared room inventory:",
            sharedRoomError
          );
          // Continue even if shared room update fails
        }
      }

      return newEntry;
    } catch (error: any) {
      throw new Error(`Failed to release room: ${error.message}`);
    }
  }

  /**
   * Split a booking into two parts at the specified date
   * @param orderId - The ID of the order/booking to split
   * @param splitDate - The date to split the booking (check-out from first part, check-in to second part)
   * @param keepFirstPart - If true, keep first part in current room and move second part to unallocated
   * @returns Object containing IDs of the two new booking parts
   */
  async splitBooking(
    orderId: string,
    splitDate: string,
    keepFirstPart: boolean = true
  ): Promise<any> {
    try {
      console.log(
        `Splitting booking ${orderId} at ${splitDate}, keepFirstPart: ${keepFirstPart}`
      );

      // Get the order service
      const orderService = this.productService?.container?.resolve(
        Modules.ORDER
      );

      // Get the original order
      const order = await orderService.retrieve(orderId, {
        relations: ["items", "customer"],
      });

      if (!order) {
        throw new Error(`Order with id ${orderId} not found`);
      }

      // Check if order has metadata with reservations
      if (!order.metadata?.reservations) {
        throw new Error("Order does not have reservation information");
      }

      // Get reservation details
      const reservations = order.metadata.reservations || [];
      if (!Array.isArray(reservations) || reservations.length === 0) {
        throw new Error("No reservations found in order");
      }

      // Validate split date is within reservation period
      const splitDateObj = new Date(splitDate);
      let reservationToSplit = null;
      let reservationIndex = -1;

      for (let i = 0; i < reservations.length; i++) {
        const reservation = reservations[i];
        const fromDate = new Date(reservation.from_date);
        const toDate = new Date(reservation.to_date);

        // Check if split date is between from_date and to_date
        if (splitDateObj > fromDate && splitDateObj < toDate) {
          reservationToSplit = reservation;
          reservationIndex = i;
          break;
        }
      }

      if (!reservationToSplit) {
        throw new Error(
          "Split date must be between check-in and check-out dates"
        );
      }

      console.log("Found reservation to split:", reservationToSplit);

      // Create two new reservation objects
      const firstPartReservation = {
        ...reservationToSplit,
        id: `${reservationToSplit.id || `res_${Date.now()}`}_part1`,
        to_date: splitDate,
        split_part: "first_part",
        room_id: keepFirstPart ? reservationToSplit.room_id : null,
        status: keepFirstPart
          ? reservationToSplit.status
          : "reserved_unassigned",
      };

      const secondPartReservation = {
        ...reservationToSplit,
        id: `${reservationToSplit.id || `res_${Date.now()}`}_part2`,
        from_date: splitDate,
        to_date: reservationToSplit.to_date,
        split_part: "second_part",
        room_id: keepFirstPart ? null : reservationToSplit.room_id,
        status: keepFirstPart
          ? "reserved_unassigned"
          : reservationToSplit.status,
      };

      console.log("Created split reservations:", {
        firstPart: firstPartReservation,
        secondPart: secondPartReservation,
      });

      // Remove the original reservation and add the two new ones
      const updatedReservations = [...reservations];
      if (reservationIndex >= 0) {
        updatedReservations.splice(reservationIndex, 1);
      }
      updatedReservations.push(firstPartReservation, secondPartReservation);

      // Update the order metadata
      const updatedMetadata = {
        ...order.metadata,
        split_booking: true,
        split_date: splitDate,
        reservations: updatedReservations,
      };

      console.log("Updating order metadata with split information");

      try {
        await orderService.update(orderId, {
          metadata: updatedMetadata,
        });
        console.log("Order metadata updated successfully");
      } catch (updateError) {
        console.error("Error updating order metadata:", updateError);
        throw new Error(
          `Failed to update order metadata: ${updateError.message}`
        );
      }

      // Update room inventory records if a room was assigned
      if (reservationToSplit.room_id) {
        console.log(
          `Updating room inventory for room ${reservationToSplit.room_id}`
        );

        try {
          // Update the existing inventory record to end at split date
          if (keepFirstPart) {
            // First part keeps the room
            await this.updateInventoryStatus(
              reservationToSplit.room_id,
              new Date(reservationToSplit.from_date),
              new Date(splitDate),
              reservationToSplit.status || "booked",
              `Split booking - first part of ${orderId}`
            );

            // Second part is unallocated, so we make the room available
            await this.updateInventoryStatus(
              reservationToSplit.room_id,
              new Date(splitDate),
              new Date(reservationToSplit.to_date),
              "available",
              `Split booking - room released for second part of ${orderId}`
            );
          } else {
            // First part is unallocated, so we make the room available
            await this.updateInventoryStatus(
              reservationToSplit.room_id,
              new Date(reservationToSplit.from_date),
              new Date(splitDate),
              "available",
              `Split booking - room released for first part of ${orderId}`
            );

            // Second part keeps the room
            await this.updateInventoryStatus(
              reservationToSplit.room_id,
              new Date(splitDate),
              new Date(reservationToSplit.to_date),
              reservationToSplit.status || "booked",
              `Split booking - second part of ${orderId}`
            );
          }

          console.log("Room inventory updated successfully");
        } catch (inventoryError) {
          console.error("Error updating room inventory:", inventoryError);
          // We don't throw here as the order metadata was already updated
          // Instead, we log the error and continue
        }
      }

      return {
        first_part_id: firstPartReservation.id,
        second_part_id: secondPartReservation.id,
        order_id: orderId,
      };
    } catch (error: any) {
      console.error("Error splitting booking:", error);
      throw new Error(`Failed to split booking: ${error.message}`);
    }
  }
}

export default RoomInventoryService;
