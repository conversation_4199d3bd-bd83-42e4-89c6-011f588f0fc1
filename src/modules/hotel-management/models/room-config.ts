import { BaseEntity } from "@camped-ai/framework/utils";
import { Entity, Column, ManyToOne, JoinColumn, Index } from "typeorm";
import { Hotel } from "./hotel";

@Entity()
export class RoomConfig extends BaseEntity {
  @Column()
  name: string;

  @Column()
  type: string;

  @Column({ nullable: true })
  description: string;

  @Column({ nullable: true })
  room_size: string;

  @Column({ nullable: true })
  bed_type: string;

  @Column({ default: 0 })
  max_extra_beds: number;

  @Column({ default: 0 })
  max_cots: number;

  @Column({ default: 1 })
  max_adults: number;

  @Column({ default: 0 })
  max_adults_beyond_capacity: number;

  @Column({ default: 0 })
  max_children: number;

  @Column({ default: 0 })
  max_infants: number;

  @Column({ default: 1 })
  max_occupancy: number;

  @Column("simple-array", { nullable: true })
  amenities: string[];

  @Index()
  @Column()
  hotel_id: string;

  @ManyToOne(() => Hotel, { onDelete: "CASCADE" })
  @JoinColumn({ name: "hotel_id" })
  hotel: Hotel;

  @Column({ type: "jsonb", nullable: true })
  metadata: Record<string, any>;
}
