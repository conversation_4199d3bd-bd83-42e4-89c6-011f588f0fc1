import { Migration } from '@mikro-orm/migrations';

export class Migration20250619144141 extends Migration {

  override async up(): Promise<void> {
    // Add new decimal rating column
    this.addSql(`alter table "hotel" add column "rating_decimal" decimal(3,1) null`);
    
    // Copy existing integer ratings to the new decimal column
    this.addSql(`update "hotel" set "rating_decimal" = "rating"`);
    
    // Drop the old integer rating column
    this.addSql(`alter table "hotel" drop column "rating"`);
    
    // Rename the new decimal column to rating
    this.addSql(`alter table "hotel" rename column "rating_decimal" to "rating"`);
  }

  override async down(): Promise<void> {
    // Rename the rating column back to rating_decimal
    this.addSql(`alter table "hotel" rename column "rating" to "rating_decimal"`);
    
    // Add back the old integer rating column
    this.addSql(`alter table "hotel" add column "rating" integer null`);
    
    // Copy values back to the integer column
    this.addSql(`update "hotel" set "rating" = "rating_decimal"`);
    
    // Drop the decimal column
    this.addSql(`alter table "hotel" drop column "rating_decimal"`);
  }

}
