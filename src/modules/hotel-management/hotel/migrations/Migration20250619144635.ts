import { Migration } from '@mikro-orm/migrations';

export class Migration20250619144635 extends Migration {

  override async up(): Promise<void> {
    this.addSql(`alter table if exists "room_config" add column if not exists "max_cots" integer not null default 0, add column if not exists "max_adults_beyond_capacity" integer not null default 0;`);
  }

  override async down(): Promise<void> {
    this.addSql(`alter table if exists "room_config" drop column if exists "max_cots", drop column if exists "max_adults_beyond_capacity";`);
  }

}
