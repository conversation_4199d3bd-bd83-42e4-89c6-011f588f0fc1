import { model } from "@camped-ai/framework/utils";
import { Hotel } from "./hotel";

export const RoomConfig = model.define("room_config", {
  id: model.id({ prefix: "room_cfg" }).primary<PERSON>ey(),
  name: model.text(),
  type: model.text(),
  description: model.text().nullable(),
  room_size: model.text().nullable(),
  amenities: model.array().nullable(),
  bed_type: model.text().nullable(),
  max_extra_beds: model.number().default(0),
  max_cots: model.number().default(0),
  max_adults: model.number().default(1),
  max_adults_beyond_capacity: model.number().default(0),
  max_children: model.number().default(0),
  max_infants: model.number().default(0),
  max_occupancy: model.number().default(1),
  hotel: model.belongsTo(() => Hotel, {
    mappedBy: "roomConfigs",
  }),
});
