import { MedusaError, MedusaService } from "@camped-ai/framework/utils";
import { CancellationPolicy, RefundType } from "./models/cancellation-policy";
import { EntityManager } from "typeorm";

export type CancellationPolicyData = {
  id?: string;
  name: string;
  description?: string;
  hotel_id: string;
  days_before_checkin: number;
  refund_type: RefundType;
  refund_amount: number;
  is_active: boolean;
  metadata?: Record<string, any>;
  created_at?: Date;
  updated_at?: Date;
};

class CancellationPolicyService extends MedusaService({
  CancellationPolicy,
}) {
  protected readonly manager_: EntityManager;
  protected readonly repository_: any;

  constructor(container: any) {
    super(arguments[0]);
    this.manager_ = container.manager;
    this.repository_ = container.cancellationPolicyRepository;

    // Debug log to see what methods are available on the repository
    console.log(
      "Repository methods:",
      Object.getOwnPropertyNames(Object.getPrototypeOf(this.repository_))
    );
  }

  /**
   * Create a new cancellation policy
   */
  async createCancellationPolicy(data: CancellationPolicyData) {
    try {
      // Create the policy using the service's create method
      const policy = await this.createCancellationPolicies({
        id: data.id || `canc_pol_${Date.now()}`, // Use provided ID or generate a new one
        name: data.name,
        description: data.description || null,
        hotel_id: data.hotel_id,
        days_before_checkin: data.days_before_checkin,
        refund_type: data.refund_type,
        refund_amount: data.refund_amount,
        is_active: data.is_active !== undefined ? data.is_active : true,
        metadata: data.metadata,
      });

      return policy;
    } catch (error) {
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to create cancellation policy: ${error.message}`
      );
    }
  }

  /**
   * Update a cancellation policy
   */
  async updateCancellationPolicy(
    id: string,
    data: Partial<CancellationPolicyData>
  ) {
    try {
      console.log("Updating policy with ID:", id);

      // First check if the policy exists using the service method
      const existingPolicy = await this.getCancellationPolicy(id);

      // Prepare update data - only include fields that are provided
      const updateData: any = {};

      if (data.name !== undefined) updateData.name = data.name;
      if (data.description !== undefined)
        updateData.description = data.description;
      if (data.days_before_checkin !== undefined)
        updateData.days_before_checkin = data.days_before_checkin;
      if (data.refund_type !== undefined)
        updateData.refund_type = data.refund_type;
      if (data.refund_amount !== undefined)
        updateData.refund_amount = data.refund_amount;
      if (data.is_active !== undefined) updateData.is_active = data.is_active;
      if (data.metadata !== undefined) updateData.metadata = data.metadata;

      // Add updated_at timestamp
      updateData.updated_at = new Date();

      console.log("Update data:", updateData);

      // Use the service's update method instead of delete-and-recreate
      const updatedPolicy = await this.updateCancellationPolicies([
        {
          id: id,
          ...updateData,
        },
      ]);

      console.log("Update result:", updatedPolicy);

      if (!updatedPolicy || updatedPolicy.length === 0) {
        throw new MedusaError(
          MedusaError.Types.DB_ERROR,
          `Failed to update policy with id ${id}`
        );
      }

      return updatedPolicy[0];
    } catch (error) {
      console.error("Error updating cancellation policy:", error);

      // Re-throw MedusaError as-is, wrap other errors
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to update cancellation policy: ${error.message}`
      );
    }
  }

  /**
   * Get a cancellation policy by ID
   */
  async getCancellationPolicy(id: string) {
    try {
      if (!id) {
        throw new MedusaError(
          MedusaError.Types.INVALID_DATA,
          `Cancellation policy ID is required`
        );
      }

      // Use the service's retrieve method
      const policy = await this.retrieveCancellationPolicy(id);

      return policy;
    } catch (error) {
      console.error(`Error getting cancellation policy ${id}:`, error);

      // Re-throw MedusaError as-is, wrap other errors
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.NOT_FOUND,
        `Cancellation policy with id ${id} not found: ${error.message}`
      );
    }
  }

  /**
   * List cancellation policies with optional filters
   */
  async findCancellationPolicies(
    selector: Partial<CancellationPolicyData> = {},
    config: any = {}
  ) {
    try {
      // Build the query filters
      const filters: any = {};

      // Add filters
      if (selector.hotel_id) {
        filters.hotel_id = selector.hotel_id;
      }

      if (selector.is_active !== undefined) {
        filters.is_active = selector.is_active;
      }

      // Use the service's list method with proper configuration
      const policies = await this.listCancellationPolicies(filters, {
        ...config,
        order: { days_before_checkin: "DESC" },
      });

      return policies;
    } catch (error) {
      console.error("Error listing cancellation policies:", error);
      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to list cancellation policies: ${error.message}`
      );
    }
  }

  /**
   * Delete a cancellation policy
   */
  async deleteCancellationPolicy(id: string) {
    try {
      // First check if the policy exists
      await this.getCancellationPolicy(id);

      // Use the service's delete method
      await this.deleteCancellationPolicies([id]);

      return { id, deleted: true };
    } catch (error) {
      console.error(`Error deleting cancellation policy ${id}:`, error);

      // Re-throw MedusaError as-is, wrap other errors
      if (error instanceof MedusaError) {
        throw error;
      }

      throw new MedusaError(
        MedusaError.Types.DB_ERROR,
        `Failed to delete cancellation policy: ${error.message}`
      );
    }
  }
}

export default CancellationPolicyService;
