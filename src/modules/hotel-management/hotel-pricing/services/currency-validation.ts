import { MedusaError } from "@camped-ai/framework/utils";

/**
 * Currency validation service for hotel pricing
 */
export class CurrencyValidationService {
  
  /**
   * Validate currency code format (ISO 4217)
   */
  static isValidCurrencyFormat(currencyCode: string): boolean {
    return /^[A-Z]{3}$/.test(currencyCode.toUpperCase());
  }

  /**
   * Get supported currencies from store configuration
   */
  static async getSupportedCurrencies(query: any): Promise<string[]> {
    try {
      const { data: stores } = await query.graph({
        entity: "store",
        fields: ["*", "supported_currencies.*"],
      });

      const store = stores?.[0];

      if (!store || !store.supported_currencies?.length) {
        // Return default currencies if store currencies not configured
        return ["USD", "EUR", "GBP"];
      }

      return store.supported_currencies.map((currency: any) =>
        currency.currency_code.toUpperCase()
      );
    } catch (error) {
      console.error("Error fetching supported currencies:", error);
      // Return default currencies on error
      return ["USD", "EUR", "GBP"];
    }
  }

  /**
   * Validate that a currency code is supported by the store
   */
  static async validateCurrencyCode(
    currencyCode: string,
    query: any
  ): Promise<void> {
    // First check format
    if (!this.isValidCurrencyFormat(currencyCode)) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        `Invalid currency code format: ${currencyCode}. Must be a 3-letter ISO 4217 code.`
      );
    }

    // Get supported currencies
    const supportedCurrencies = await this.getSupportedCurrencies(query);
    const normalizedCode = currencyCode.toUpperCase();

    if (!supportedCurrencies.includes(normalizedCode)) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        `Currency ${normalizedCode} is not supported. Supported currencies: ${supportedCurrencies.join(", ")}`
      );
    }
  }

  /**
   * Get default currency from store configuration
   */
  static async getDefaultCurrency(query: any): Promise<string> {
    try {
      const { data: stores } = await query.graph({
        entity: "store",
        fields: ["*", "supported_currencies.*"],
      });

      const store = stores?.[0];

      if (!store || !store.supported_currencies?.length) {
        return "USD"; // Default fallback
      }

      // Find the default currency
      const defaultCurrency = store.supported_currencies.find(
        (currency: any) => currency.is_default
      );

      return defaultCurrency ? defaultCurrency.currency_code.toUpperCase() : "USD";
    } catch (error) {
      console.error("Error fetching default currency:", error);
      return "USD";
    }
  }

  /**
   * Normalize currency code to uppercase
   */
  static normalizeCurrencyCode(currencyCode: string): string {
    return currencyCode.toUpperCase();
  }

  /**
   * Validate and normalize currency code
   */
  static async validateAndNormalizeCurrency(
    currencyCode: string | null | undefined,
    query: any
  ): Promise<string> {
    // If no currency provided, use default
    if (!currencyCode) {
      return await this.getDefaultCurrency(query);
    }

    // Normalize and validate
    const normalizedCode = this.normalizeCurrencyCode(currencyCode);
    await this.validateCurrencyCode(normalizedCode, query);

    return normalizedCode;
  }

  /**
   * Validate currency consistency across pricing rules
   */
  static validateCurrencyConsistency(
    baseCurrency: string,
    ruleCurrencies: (string | null)[]
  ): void {
    const inconsistentCurrencies = ruleCurrencies.filter(
      currency => currency && currency !== baseCurrency
    );

    if (inconsistentCurrencies.length > 0) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        `Currency inconsistency detected. Base currency: ${baseCurrency}, but found: ${inconsistentCurrencies.join(", ")}`
      );
    }
  }

  /**
   * Get decimal places for a currency
   */
  static getCurrencyDecimalPlaces(currencyCode: string): number {
    const zeroDecimalCurrencies = [
      "BIF", "CLP", "DJF", "GNF", "JPY", "KMF", "KRW", "MGA", 
      "PYG", "RWF", "UGX", "VND", "VUV", "XAF", "XOF", "XPF"
    ];
    
    const threeDecimalCurrencies = [
      "BHD", "IQD", "JOD", "KWD", "OMR", "TND"
    ];

    const code = currencyCode.toUpperCase();
    
    if (zeroDecimalCurrencies.includes(code)) {
      return 0;
    } else if (threeDecimalCurrencies.includes(code)) {
      return 3;
    } else {
      return 2; // Default for most currencies
    }
  }

  /**
   * Convert amount to smallest currency unit (e.g., cents for USD)
   */
  static toSmallestUnit(amount: number, currencyCode: string): number {
    const decimalPlaces = this.getCurrencyDecimalPlaces(currencyCode);
    return Math.round(amount * Math.pow(10, decimalPlaces));
  }

  /**
   * Convert amount from smallest currency unit to standard unit
   */
  static fromSmallestUnit(amount: number, currencyCode: string): number {
    const decimalPlaces = this.getCurrencyDecimalPlaces(currencyCode);
    return amount / Math.pow(10, decimalPlaces);
  }
}
