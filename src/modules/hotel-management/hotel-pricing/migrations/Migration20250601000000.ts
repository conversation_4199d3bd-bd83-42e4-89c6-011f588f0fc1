import { Migration } from '@mikro-orm/migrations';

export class Migration20250601000000 extends Migration {

  override async up(): Promise<void> {
    // Create occupancy_config table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "occupancy_config" (
        "id" character varying NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "name" character varying NOT NULL,
        "type" character varying NOT NULL,
        "hotel_id" character varying NOT NULL,
        "min_age" integer NOT NULL,
        "max_age" integer NOT NULL,
        "min_occupancy" integer NOT NULL DEFAULT 1,
        "max_occupancy" integer NOT NULL DEFAULT 1,
        "is_default" boolean NOT NULL DEFAULT false,
        "metadata" jsonb,
        CONSTRAINT "PK_occupancy_config" PRIMARY KEY ("id")
      )
    `);

    // Create base_price_rule table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "base_price_rule" (
        "id" character varying NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "amount" numeric NOT NULL,
        "currency_code" character varying,
        "description" character varying,
        "hotel_id" character varying NOT NULL,
        "room_config_id" character varying NOT NULL,
        "occupancy_type_id" character varying NOT NULL,
        "meal_plan_id" character varying,
        "min_occupancy" integer NOT NULL DEFAULT 1,
        "max_occupancy" integer,
        "monday_price" numeric,
        "tuesday_price" numeric,
        "wednesday_price" numeric,
        "thursday_price" numeric,
        "friday_price" numeric,
        "saturday_price" numeric,
        "sunday_price" numeric,
        "metadata" jsonb,
        CONSTRAINT "PK_base_price_rule" PRIMARY KEY ("id")
      )
    `);

    // Create seasonal_price_rule table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "seasonal_price_rule" (
        "id" character varying NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "start_date" TIMESTAMP WITH TIME ZONE NOT NULL,
        "end_date" TIMESTAMP WITH TIME ZONE NOT NULL,
        "amount" numeric NOT NULL,
        "currency_code" character varying,
        "name" character varying,
        "description" character varying,
        "priority" integer NOT NULL DEFAULT 0,
        "base_price_rule_id" character varying NOT NULL,
        "min_nights" integer,
        "max_nights" integer,
        "day_of_week_constraints" jsonb,
        "metadata" jsonb,
        CONSTRAINT "PK_seasonal_price_rule" PRIMARY KEY ("id")
      )
    `);

    // Create channel_price_override table
    this.addSql(`
      CREATE TABLE IF NOT EXISTS "channel_price_override" (
        "id" character varying NOT NULL,
        "created_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "updated_at" TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
        "deleted_at" TIMESTAMP WITH TIME ZONE,
        "amount" numeric NOT NULL,
        "currency_code" character varying,
        "base_price_rule_id" character varying NOT NULL,
        "sales_channel_id" character varying NOT NULL,
        "metadata" jsonb,
        CONSTRAINT "PK_channel_price_override" PRIMARY KEY ("id")
      )
    `);

    // Create indexes for occupancy_config
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_occupancy_config_hotel_id" ON "occupancy_config" ("hotel_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_occupancy_config_hotel_id_type" ON "occupancy_config" ("hotel_id", "type") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_occupancy_config_hotel_id_age_range" ON "occupancy_config" ("hotel_id", "min_age", "max_age") WHERE deleted_at IS NULL;`);

    // Create indexes for base_price_rule
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_base_price_rule_hotel_id" ON "base_price_rule" ("hotel_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_base_price_rule_room_config_id" ON "base_price_rule" ("room_config_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_base_price_rule_occupancy_type_id" ON "base_price_rule" ("occupancy_type_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_base_price_rule_meal_plan_id" ON "base_price_rule" ("meal_plan_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_base_price_rule_room_config_occupancy_meal_currency" ON "base_price_rule" ("room_config_id", "occupancy_type_id", "meal_plan_id", "currency_code") WHERE deleted_at IS NULL;`);

    // Create indexes for seasonal_price_rule
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_seasonal_price_rule_base_price_rule_id" ON "seasonal_price_rule" ("base_price_rule_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_seasonal_price_rule_date_range" ON "seasonal_price_rule" ("start_date", "end_date") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_seasonal_price_rule_priority" ON "seasonal_price_rule" ("priority") WHERE deleted_at IS NULL;`);

    // Create indexes for channel_price_override
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_channel_price_override_base_price_rule_id" ON "channel_price_override" ("base_price_rule_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE INDEX IF NOT EXISTS "IDX_channel_price_override_sales_channel_id" ON "channel_price_override" ("sales_channel_id") WHERE deleted_at IS NULL;`);
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_channel_price_override_base_price_rule_id_sales_channel_id" ON "channel_price_override" ("base_price_rule_id", "sales_channel_id") WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    // Drop indexes
    this.addSql(`DROP INDEX IF EXISTS "IDX_occupancy_config_hotel_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_occupancy_config_hotel_id_type";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_occupancy_config_hotel_id_age_range";`);

    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_hotel_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_room_config_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_occupancy_type_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_meal_plan_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_room_config_occupancy_meal_currency";`);

    this.addSql(`DROP INDEX IF EXISTS "IDX_seasonal_price_rule_base_price_rule_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_seasonal_price_rule_date_range";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_seasonal_price_rule_priority";`);

    this.addSql(`DROP INDEX IF EXISTS "IDX_channel_price_override_base_price_rule_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_channel_price_override_sales_channel_id";`);
    this.addSql(`DROP INDEX IF EXISTS "IDX_channel_price_override_base_price_rule_id_sales_channel_id";`);

    // Drop tables
    this.addSql(`DROP TABLE IF EXISTS "channel_price_override";`);
    this.addSql(`DROP TABLE IF EXISTS "seasonal_price_rule";`);
    this.addSql(`DROP TABLE IF EXISTS "base_price_rule";`);
    this.addSql(`DROP TABLE IF EXISTS "occupancy_config";`);
  }
}
