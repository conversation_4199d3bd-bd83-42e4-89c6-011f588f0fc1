import { Migration } from '@mikro-orm/migrations';

/**
 * Migration to update base_price_rule unique constraint to include currency_code
 * This allows multiple currencies for the same room/occupancy/meal plan combination
 */
export class Migration20250615000000 extends Migration {
  override async up(): Promise<void> {
    // Drop the old unique index that doesn't include currency_code
    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_room_config_occupancy_meal";`);
    
    // Create new unique index that includes currency_code
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_base_price_rule_room_config_occupancy_meal_currency" ON "base_price_rule" ("room_config_id", "occupancy_type_id", "meal_plan_id", "currency_code") WHERE deleted_at IS NULL;`);
  }

  override async down(): Promise<void> {
    // Drop the new unique index
    this.addSql(`DROP INDEX IF EXISTS "IDX_base_price_rule_room_config_occupancy_meal_currency";`);
    
    // Recreate the old unique index (this might fail if there are duplicate records)
    this.addSql(`CREATE UNIQUE INDEX IF NOT EXISTS "IDX_base_price_rule_room_config_occupancy_meal" ON "base_price_rule" ("room_config_id", "occupancy_type_id", "meal_plan_id") WHERE deleted_at IS NULL;`);
  }
}
