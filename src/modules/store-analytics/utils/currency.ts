const DEFAULT_DECIMAL_DIGITS = 2;

// Cache for currency data from Medusa API
let currencyCache: Record<string, { decimal_digits: number; symbol: string; name: string }> | null = null;

/**
 * Get decimal digits for a currency code from Medusa's currency data
 * Uses cached data or default fallback
 */
export function getDecimalDigits(currencyCode: string): number {
  try {
    const code = currencyCode.toUpperCase();

    if (currencyCache && currencyCache[code]?.decimal_digits !== undefined) {
      return currencyCache[code].decimal_digits;
    }

    // Default fallback when Medusa data not available
    return DEFAULT_DECIMAL_DIGITS;
  } catch {
    return DEFAULT_DECIMAL_DIGITS;
  }
}

/**
 * Update currency cache with data from Medusa's currencies API
 */
export function updateCurrencyCache(currencies: Array<{ code: string; decimal_digits: number; symbol: string; name: string }>) {
  currencyCache = {};
  currencies.forEach(currency => {
    currencyCache![currency.code.toUpperCase()] = {
      decimal_digits: currency.decimal_digits,
      symbol: currency.symbol,
      name: currency.name
    };
  });
}

export function amountToDisplay(amount: number, currencyCode: string) : string {
  const decimalDigits = getDecimalDigits(currencyCode);
  return `${(amount / Math.pow(10, decimalDigits)).toFixed(decimalDigits)} ${currencyCode.toUpperCase()}`;
}