/**
 * <PERSON><PERSON>t to seed default add-on categories
 * Run this script to populate the database with default categories
 */

import { ContainerRegistrationKeys } from "@camped-ai/framework/utils";

const defaultCategories = [
  {
    name: "Transportation",
    description: "Airport transfers, car rentals, and transportation services",
    is_active: true,
  },
  {
    name: "Activities",
    description: "Outdoor activities, sports, and adventure experiences",
    is_active: true,
  },
  {
    name: "Dining",
    description:
      "Restaurant reservations, special meals, and culinary experiences",
    is_active: true,
  },
  {
    name: "Spa & Wellness",
    description:
      "Spa treatments, wellness services, and relaxation experiences",
    is_active: true,
  },
  {
    name: "Tours & Excursions",
    description: "Guided tours, sightseeing, and cultural experiences",
    is_active: true,
  },
  {
    name: "Equipment Rental",
    description: "Ski equipment, sports gear, and other rental items",
    is_active: true,
  },
  {
    name: "Childcare",
    description: "Kids club, babysitting, and family services",
    is_active: true,
  },
  {
    name: "Business Services",
    description: "Meeting rooms, business center, and corporate services",
    is_active: true,
  },
];

export async function seedAddOnCategories(container: any) {
  try {
    console.log("Starting to seed add-on categories...");

    const query = container.resolve(ContainerRegistrationKeys.QUERY);

    // Check if categories already exist
    const existingCategories = await query.graph({
      entity: "lookup",
      filters: {
        entity_name: "add_on_category",
      },
      fields: ["*"],
    });

    if (existingCategories.data && existingCategories.data.length > 0) {
      console.log(
        `Found ${existingCategories.data.length} existing categories. Skipping seed.`
      );
      return;
    }

    // Create default categories
    for (const category of defaultCategories) {
      const categoryData = {
        name: category.name,
        description: category.description,
        is_active: category.is_active,
      };

      await query.graph({
        entity: "lookup",
        data: {
          entity_name: "add_on_category",
          value: JSON.stringify(categoryData),
        },
        fields: ["*"],
      });

      console.log(`Created category: ${category.name}`);
    }

    console.log("Successfully seeded add-on categories!");
  } catch (error) {
    console.error("Error seeding add-on categories:", error);
    throw error;
  }
}

// If running this script directly
if (require.main === module) {
  console.log(
    "This script should be run as part of the application startup or migration process."
  );
  console.log(
    "Import and call seedAddOnCategories(container) from your application."
  );
}
