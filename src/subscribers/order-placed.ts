import type { SubscriberArgs, SubscriberConfig } from "@camped-ai/framework";
import { Modules } from "@camped-ai/framework/utils";
import { NOTIFICATION_TEMPLATE_SERVICE } from "../modules/notification-template/service";
import NotificationTemplateService from "../modules/notification-template/service";
import Handlebars from "handlebars";

export default async function orderPlaceHandler({
  event: { data },
  container,
}: SubscriberArgs<any>) {
  console.log(
    "i am in the o  rder placed email subscriber",
    JSON.stringify(data)
  );

  // Resolve required services
  const notificationModuleService = container.resolve(Modules.NOTIFICATION);
  const notificationTemplateService: NotificationTemplateService =
    container.resolve(NOTIFICATION_TEMPLATE_SERVICE);
  const orderId = data.id;
  const orderModuleService = container.resolve(Modules.ORDER);
  const order = await orderModuleService.retrieveOrder(orderId, {
    relations: ["items"],
  });

  // Set currency symbol based on currency code
  const currencySymbol =
    order.currency_code === "inr" ? "₹" : order.currency_code;

  // Format numeric values for display
  if (order.items && Array.isArray(order.items)) {
    order.items.forEach((item: any) => {
      if (item.unit_price) {
        item.unit_price = parseFloat(Number(item.unit_price).toFixed(2));
      }
      if (item.raw_subtotal && item.raw_subtotal.value) {
        item.raw_subtotal.value = parseFloat(
          Number(item.raw_subtotal.value).toFixed(2)
        );
      }
    });
  }

  // Format order totals if they exist
  if (order.raw_item_subtotal && order.raw_item_subtotal.value) {
    order.raw_item_subtotal.value = parseFloat(
      Number(order.raw_item_subtotal.value).toFixed(2)
    );
  }

  if (order.raw_shipping_total && order.raw_shipping_total.value) {
    order.raw_shipping_total.value = parseFloat(
      Number(order.raw_shipping_total.value).toFixed(2)
    );
  }

  if (order.raw_item_tax_total && order.raw_item_tax_total.value) {
    order.raw_item_tax_total.value = parseFloat(
      Number(order.raw_item_tax_total.value).toFixed(2)
    );
  }

  if (order.raw_total && order.raw_total.value) {
    order.raw_total.value = parseFloat(
      Number(order.raw_total.value).toFixed(2)
    );
  }

  // Check if notifications should be sent for this event and channel
  const shouldSendEmail =
    await notificationTemplateService.shouldSendNotification(
      "order.placed",
      "email"
    );

  if (!shouldSendEmail) {
    console.log(
      `Notifications for order.placed are disabled. Skipping email for order ${orderId}`
    );
    return;
  }

  console.log("=====>1");
  // Get the template from the database
  const emailTemplate = await notificationTemplateService.getTemplate(
    "order.placed",
    "email"
  );

  console.log("=====>2");
  if (!emailTemplate) {
    console.log(
      `No active template found for order.placed. Skipping email for order ${orderId}`
    );
    return;
  }

  console.log("=====>3");
  // Create a booking object with formatted data from order metadata
  const booking: any = {};

  // Extract booking data from order metadata
  if (order.metadata) {
    // Copy all metadata fields to booking object
    Object.assign(booking, order.metadata);

    // Extract room information from order items if available
    if (order.items && order.items.length > 0) {
      const roomItem = order.items[0];

      // Use title from the line item if available
      if (roomItem.title) {
        const titleMatch = roomItem.title.match(/Room booking: (.+)/);
        if (titleMatch && titleMatch[1]) {
          booking.room_type = titleMatch[1];
        }
      }

      // Get additional data from item metadata
      if (roomItem.metadata) {
        if (!booking.check_in_date && roomItem.metadata.check_in_date) {
          booking.check_in_date = roomItem.metadata.check_in_date;
        }
        if (!booking.check_out_date && roomItem.metadata.check_out_date) {
          booking.check_out_date = roomItem.metadata.check_out_date;
        }
        if (!booking.room_config_id && roomItem.metadata.room_config_id) {
          booking.room_config_id = roomItem.metadata.room_config_id;
        }
        if (!booking.room_type && roomItem.metadata.room_config_name) {
          booking.room_type = roomItem.metadata.room_config_name;
        }
        if (!booking.number_of_rooms && roomItem.metadata.number_of_rooms) {
          booking.number_of_rooms = roomItem.metadata.number_of_rooms;
        }
      }

      // Use quantity as number of rooms if not specified
      if (!booking.number_of_rooms && roomItem.quantity) {
        booking.number_of_rooms = roomItem.quantity;
      }
    }
  }

  console.log("Booking data before formatting:", booking);

  // Format dates for better display
  try {
    // Format check-in date if it exists
    if (booking.check_in_date) {
      const checkInDate = new Date(booking.check_in_date);
      booking.formatted_check_in_date = checkInDate.toLocaleDateString(
        "en-US",
        {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        }
      );
    }

    // Format check-out date if it exists
    if (booking.check_out_date) {
      const checkOutDate = new Date(booking.check_out_date);
      booking.formatted_check_out_date = checkOutDate.toLocaleDateString(
        "en-US",
        {
          weekday: "long",
          year: "numeric",
          month: "long",
          day: "numeric",
        }
      );

      // Calculate number of nights if both dates exist
      if (booking.check_in_date) {
        const checkInDate = new Date(booking.check_in_date);
        const nights = Math.round(
          (checkOutDate.getTime() - checkInDate.getTime()) /
            (1000 * 60 * 60 * 24)
        );
        booking.nights = nights;
      }
    }

    // Set default values for missing fields
    if (!booking.check_in_time) booking.check_in_time = "12:00";
    if (!booking.check_out_time) booking.check_out_time = "12:00";
    if (!booking.room_type) booking.room_type = "Standard Room";
    if (!booking.number_of_guests) booking.number_of_guests = 1;
    if (!booking.nights) booking.nights = 1;

    // Use the actual number of rooms from metadata
    if (
      !booking.number_of_rooms &&
      order.metadata &&
      order.metadata.number_of_rooms
    ) {
      booking.number_of_rooms = order.metadata.number_of_rooms;
    }
    // If not in top-level metadata, check line items
    else if (
      !booking.number_of_rooms &&
      order.metadata &&
      order.metadata.line_items &&
      Array.isArray(order.metadata.line_items)
    ) {
      // Look for room booking line items
      const roomItems = order.metadata.line_items.filter(
        (item: any) =>
          item.metadata &&
          (item.metadata.item_type === "room" || item.metadata.number_of_rooms)
      );

      if (roomItems.length > 0 && roomItems[0].metadata?.number_of_rooms) {
        booking.number_of_rooms = roomItems[0].metadata.number_of_rooms;
        console.log(
          `Found number of rooms in line items: ${booking.number_of_rooms}`
        );
      }
    }

    // Try to get hotel name from metadata
    if (!booking.hotel_name && order.metadata && order.metadata.hotel_name) {
      booking.hotel_name = order.metadata.hotel_name;
      console.log(
        `Using hotel name from order metadata: ${booking.hotel_name}`
      );
    }
    // Check line items for hotel_name
    else if (
      !booking.hotel_name &&
      order.items &&
      order.items.length > 0 &&
      order.items[0].metadata?.hotel_name
    ) {
      booking.hotel_name = order.items[0].metadata.hotel_name;
      console.log(
        `Using hotel name from line item metadata: ${booking.hotel_name}`
      );
    }
    // If hotel_name is still not set but we have hotel_id, try to fetch the hotel name
    else if (!booking.hotel_name && order.metadata && order.metadata.hotel_id) {
      try {
        const query = container.resolve("query");
        const { data: hotelData } = await query.graph({
          entity: "hotel",
          filters: {
            id: order.metadata.hotel_id,
          },
          fields: ["id", "name"],
        });

        if (hotelData && hotelData.length > 0) {
          booking.hotel_name =
            hotelData[0].name || String(hotelData[0].id) || "Hotel";
          console.log(`Found hotel name from database: ${booking.hotel_name}`);
        }
      } catch (error) {
        console.error("Error fetching hotel name:", error);
      }
    }
  } catch (error) {
    console.error("Error formatting dates:", error);
  }

  console.log("Booking data after formatting:", booking);

  // Get payment status from order if available
  const paymentStatus = (order as any).payment_status || "awaiting";

  // Extract add-ons from order metadata if they exist
  const addOns = (order.metadata?.add_ons as any[]) || [];
  const metadata = order.metadata || {};

  // Calculate add-ons total amount (convert from cents) - same as invoice
  const addOnTotalAmount = ((metadata as any).add_on_total_amount || 0) / 100;

  // Calculate room-only total (total amount minus add-ons) - same as invoice
  const totalAmount = ((metadata as any).total_amount || 0) + addOnTotalAmount;
  const roomOnlyAmount = totalAmount - addOnTotalAmount;

  // Currency formatter function
  const currencyFormatter = (amount: number, currencyCode: string): string => {
    const value = amount / 100; // Convert from cents
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode.toUpperCase(),
    }).format(value);
  };

  // Helper function to get guest name by index (same as invoice)
  const getGuestNameByIndex = (
    guestType: string,
    guestIndex: number,
    travelers: any
  ): string => {
    if (!travelers) {
      return "Guest";
    }

    // Map guest types to correct plural forms
    const guestTypeMap: { [key: string]: string } = {
      adult: "adults",
      child: "children", // Fix: "child" should map to "children", not "childs"
      infant: "infants",
    };

    const guestListKey = guestTypeMap[guestType] || guestType + "s";
    const guestList = travelers[guestListKey];

    if (!guestList || !Array.isArray(guestList)) {
      return "Guest";
    }

    if (guestIndex >= 0 && guestIndex < guestList.length) {
      const guest = guestList[guestIndex];
      return guest.name || "Guest";
    }

    return "Guest";
  };

  // Create detailed add-ons list for display (same as invoice)
  const addOnsDetailed =
    addOns.length > 0
      ? addOns
          .map((addOn: any) => {
            let addOnDisplay = `<div class="add-on-item"><strong>${
              addOn.name || "Service"
            }</strong>`;

            // Handle different pricing types
            if (
              addOn.pricing_type === "usage_based" &&
              addOn.guest_usage &&
              addOn.guest_usage.length > 0
            ) {
              // Usage-based pricing: show guest name and service dates
              addOn.guest_usage.forEach((usage: any) => {
                const guestName = getGuestNameByIndex(
                  usage.guest_type,
                  usage.guest_index,
                  metadata.travelers
                );
                const dates = usage.usage_dates.join(", ");
                addOnDisplay += `<br>&nbsp;&nbsp;${guestName}<br>&nbsp;&nbsp;&nbsp;&nbsp;<span class="add-on-dates">Dates: ${dates}</span>`;
              });
            } else if (addOn.pricing_type === "per_person") {
              // Per-person pricing: show guest names based on quantities (same as invoice)
              const guests = [];

              // Add adults
              if (addOn.adult_quantity > 0) {
                for (let i = 0; i < addOn.adult_quantity; i++) {
                  const guestName = getGuestNameByIndex(
                    "adult",
                    i,
                    (metadata as any).travelers
                  );
                  guests.push(guestName);
                }
              }

              // Add children
              if (addOn.child_quantity > 0) {
                for (let i = 0; i < addOn.child_quantity; i++) {
                  const guestName = getGuestNameByIndex(
                    "child",
                    i,
                    (metadata as any).travelers
                  );
                  guests.push(guestName);
                }
              }

              guests.forEach((guestName) => {
                addOnDisplay += `<br>&nbsp;&nbsp;${guestName}`;
              });
            } else if (addOn.pricing_type === "package") {
              // Package pricing: show package details
              addOnDisplay += `<br>&nbsp;&nbsp;Package for ${
                addOn.package_quantity || 1
              } guest(s)`;
            }

            addOnDisplay += `</div>`;
            return addOnDisplay;
          })
          .join("")
      : "";

  // Create travelers list for display (same as invoice)
  const travelers = (metadata as any).travelers || {};
  let travelersList = "";

  if (travelers.adults && travelers.adults.length > 0) {
    travelersList += `<div class="guest-item"><strong>Adults:</strong>`;
    travelers.adults.forEach((adult: any) => {
      travelersList += `<br>&nbsp;&nbsp;${adult.name}`;
    });
    travelersList += `</div>`;
  }

  if (travelers.children && travelers.children.length > 0) {
    travelersList += `<div class="guest-item"><strong>Children:</strong>`;
    travelers.children.forEach((child: any) => {
      travelersList += `<br>&nbsp;&nbsp;${child.name} (Age: ${
        child.age || ""
      })`;
    });
    travelersList += `</div>`;
  }

  if (travelers.infants && travelers.infants.length > 0) {
    travelersList += `<div class="guest-item"><strong>Infants:</strong>`;
    travelers.infants.forEach((infant: any) => {
      travelersList += `<br>&nbsp;&nbsp;${infant.name} (Age: ${
        infant.age || ""
      })`;
    });
    travelersList += `</div>`;
  }

  // If no travelers list, create a fallback with primary guest (same as invoice)
  if (!travelersList) {
    const firstName =
      (order as any).customer?.first_name ||
      (metadata as any).guest_name?.split(" ")[0] ||
      "";
    const lastName =
      (order as any).customer?.last_name ||
      (metadata as any).guest_name?.split(" ").slice(1).join(" ") ||
      "";
    travelersList = `<div class="guest-item"><strong>Primary Guest:</strong> ${firstName} ${lastName}</div>`;
  }

  // Format order created date
  const dateFormatter = (date: Date | string): string => {
    const d = new Date(date);
    return d.toLocaleDateString("en-US", {
      year: "numeric",
      month: "long",
      day: "numeric",
    });
  };

  // Fetch hotel details for destination, address, contact info, rules, and cancellation policies
  let hotelDetails = null;
  let cancellationPolicyText = "";
  let propertyRulesText = "";
  let boardBasisLabel = "Bed Only"; // Default fallback

  if ((metadata as any).hotel_id) {
    try {
      const query = container.resolve("query");

      // Fetch hotel details
      const { data: hotels } = await query.graph({
        entity: "hotel",
        filters: { id: (metadata as any).hotel_id },
        fields: [
          "id",
          "name",
          "address",
          "phone_number",
          "email",
          "rules",
          "destination_id",
        ],
      });

      if (hotels && hotels.length > 0) {
        hotelDetails = hotels[0];

        // Format property rules into bullet list
        if (hotelDetails.rules && Array.isArray(hotelDetails.rules)) {
          propertyRulesText =
            "<ul>" +
            hotelDetails.rules
              .map((rule: string) => `<li>${rule}</li>`)
              .join("") +
            "</ul>";
        }

        // Fetch destination name if destination_id exists
        if (hotelDetails.destination_id) {
          try {
            const { data: destinations } = await query.graph({
              entity: "destination",
              filters: { id: hotelDetails.destination_id },
              fields: ["id", "name"],
            });

            if (destinations && destinations.length > 0) {
              hotelDetails.destination_name = destinations[0].name;
            }
          } catch (error) {
            console.error("Error fetching destination details:", error);
          }
        }
      }

      // Fetch cancellation policies
      const { data: cancellationPolicies } = await query.graph({
        entity: "cancellation_policy",
        filters: {
          hotel_id: (metadata as any).hotel_id,
          is_active: true,
        },
        fields: [
          "name",
          "description",
          "days_before_checkin",
          "refund_type",
          "refund_amount",
        ],
      });

      if (cancellationPolicies && cancellationPolicies.length > 0) {
        // Format cancellation policies into bullet list
        cancellationPolicyText =
          "<ul>" +
          cancellationPolicies
            .map((policy: any) => {
              let policyText = policy.description || policy.name;
              if (policy.days_before_checkin > 0) {
                policyText += ` (${policy.days_before_checkin} days before check-in)`;
              }
              if (
                policy.refund_type === "PERCENTAGE" &&
                policy.refund_amount > 0
              ) {
                policyText += ` - ${policy.refund_amount}% refund`;
              } else if (policy.refund_type === "NO_REFUND") {
                policyText += ` - No refund`;
              }
              return `<li>${policyText}</li>`;
            })
            .join("") +
          "</ul>";
      }
    } catch (error) {
      console.error("Error fetching hotel details:", error);
    }
  }

  // Fetch meal plan label for board basis
  if (
    (metadata as any).hotel_id &&
    ((order as any).meal_plan || (metadata as any).meal_plan)
  ) {
    try {
      const hotelPricingService = container.resolve("hotelPricingService");
      const mealPlanType =
        (order as any).meal_plan || (metadata as any).meal_plan;

      console.log(
        `Fetching meal plan label for hotel ${
          (metadata as any).hotel_id
        } and type ${mealPlanType}`
      );

      // Get meal plans for this hotel with the specific type
      const mealPlans = await hotelPricingService.listMealPlans({
        hotel_id: (metadata as any).hotel_id,
        type: mealPlanType,
      });

      if (mealPlans && mealPlans.length > 0) {
        boardBasisLabel = mealPlans[0].name;
        console.log(`Found meal plan label: ${boardBasisLabel}`);
      } else {
        console.log(
          `No meal plan found for type ${mealPlanType}, using default: ${boardBasisLabel}`
        );
      }
    } catch (error) {
      console.error("Error fetching meal plan label:", error);
      // Keep the default fallback value
    }
  }

  // Calculate formatted amounts with proper formatting (using roomOnlyAmount from above)
  const formattedRoomTotal = currencyFormatter(
    roomOnlyAmount * 100,
    order.currency_code
  );
  const formattedAddOnTotal =
    addOnTotalAmount > 0
      ? currencyFormatter(addOnTotalAmount * 100, order.currency_code)
      : null;
  const formattedGrandTotal = currencyFormatter(
    totalAmount * 100,
    order.currency_code
  );

  // Calculate number of nights
  const checkInDate = new Date((metadata as any).check_in_date);
  const checkOutDate = new Date((metadata as any).check_out_date);
  const numberOfNights = Math.ceil(
    (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
  );

  // Create special requests section if available
  const hasSpecialRequests = !!(
    (metadata as any).special_requests &&
    (metadata as any).special_requests.trim() &&
    (metadata as any).special_requests.trim() !== "None"
  );

  const specialRequestsSection = hasSpecialRequests
    ? `<div class="section">
        <h3>
          <svg class="section-icon" viewBox="0 0 24 24">
            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-3 15l-3-3 1.41-1.41L11 14.17l4.59-4.58L17 11l-6 6z"/>
          </svg>
          Special Requests
        </h3>
        <div class="section-item">
          <span class="section-value">${
            (metadata as any).special_requests
          }</span>
        </div>
      </div>`
    : "";

  // Create add-ons pricing breakdown with hierarchical structure (same as invoice)
  const addOnsPricing =
    addOns.length > 0
      ? addOns
          .map((addOn: any) => {
            let rows = "";

            if (addOn.pricing_type === "per_person") {
              // Main add-on row
              rows += `<tr class="addon-main has-subitems">
                <td>${addOn.name || "Service"}</td>
                <td></td>
                <td></td>
                <td>${currencyFormatter(
                  (addOn.total_price || 0) * 100,
                  order.currency_code
                )}</td>
              </tr>`;

              // Adult sub-rows
              if (addOn.adult_quantity > 0) {
                const isLastSubItem = addOn.child_quantity === 0; // Last if no children
                rows += `<tr class="addon-sub${
                  isLastSubItem ? " addon-sub-last" : ""
                }">
                  <td>Adult</td>
                  <td>${addOn.adult_quantity}</td>
                  <td>${currencyFormatter(
                    (addOn.adult_price || 0) * 100,
                    order.currency_code
                  )}</td>
                  <td>${currencyFormatter(
                    (addOn.adult_price || 0) * addOn.adult_quantity * 100,
                    order.currency_code
                  )}</td>
                </tr>`;
              }

              // Child sub-rows
              if (addOn.child_quantity > 0) {
                rows += `<tr class="addon-sub addon-sub-last">
                  <td>Child</td>
                  <td>${addOn.child_quantity}</td>
                  <td>${currencyFormatter(
                    (addOn.child_price || 0) * 100,
                    order.currency_code
                  )}</td>
                  <td>${currencyFormatter(
                    (addOn.child_price || 0) * addOn.child_quantity * 100,
                    order.currency_code
                  )}</td>
                </tr>`;
              }
            } else if (addOn.pricing_type === "usage_based") {
              // Main add-on row
              rows += `<tr class="addon-main has-subitems">
                <td>${addOn.name || "Service"}</td>
                <td></td>
                <td></td>
                <td>${currencyFormatter(
                  (addOn.total_price || 0) * 100,
                  order.currency_code
                )}</td>
              </tr>`;

              // Calculate days from guest_usage
              if (addOn.guest_usage && addOn.guest_usage.length > 0) {
                const adultUsage = addOn.guest_usage.filter(
                  (usage: any) => usage.guest_type === "adult"
                );
                const childUsage = addOn.guest_usage.filter(
                  (usage: any) => usage.guest_type === "child"
                );

                if (adultUsage.length > 0) {
                  const totalAdultDays = adultUsage.reduce(
                    (sum: number, usage: any) =>
                      sum + (usage.usage_dates ? usage.usage_dates.length : 0),
                    0
                  );

                  // Create detailed breakdown with guest names and days
                  const adultBreakdown = adultUsage
                    .map((usage: any) => {
                      const guestName = getGuestNameByIndex(
                        usage.guest_type,
                        usage.guest_index,
                        (metadata as any).travelers
                      );
                      const days = usage.usage_dates
                        ? usage.usage_dates.length
                        : 0;
                      const dayText = days === 1 ? "day" : "days";
                      return `${days} ${dayText} × ${guestName}`;
                    })
                    .join(" + ");

                  // Calculate total quantity as people × days
                  const totalAdultQuantity =
                    adultUsage.length * (totalAdultDays / adultUsage.length);
                  const isLastSubItem = childUsage.length === 0; // Last if no children
                  rows += `<tr class="addon-sub${
                    isLastSubItem ? " addon-sub-last" : ""
                  }">
                    <td>Adult<br><span style="font-size: 0.85em; color: #888;">${adultBreakdown}</span></td>
                    <td>${totalAdultQuantity}</td>
                    <td>${currencyFormatter(
                      (addOn.per_day_adult_price || 0) * 100,
                      order.currency_code
                    )}</td>
                    <td>${currencyFormatter(
                      (addOn.per_day_adult_price || 0) *
                        totalAdultQuantity *
                        100,
                      order.currency_code
                    )}</td>
                  </tr>`;
                }

                if (childUsage.length > 0) {
                  const totalChildDays = childUsage.reduce(
                    (sum: number, usage: any) =>
                      sum + (usage.usage_dates ? usage.usage_dates.length : 0),
                    0
                  );

                  // Create detailed breakdown with guest names and days
                  const childBreakdown = childUsage
                    .map((usage: any) => {
                      const guestName = getGuestNameByIndex(
                        usage.guest_type,
                        usage.guest_index,
                        (metadata as any).travelers
                      );
                      const days = usage.usage_dates
                        ? usage.usage_dates.length
                        : 0;
                      const dayText = days === 1 ? "day" : "days";
                      return `${days} ${dayText} × ${guestName}`;
                    })
                    .join(" + ");

                  // Calculate total quantity as people × days
                  const totalChildQuantity =
                    childUsage.length * (totalChildDays / childUsage.length);
                  rows += `<tr class="addon-sub addon-sub-last">
                    <td>Child<br><span style="font-size: 0.85em; color: #888;">${childBreakdown}</span></td>
                    <td>${totalChildQuantity}</td>
                    <td>${currencyFormatter(
                      (addOn.per_day_child_price || 0) * 100,
                      order.currency_code
                    )}</td>
                    <td>${currencyFormatter(
                      (addOn.per_day_child_price || 0) *
                        totalChildQuantity *
                        100,
                      order.currency_code
                    )}</td>
                  </tr>`;
                }
              }
            } else if (addOn.pricing_type === "package") {
              // Package pricing - single row
              rows += `<tr class="addon-main">
                <td>${addOn.name || "Service"}</td>
                <td>1</td>
                <td>${currencyFormatter(
                  (addOn.total_price || 0) * 100,
                  order.currency_code
                )}</td>
                <td>${currencyFormatter(
                  (addOn.total_price || 0) * 100,
                  order.currency_code
                )}</td>
              </tr>`;
            } else {
              // Fallback for unknown pricing types
              rows += `<tr class="addon-main">
                <td>${addOn.name || "Service"}</td>
                <td>1</td>
                <td>${currencyFormatter(
                  (addOn.total_price || 0) * 100,
                  order.currency_code
                )}</td>
                <td>${currencyFormatter(
                  (addOn.total_price || 0) * 100,
                  order.currency_code
                )}</td>
              </tr>`;
            }

            return rows;
          })
          .join("")
      : "";

  // Prepare data for template rendering (same structure as invoice)
  const templateData = {
    order: {
      ...order,
      created_at_formatted: dateFormatter(order.created_at),
      tax_total_formatted: order.tax_total
        ? currencyFormatter(Number(order.tax_total), order.currency_code)
        : null,
    },
    customer: {
      email: (order as any).customer?.email || (order as any).email || "N/A",
      guest_phone: (metadata as any).guest_phone || "N/A",
      first_name:
        (order as any).customer?.first_name ||
        (metadata as any).guest_name?.split(" ")[0] ||
        "",
      last_name:
        (order as any).customer?.last_name ||
        (metadata as any).guest_name?.split(" ").slice(1).join(" ") ||
        "",
    },
    currencySymbol,
    frontendURL: process.env.MEDUSA_STOREFRONT_URL || "",
    booking: {
      ...booking,
      total: formattedGrandTotal,
      room_total_amount: formattedRoomTotal,
      add_on_total_amount: formattedAddOnTotal,
      board_basis: boardBasisLabel,
      hotel_name:
        hotelDetails?.name ||
        (metadata as any).hotel_name ||
        booking.hotel_name ||
        "Hotel",
      destination_name:
        hotelDetails?.destination_name ||
        (metadata as any).destination_name ||
        "Destination",
      room_config_name:
        (metadata as any).room_config_name ||
        (metadata as any).room_type ||
        "Standard Room",
      check_in_date: (metadata as any).check_in_date
        ? dateFormatter((metadata as any).check_in_date)
        : "N/A",
      check_out_date: (metadata as any).check_out_date
        ? dateFormatter((metadata as any).check_out_date)
        : "N/A",
      check_in_time: (metadata as any).check_in_time || "12:00",
      check_out_time: (metadata as any).check_out_time || "12:00",
      number_of_guests: (metadata as any).number_of_guests || 1,
      number_of_nights: numberOfNights,
      special_requests: (metadata as any).special_requests || "",
      has_special_requests: !!(
        (metadata as any).special_requests &&
        (metadata as any).special_requests.trim() &&
        (metadata as any).special_requests.trim() !== "None"
      ),
      hotel_address:
        hotelDetails?.address ||
        (metadata as any).hotel_address ||
        "Address not available",
      hotel_phone:
        hotelDetails?.phone_number ||
        (metadata as any).hotel_phone ||
        "Contact not available",
      hotel_email:
        hotelDetails?.email ||
        (metadata as any).hotel_email ||
        "Email not available",
      total_amount: currencyFormatter(
        (metadata as any).total_amount * 100 || 0,
        order.currency_code
      ),
    },
    payment_status: paymentStatus,
    add_ons_detailed: addOnsDetailed,
    add_ons_pricing: addOnsPricing,
    travelers_list: travelersList,
    special_requests_section: specialRequestsSection,
    cancellation_policy_text: cancellationPolicyText,
    property_rules_text: propertyRulesText,
    items_html_block: `<tr class="room-main">
      <td>${(metadata as any).room_config_name || "Hotel Booking"}</td>
      <td>${numberOfNights}</td>
      <td>${currencyFormatter(
        (roomOnlyAmount / numberOfNights) * 100 || 0,
        order.currency_code
      )}</td>
      <td>${currencyFormatter(
        roomOnlyAmount * 100 || 0,
        order.currency_code
      )}</td>
    </tr>`,
  };

  console.log("=====>4");
  // Compile and render the template with Handlebars
  const compiledTemplate = Handlebars.compile(emailTemplate.content);
  const emailBody = compiledTemplate(templateData);

  console.log({ orderEmail: order.email });

  // Send email
  try {
    await notificationModuleService.createNotifications({
      to: order.email,
      channel: "email",
      template: "order.placed",
      data: {
        subject: emailTemplate.subject || "Order Confirmation",
        html: emailBody,
      },
    });
    console.log(`Email sent successfully for order ${orderId}`);
  } catch (error) {
    console.error("Error sending email:", error);
  }
}

export const config: SubscriberConfig = {
  event: "order.placed",
};
