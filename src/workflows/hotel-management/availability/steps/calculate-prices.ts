import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { HOTEL_PRICING_MODULE } from "../../../../modules/hotel-management/hotel-pricing";

// Child age information
type ChildInfo = {
  age: number | string;
};

// Helper function to get the day of week from a date (0 = Sunday, 1 = Monday, etc.)
function getDayOfWeek(date: Date): number {
  return date.getDay();
}

// Helper function to get the weekday key for a given date
function getWeekdayKey(date: Date): string {
  const dayMap = [
    "sunday",
    "monday",
    "tuesday",
    "wednesday",
    "thursday",
    "friday",
    "saturday",
  ];
  return dayMap[getDayOfWeek(date)];
}

// Helper function to get seasonal override for a base price rule within date range
async function getSeasonalOverride(
  hotelPricingService: any,
  basePriceRuleId: string,
  checkInDate: string,
  checkOutDate: string
) {
  try {
    console.log(
      `Checking for seasonal overrides for base price rule: ${basePriceRuleId}`
    );
    console.log(`Date range: ${checkInDate} to ${checkOutDate}`);

    // Get all seasonal overrides for this base price rule
    const seasonalOverrides = await hotelPricingService.listSeasonalPriceRules({
      base_price_rule_id: basePriceRuleId,
    });

    if (!seasonalOverrides || seasonalOverrides.length === 0) {
      return null;
    }

    const bookingStart = new Date(checkInDate);
    const bookingEnd = new Date(checkOutDate);

    // Filter overrides that overlap with the booking dates and are active
    const applicableOverrides = seasonalOverrides.filter((override) => {
      const overrideStart = new Date(override.start_date);
      const overrideEnd = new Date(override.end_date);

      // Check if the booking dates overlap with the seasonal override dates
      const hasOverlap =
        bookingStart <= overrideEnd && bookingEnd >= overrideStart;

      // Check if the override is active (from metadata)
      const isActive = override.metadata?.is_active !== false;

      return hasOverlap && isActive;
    });

    if (applicableOverrides.length === 0) {
      return null;
    }

    // Sort by priority (highest first) and return the highest priority override
    applicableOverrides.sort((a, b) => (b.priority || 0) - (a.priority || 0));

    // Using highest priority seasonal override

    return applicableOverrides[0];
  } catch (error) {
    console.error(
      `Error getting seasonal override for base price rule ${basePriceRuleId}:`,
      error
    );
    return null;
  }
}

// Helper function to get price for a specific day, considering seasonal overrides
function getDayPrice(
  basePriceRule: any,
  seasonalOverride: any,
  weekdayKey: string
): number {
  // If we have a seasonal override, use its weekday prices
  if (seasonalOverride && seasonalOverride.metadata?.weekday_prices) {
    // Convert full weekday name to short form for seasonal override lookup
    const shortWeekdayKey = weekdayKey.substring(0, 3); // "sunday" -> "sun"
    const seasonalPrice =
      seasonalOverride.metadata.weekday_prices[shortWeekdayKey];
    if (seasonalPrice !== null && seasonalPrice !== undefined) {
      return Number(seasonalPrice);
    }
  }

  // Fall back to base price rule weekday pricing
  let dayPrice = Number(basePriceRule.amount || 0);
  const dayPriceKey = `${weekdayKey}_price`;
  if (
    basePriceRule[dayPriceKey] !== null &&
    basePriceRule[dayPriceKey] !== undefined
  ) {
    dayPrice = Number(basePriceRule[dayPriceKey] || 0);
  }

  return dayPrice;
}

type CalculatePricesInput = {
  hotel: any;
  availability_results: any[];
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  infants: number;

  child_ages?: ChildInfo[]; // Array of child ages
  currency_code: string;
  nights: number;
  sales_channel_id?: string; // Optional sales channel ID for channel-specific pricing
};

export const calculatePricesStep = createStep(
  "calculate-prices",
  async (input: CalculatePricesInput, { container }) => {
    const {
      hotel,
      availability_results,
      currency_code,
      nights,
      adults = 1,
      children = 0,
      infants = 0,
      sales_channel_id,
      check_in_date,
      check_out_date,
    } = input;

    let formattedCheckInDate: string;
    let formattedCheckOutDate: string;

    try {
      formattedCheckInDate =
        check_in_date instanceof Date
          ? check_in_date.toISOString().split("T")[0]
          : typeof check_in_date === "string"
          ? check_in_date
          : new Date(check_in_date).toISOString().split("T")[0];
    } catch (error) {
      console.error("Error formatting check-in date:", error);
      // Fallback to current date
      formattedCheckInDate = new Date().toISOString().split("T")[0];
    }

    try {
      formattedCheckOutDate =
        check_out_date instanceof Date
          ? check_out_date.toISOString().split("T")[0]
          : typeof check_out_date === "string"
          ? check_out_date
          : new Date(check_out_date).toISOString().split("T")[0];
    } catch (error) {
      console.error("Error formatting check-out date:", error);
      // Fallback to tomorrow's date
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      formattedCheckOutDate = tomorrow.toISOString().split("T")[0];
    }

    // Removed verbose date logging

    // Calculate prices for each available room
    const pricedResults = [];

    for (const config of availability_results) {
      const pricedVariants = [];

      // Calculate prices for each available variant
      for (const variant of config.available_variants) {
        try {
          // Base context for price calculation
          const baseContext = {
            currency_code: currency_code,
            check_in_date: formattedCheckInDate,
            check_out_date: formattedCheckOutDate,
            sales_channel_id: sales_channel_id,
          };

          // Get the hotel pricing service for day-wise pricing and child age calculations
          const hotelPricingService = container.resolve(HOTEL_PRICING_MODULE);

          // Initialize price variables with explicit zero values
          let totalPrice = 0;
          let pricePerNight = 0;
          let basePriceRules: any[];

          try {
            // Get base price rules from the hotel pricing service
            console.log(
              `Getting base price rules for room config ${config.id}`
            );

            // Get base price rules for this room configuration filtered by currency
            basePriceRules = await hotelPricingService.listBasePriceRules({
              room_config_id: config.id,
              currency_code: currency_code,
            });

            console.log(
              `Found ${basePriceRules.length} base price rules for room config ${config.id} in ${currency_code}`
            );

            // Removed verbose base price rule logging

            if (basePriceRules.length === 0) {
              throw new Error(
                `No pricing rules found for room ${config.id} in ${currency_code}. Please configure ${currency_code} pricing in the admin interface.`
              );
            }

            // Get all occupancy configurations for this hotel
            const occupancyConfigs =
              await hotelPricingService.listOccupancyConfigs({
                hotel_id: hotel.id,
              });

            // Removed verbose config logging

            // Calculate pricing per guest type instead of using a single base rule
            let totalGuestPrice = 0;
            let priceBreakdown = [];

            // Calculate adult pricing with proper base + extra logic
            if (adults > 0) {
              console.log(`Calculating pricing for ${adults} adults`);

              let adultTotalPrice = 0;
              let adultsProcessed = 0;

              // First, try to use BASE_2 for 2 adults if we have 2 or more adults
              if (adults >= 2) {
                const base2Config = occupancyConfigs.find(
                  (config) => config.type === "BASE_2"
                );

                if (base2Config) {
                  const base2PriceRule = basePriceRules.find(
                    (rule) => rule.occupancy_type_id === base2Config.id
                  );

                  if (base2PriceRule) {
                    console.log(`Using BASE_2 pricing for 2 adults`);

                    // Check for seasonal overrides for BASE_2
                    const base2SeasonalOverride = await getSeasonalOverride(
                      hotelPricingService,
                      base2PriceRule.id,
                      formattedCheckInDate,
                      formattedCheckOutDate
                    );

                    // Calculate BASE_2 pricing for 2 adults
                    const checkInDate = new Date(formattedCheckInDate);
                    const checkOutDate = new Date(formattedCheckOutDate);
                    const currentDate = new Date(checkInDate);

                    while (currentDate < checkOutDate) {
                      const weekdayKey = getWeekdayKey(currentDate);
                      const dayPrice = getDayPrice(
                        base2PriceRule,
                        base2SeasonalOverride,
                        weekdayKey
                      );

                      adultTotalPrice += dayPrice;
                      console.log(
                        `BASE_2 day ${weekdayKey}: ${dayPrice}${
                          base2SeasonalOverride ? " (seasonal)" : ""
                        }`
                      );
                      currentDate.setDate(currentDate.getDate() + 1);
                    }

                    adultsProcessed = 2;
                    priceBreakdown.push({
                      type: "adult_base2",
                      count: 2,
                      total: adultTotalPrice,
                      description: `2 adults (BASE_2)`,
                    });
                  }
                }
              }

              // If we couldn't use BASE_2, try BASE_1 for 1 adult
              if (adultsProcessed === 0 && adults >= 1) {
                const base1Config = occupancyConfigs.find(
                  (config) => config.type === "BASE_1"
                );

                if (base1Config) {
                  const base1PriceRule = basePriceRules.find(
                    (rule) => rule.occupancy_type_id === base1Config.id
                  );

                  if (base1PriceRule) {
                    console.log(`Using BASE_1 pricing for 1 adult`);

                    // Check for seasonal overrides for BASE_1
                    const base1SeasonalOverride = await getSeasonalOverride(
                      hotelPricingService,
                      base1PriceRule.id,
                      formattedCheckInDate,
                      formattedCheckOutDate
                    );

                    // Calculate BASE_1 pricing for 1 adult
                    const checkInDate = new Date(formattedCheckInDate);
                    const checkOutDate = new Date(formattedCheckOutDate);
                    const currentDate = new Date(checkInDate);

                    while (currentDate < checkOutDate) {
                      const weekdayKey = getWeekdayKey(currentDate);
                      const dayPrice = getDayPrice(
                        base1PriceRule,
                        base1SeasonalOverride,
                        weekdayKey
                      );

                      adultTotalPrice += dayPrice;
                      console.log(
                        `BASE_1 day ${weekdayKey}: ${dayPrice}${
                          base1SeasonalOverride ? " (seasonal)" : ""
                        }`
                      );
                      currentDate.setDate(currentDate.getDate() + 1);
                    }

                    adultsProcessed = 1;
                    priceBreakdown.push({
                      type: "adult_base1",
                      count: 1,
                      total: adultTotalPrice,
                      description: `1 adult (BASE_1)`,
                    });
                  }
                } else {
                  // If BASE_1 is not available, check if EXTRA_ADULT is available
                  // and use it for all adults
                  console.log(
                    `BASE_1 not found, checking for EXTRA_ADULT for all ${adults} adults`
                  );
                  const extraAdultConfig = occupancyConfigs.find(
                    (config) =>
                      config.type === "EXTRA_ADULT" ||
                      config.name.toLowerCase().includes("adult")
                  );

                  if (extraAdultConfig) {
                    const extraAdultPriceRule = basePriceRules.find(
                      (rule) => rule.occupancy_type_id === extraAdultConfig.id
                    );

                    if (extraAdultPriceRule) {
                      console.log(
                        `Using EXTRA_ADULT pricing for all ${adults} adults (no BASE_1 available)`
                      );

                      // Check for seasonal overrides for EXTRA_ADULT
                      const extraAdultSeasonalOverride =
                        await getSeasonalOverride(
                          hotelPricingService,
                          extraAdultPriceRule.id,
                          formattedCheckInDate,
                          formattedCheckOutDate
                        );

                      const checkInDate = new Date(formattedCheckInDate);
                      const checkOutDate = new Date(formattedCheckOutDate);
                      const currentDate = new Date(checkInDate);

                      while (currentDate < checkOutDate) {
                        const weekdayKey = getWeekdayKey(currentDate);
                        const dayPrice = getDayPrice(
                          extraAdultPriceRule,
                          extraAdultSeasonalOverride,
                          weekdayKey
                        );

                        adultTotalPrice += dayPrice * adults;
                        console.log(
                          `EXTRA_ADULT (no BASE_1) day ${weekdayKey}: ${dayPrice} × ${adults} = ${
                            dayPrice * adults
                          }${extraAdultSeasonalOverride ? " (seasonal)" : ""}`
                        );
                        currentDate.setDate(currentDate.getDate() + 1);
                      }

                      adultsProcessed = adults; // Mark all adults as processed
                      priceBreakdown.push({
                        type: "adult_extra_all",
                        count: adults,
                        total: adultTotalPrice,
                        description: `${adults} adult(s) (EXTRA_ADULT only)`,
                      });
                    }
                  }
                }
              }

              // Handle remaining adults with capacity-aware pricing
              const remainingAdults = adults - adultsProcessed;
              if (remainingAdults > 0) {
                // Get room capacity information
                const maxAdults = Number(config.metadata?.max_adults) || 1;
                const maxAdultsBeyondCapacity =
                  Number(config.metadata?.max_adults_beyond_capacity) || 0;

                // Calculate how many adults are within capacity vs beyond capacity
                const totalProcessedSoFar = adultsProcessed;
                const adultsWithinCapacity = Math.max(
                  0,
                  Math.min(remainingAdults, maxAdults - totalProcessedSoFar)
                );
                const adultsBeyondCapacity = Math.max(
                  0,
                  remainingAdults - adultsWithinCapacity
                );

                console.log(
                  `Capacity-aware adult pricing: ${remainingAdults} remaining adults`
                );
                console.log(
                  `- Max adults: ${maxAdults}, Max beyond capacity: ${maxAdultsBeyondCapacity}`
                );
                console.log(`- Total processed so far: ${totalProcessedSoFar}`);
                console.log(`- Within capacity: ${adultsWithinCapacity}`);
                console.log(`- Beyond capacity: ${adultsBeyondCapacity}`);

                // Handle adults within capacity using EXTRA_ADULT pricing
                if (adultsWithinCapacity > 0) {
                  const extraAdultConfig = occupancyConfigs.find(
                    (config) =>
                      config.type === "EXTRA_ADULT" ||
                      config.name.toLowerCase().includes("adult")
                  );

                  if (extraAdultConfig) {
                    const extraAdultPriceRule = basePriceRules.find(
                      (rule) => rule.occupancy_type_id === extraAdultConfig.id
                    );

                    if (extraAdultPriceRule) {
                      console.log(
                        `Using EXTRA_ADULT pricing for ${adultsWithinCapacity} adults within capacity`
                      );

                      // Check for seasonal overrides for EXTRA_ADULT
                      const extraAdultSeasonalOverride =
                        await getSeasonalOverride(
                          hotelPricingService,
                          extraAdultPriceRule.id,
                          formattedCheckInDate,
                          formattedCheckOutDate
                        );

                      let extraAdultPrice = 0;
                      const checkInDate = new Date(formattedCheckInDate);
                      const checkOutDate = new Date(formattedCheckOutDate);
                      const currentDate = new Date(checkInDate);

                      while (currentDate < checkOutDate) {
                        const weekdayKey = getWeekdayKey(currentDate);
                        const dayPrice = getDayPrice(
                          extraAdultPriceRule,
                          extraAdultSeasonalOverride,
                          weekdayKey
                        );

                        extraAdultPrice += dayPrice * adultsWithinCapacity;
                        console.log(
                          `EXTRA_ADULT (within capacity) day ${weekdayKey}: ${dayPrice} × ${adultsWithinCapacity} = ${
                            dayPrice * adultsWithinCapacity
                          }${extraAdultSeasonalOverride ? " (seasonal)" : ""}`
                        );
                        currentDate.setDate(currentDate.getDate() + 1);
                      }

                      adultTotalPrice += extraAdultPrice;
                      priceBreakdown.push({
                        type: "adult_extra_within_capacity",
                        count: adultsWithinCapacity,
                        total: extraAdultPrice,
                        description: `${adultsWithinCapacity} extra adult(s) within capacity`,
                      });
                    } else {
                      console.log(
                        `No price rule found for EXTRA_ADULT config ${extraAdultConfig.id}`
                      );
                    }
                  } else {
                    console.log(
                      `No EXTRA_ADULT config found for adults within capacity`
                    );
                  }
                }

                // Handle adults beyond capacity using EXTRA_ADULT_BEYOND_CAPACITY pricing
                if (adultsBeyondCapacity > 0) {
                  console.log(
                    `Processing ${adultsBeyondCapacity} adults beyond capacity (max_adults_beyond_capacity: ${maxAdultsBeyondCapacity})`
                  );

                  // Check if the number of adults beyond capacity exceeds the allowed limit
                  if (adultsBeyondCapacity > maxAdultsBeyondCapacity) {
                    console.log(
                      `WARNING: ${adultsBeyondCapacity} adults beyond capacity exceeds the maximum allowed (${maxAdultsBeyondCapacity})`
                    );
                    // For now, we'll still process them but this could be a validation error
                  }

                  const extraAdultBeyondCapacityConfig = occupancyConfigs.find(
                    (config) => config.type === "EXTRA_ADULT_BEYOND_CAPACITY"
                  );

                  if (extraAdultBeyondCapacityConfig) {
                    console.log(
                      `Found EXTRA_ADULT_BEYOND_CAPACITY config: ${extraAdultBeyondCapacityConfig.id}`
                    );

                    const extraAdultBeyondCapacityPriceRule =
                      basePriceRules.find(
                        (rule) =>
                          rule.occupancy_type_id ===
                          extraAdultBeyondCapacityConfig.id
                      );

                    if (extraAdultBeyondCapacityPriceRule) {
                      console.log(
                        `Using EXTRA_ADULT_BEYOND_CAPACITY pricing for ${adultsBeyondCapacity} adults beyond capacity`
                      );

                      // Check for seasonal overrides for EXTRA_ADULT_BEYOND_CAPACITY
                      const extraAdultBeyondCapacitySeasonalOverride =
                        await getSeasonalOverride(
                          hotelPricingService,
                          extraAdultBeyondCapacityPriceRule.id,
                          formattedCheckInDate,
                          formattedCheckOutDate
                        );

                      let extraAdultBeyondCapacityPrice = 0;
                      const checkInDate = new Date(formattedCheckInDate);
                      const checkOutDate = new Date(formattedCheckOutDate);
                      const currentDate = new Date(checkInDate);

                      while (currentDate < checkOutDate) {
                        const weekdayKey = getWeekdayKey(currentDate);
                        const dayPrice = getDayPrice(
                          extraAdultBeyondCapacityPriceRule,
                          extraAdultBeyondCapacitySeasonalOverride,
                          weekdayKey
                        );

                        extraAdultBeyondCapacityPrice +=
                          dayPrice * adultsBeyondCapacity;
                        console.log(
                          `EXTRA_ADULT_BEYOND_CAPACITY day ${weekdayKey}: ${dayPrice} × ${adultsBeyondCapacity} = ${
                            dayPrice * adultsBeyondCapacity
                          }${
                            extraAdultBeyondCapacitySeasonalOverride
                              ? " (seasonal)"
                              : ""
                          }`
                        );
                        currentDate.setDate(currentDate.getDate() + 1);
                      }

                      adultTotalPrice += extraAdultBeyondCapacityPrice;
                      priceBreakdown.push({
                        type: "adult_extra_beyond_capacity",
                        count: adultsBeyondCapacity,
                        total: extraAdultBeyondCapacityPrice,
                        description: `${adultsBeyondCapacity} extra adult(s) beyond capacity`,
                      });
                    } else {
                      console.log(
                        `No price rule found for EXTRA_ADULT_BEYOND_CAPACITY config ${extraAdultBeyondCapacityConfig.id}, falling back to EXTRA_ADULT pricing`
                      );
                      // Fallback to regular EXTRA_ADULT pricing if beyond capacity pricing is not configured
                      const extraAdultConfig = occupancyConfigs.find(
                        (config) =>
                          config.type === "EXTRA_ADULT" ||
                          config.name.toLowerCase().includes("adult")
                      );

                      if (extraAdultConfig) {
                        const extraAdultPriceRule = basePriceRules.find(
                          (rule) =>
                            rule.occupancy_type_id === extraAdultConfig.id
                        );

                        if (extraAdultPriceRule) {
                          const extraAdultSeasonalOverride =
                            await getSeasonalOverride(
                              hotelPricingService,
                              extraAdultPriceRule.id,
                              formattedCheckInDate,
                              formattedCheckOutDate
                            );

                          let extraAdultPrice = 0;
                          const checkInDate = new Date(formattedCheckInDate);
                          const checkOutDate = new Date(formattedCheckOutDate);
                          const currentDate = new Date(checkInDate);

                          while (currentDate < checkOutDate) {
                            const weekdayKey = getWeekdayKey(currentDate);
                            const dayPrice = getDayPrice(
                              extraAdultPriceRule,
                              extraAdultSeasonalOverride,
                              weekdayKey
                            );

                            extraAdultPrice += dayPrice * adultsBeyondCapacity;
                            currentDate.setDate(currentDate.getDate() + 1);
                          }

                          adultTotalPrice += extraAdultPrice;
                          priceBreakdown.push({
                            type: "adult_extra_beyond_capacity_fallback",
                            count: adultsBeyondCapacity,
                            total: extraAdultPrice,
                            description: `${adultsBeyondCapacity} extra adult(s) beyond capacity (fallback pricing)`,
                          });
                        }
                      }
                    }
                  } else {
                    console.log(
                      `No EXTRA_ADULT_BEYOND_CAPACITY config found, falling back to EXTRA_ADULT pricing`
                    );
                    // Fallback to regular EXTRA_ADULT pricing if beyond capacity config is not found
                    const extraAdultConfig = occupancyConfigs.find(
                      (config) =>
                        config.type === "EXTRA_ADULT" ||
                        config.name.toLowerCase().includes("adult")
                    );

                    if (extraAdultConfig) {
                      const extraAdultPriceRule = basePriceRules.find(
                        (rule) => rule.occupancy_type_id === extraAdultConfig.id
                      );

                      if (extraAdultPriceRule) {
                        const extraAdultSeasonalOverride =
                          await getSeasonalOverride(
                            hotelPricingService,
                            extraAdultPriceRule.id,
                            formattedCheckInDate,
                            formattedCheckOutDate
                          );

                        let extraAdultPrice = 0;
                        const checkInDate = new Date(formattedCheckInDate);
                        const checkOutDate = new Date(formattedCheckOutDate);
                        const currentDate = new Date(checkInDate);

                        while (currentDate < checkOutDate) {
                          const weekdayKey = getWeekdayKey(currentDate);
                          const dayPrice = getDayPrice(
                            extraAdultPriceRule,
                            extraAdultSeasonalOverride,
                            weekdayKey
                          );

                          extraAdultPrice += dayPrice * adultsBeyondCapacity;
                          currentDate.setDate(currentDate.getDate() + 1);
                        }

                        adultTotalPrice += extraAdultPrice;
                        priceBreakdown.push({
                          type: "adult_extra_beyond_capacity_fallback",
                          count: adultsBeyondCapacity,
                          total: extraAdultPrice,
                          description: `${adultsBeyondCapacity} extra adult(s) beyond capacity (fallback pricing)`,
                        });
                      }
                    }
                  }
                }
              }

              // If no adults were processed at all, fall back to EXTRA_ADULT for all
              if (adultsProcessed === 0) {
                console.log(
                  `No base pricing found, using EXTRA_ADULT for all ${adults} adults`
                );
                const extraAdultConfig = occupancyConfigs.find(
                  (config) =>
                    config.type === "EXTRA_ADULT" ||
                    config.name.toLowerCase().includes("adult")
                );

                if (extraAdultConfig) {
                  const extraAdultPriceRule = basePriceRules.find(
                    (rule) => rule.occupancy_type_id === extraAdultConfig.id
                  );

                  if (extraAdultPriceRule) {
                    // Check for seasonal overrides for EXTRA_ADULT fallback
                    const extraAdultSeasonalOverride =
                      await getSeasonalOverride(
                        hotelPricingService,
                        extraAdultPriceRule.id,
                        formattedCheckInDate,
                        formattedCheckOutDate
                      );

                    const checkInDate = new Date(formattedCheckInDate);
                    const checkOutDate = new Date(formattedCheckOutDate);
                    const currentDate = new Date(checkInDate);

                    while (currentDate < checkOutDate) {
                      const weekdayKey = getWeekdayKey(currentDate);
                      const dayPrice = getDayPrice(
                        extraAdultPriceRule,
                        extraAdultSeasonalOverride,
                        weekdayKey
                      );

                      adultTotalPrice += dayPrice * adults;
                      console.log(
                        `EXTRA_ADULT fallback day ${weekdayKey}: ${dayPrice} × ${adults} = ${
                          dayPrice * adults
                        }${extraAdultSeasonalOverride ? " (seasonal)" : ""}`
                      );
                      currentDate.setDate(currentDate.getDate() + 1);
                    }

                    priceBreakdown.push({
                      type: "adult_fallback",
                      count: adults,
                      total: adultTotalPrice,
                      description: `${adults} adult(s) (fallback)`,
                    });
                  }
                }
              }

              totalGuestPrice += adultTotalPrice;
              console.log(
                `Total adult price: ${adultTotalPrice} (processed ${adultsProcessed} + ${
                  adults - adultsProcessed
                } extra)`
              );
            }

            // Calculate child pricing
            if (children > 0) {
              console.log(`Calculating pricing for ${children} children`);

              // Find child occupancy config
              const childOccupancyConfig = occupancyConfigs.find(
                (config) =>
                  config.type === "CHILD" ||
                  config.name.toLowerCase().includes("child")
              );

              if (childOccupancyConfig) {
                console.log(
                  `Found child occupancy config: ${childOccupancyConfig.name} (${childOccupancyConfig.id})`
                );

                // Find price rule for children
                const childPriceRule = basePriceRules.find(
                  (rule) => rule.occupancy_type_id === childOccupancyConfig.id
                );

                if (childPriceRule) {
                  console.log(
                    `Found child price rule: ${childPriceRule.id}, amount: ${childPriceRule.amount}`
                  );

                  // Check for seasonal overrides for children
                  const childSeasonalOverride = await getSeasonalOverride(
                    hotelPricingService,
                    childPriceRule.id,
                    formattedCheckInDate,
                    formattedCheckOutDate
                  );

                  // Calculate day-wise pricing for children
                  let childTotalPrice = 0;
                  const checkInDate = new Date(formattedCheckInDate);
                  const checkOutDate = new Date(formattedCheckOutDate);

                  const currentDate = new Date(checkInDate);
                  while (currentDate < checkOutDate) {
                    const weekdayKey = getWeekdayKey(currentDate);

                    // Get the price for this day of the week, considering seasonal overrides
                    const dayPrice = getDayPrice(
                      childPriceRule,
                      childSeasonalOverride,
                      weekdayKey
                    );

                    // Multiply by number of children
                    const childDayPrice = dayPrice * children;
                    childTotalPrice += childDayPrice;

                    console.log(
                      `Children day ${weekdayKey}: ${dayPrice} × ${children} = ${childDayPrice}${
                        childSeasonalOverride ? " (seasonal)" : ""
                      }, running total: ${childTotalPrice}`
                    );

                    currentDate.setDate(currentDate.getDate() + 1);
                  }

                  totalGuestPrice += childTotalPrice;
                  priceBreakdown.push({
                    type: "child",
                    count: children,
                    total: childTotalPrice,
                    description: `${children} child(ren)`,
                  });

                  console.log(`Child total price: ${childTotalPrice}`);
                } else {
                  console.log(
                    `No price rule found for child occupancy type ${childOccupancyConfig.id}`
                  );
                }
              } else {
                console.log("No child occupancy config found");
              }
            }

            // Calculate infant pricing
            if (infants > 0) {
              console.log(`Calculating pricing for ${infants} infants`);

              // Find infant occupancy config
              const infantOccupancyConfig = occupancyConfigs.find(
                (config) =>
                  config.type === "INFANT" ||
                  config.name.toLowerCase().includes("infant")
              );

              if (infantOccupancyConfig) {
                console.log(
                  `Found infant occupancy config: ${infantOccupancyConfig.name} (${infantOccupancyConfig.id})`
                );

                // Find price rule for infants
                const infantPriceRule = basePriceRules.find(
                  (rule) => rule.occupancy_type_id === infantOccupancyConfig.id
                );

                if (infantPriceRule) {
                  console.log(
                    `Found infant price rule: ${infantPriceRule.id}, amount: ${infantPriceRule.amount}`
                  );

                  // Check for seasonal overrides for infants
                  const infantSeasonalOverride = await getSeasonalOverride(
                    hotelPricingService,
                    infantPriceRule.id,
                    formattedCheckInDate,
                    formattedCheckOutDate
                  );

                  // Calculate day-wise pricing for infants
                  let infantTotalPrice = 0;
                  const checkInDate = new Date(formattedCheckInDate);
                  const checkOutDate = new Date(formattedCheckOutDate);

                  const currentDate = new Date(checkInDate);
                  while (currentDate < checkOutDate) {
                    const weekdayKey = getWeekdayKey(currentDate);

                    // Get the price for this day of the week, considering seasonal overrides
                    const dayPrice = getDayPrice(
                      infantPriceRule,
                      infantSeasonalOverride,
                      weekdayKey
                    );

                    // Multiply by number of infants
                    const infantDayPrice = dayPrice * infants;
                    infantTotalPrice += infantDayPrice;

                    console.log(
                      `Infants day ${weekdayKey}: ${dayPrice} × ${infants} = ${infantDayPrice}${
                        infantSeasonalOverride ? " (seasonal)" : ""
                      }, running total: ${infantTotalPrice}`
                    );

                    currentDate.setDate(currentDate.getDate() + 1);
                  }

                  totalGuestPrice += infantTotalPrice;
                  priceBreakdown.push({
                    type: "infant",
                    count: infants,
                    total: infantTotalPrice,
                    description: `${infants} infant(s)`,
                  });

                  console.log(`Infant total price: ${infantTotalPrice}`);
                } else {
                  console.log(
                    `No price rule found for infant occupancy type ${infantOccupancyConfig.id}`
                  );
                }
              } else {
                console.log("No infant occupancy config found");
              }
            }

            // Set the total price
            totalPrice = totalGuestPrice;

            console.log(`Total guest price calculation:`, priceBreakdown);
            console.log(`Final total price: ${totalPrice}`);

            // Calculate average price per night
            pricePerNight = Number(totalPrice) / Number(nights);
            console.log(
              `Calculated average price per night: ${pricePerNight} (${totalPrice} / ${nights})`
            );

            // The total price has already been calculated using the per-guest-type approach above
            // No need for additional pricing logic here
          } catch (priceError) {
            console.error(
              `Error calculating prices for room ${variant.id}:`,
              priceError
            );
            throw new Error(
              `Price for room ${variant.id} could not be calculated: ${priceError.message}`
            );
          }

          // Format the prices - ensure we're working with numbers
          console.log(
            `Before conversion: pricePerNight=${pricePerNight} (${typeof pricePerNight}), totalPrice=${totalPrice} (${typeof totalPrice})`
          );

          // Ensure we're working with numbers before division
          const pricePerNightNum = Number(pricePerNight);
          const totalPriceNum = Number(totalPrice);

          console.log(
            `After Number() conversion: pricePerNightNum=${pricePerNightNum}, totalPriceNum=${totalPriceNum}`
          );

          // Convert cents to decimal
          const pricePerNightDecimal = pricePerNightNum / 100;
          const totalPriceDecimal = totalPriceNum / 100;

          console.log(
            `Final price calculation: pricePerNight=${pricePerNight}, totalPrice=${totalPrice}`
          );
          let formattedTotalPrice = `${currency_code} ${totalPriceDecimal.toFixed(
            2
          )}`;

          try {
            // Try to use Intl.NumberFormat for better formatting
            const formatter = new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: currency_code,
            });

            formattedTotalPrice = formatter.format(totalPriceDecimal);
          } catch (formatError) {
            console.error(`Error formatting prices:`, formatError);
            // Continue with basic formatting
          }

          // Calculate prices for different meal plans using the same per-guest-type approach
          const mealPlans = ["none", "bb", "hb", "fb"];
          const mealPlanPrices = {};

          // Get all occupancy configurations for meal plan calculations
          const occupancyConfigs =
            await hotelPricingService.listOccupancyConfigs({
              hotel_id: hotel.id,
            });

          // Calculate prices for all meal plans using the hotel pricing service
          for (const mealPlan of mealPlans) {
            try {
              console.log(`Calculating price for meal plan: ${mealPlan}`);

              // Get meal plan ID and label for this meal plan type
              const mealPlanEntities = await hotelPricingService.listMealPlans({
                hotel_id: hotel.id,
                type: mealPlan,
              });

              let mealPlanId: string | null = null;
              let mealPlanLabel: string = mealPlan; // Default to type if no label found
              if (mealPlanEntities && mealPlanEntities.length > 0) {
                mealPlanId = mealPlanEntities[0].id;
                mealPlanLabel = mealPlanEntities[0].name || mealPlan; // Use name as label
                console.log(
                  `Found meal plan ID ${mealPlanId} with label "${mealPlanLabel}" for type ${mealPlan}`
                );
              } else {
                console.log(`No meal plan found for type ${mealPlan}`);
                if (mealPlan === "none") {
                  // For "none" meal plan, we'll use the same logic but without meal plan ID
                  mealPlanLabel = "None"; // Set a default label for "none"
                  console.log(`Using base pricing for "none" meal plan`);
                } else {
                  continue; // Skip to the next meal plan
                }
              }

              // Calculate meal plan pricing per guest type
              let mealPlanTotalPrice = 0;

              // Calculate adult meal plan pricing using the same BASE + EXTRA logic
              if (adults > 0) {
                let adultMealPlanPrice = 0;
                let adultsProcessedForMealPlan = 0;

                // First, try to use BASE_2 for 2 adults if we have 2 or more adults
                if (adults >= 2) {
                  const base2Config = occupancyConfigs.find(
                    (config) => config.type === "BASE_2"
                  );

                  if (base2Config) {
                    const base2MealPlanRule = basePriceRules.find(
                      (rule) =>
                        rule.occupancy_type_id === base2Config.id &&
                        (mealPlanId
                          ? rule.meal_plan_id === mealPlanId
                          : !rule.meal_plan_id)
                    );

                    if (base2MealPlanRule) {
                      console.log(
                        `Using BASE_2 meal plan pricing for 2 adults`
                      );

                      // Check for seasonal overrides for BASE_2 meal plan
                      const base2MealPlanSeasonalOverride =
                        await getSeasonalOverride(
                          hotelPricingService,
                          base2MealPlanRule.id,
                          formattedCheckInDate,
                          formattedCheckOutDate
                        );

                      const checkInDate = new Date(formattedCheckInDate);
                      const checkOutDate = new Date(formattedCheckOutDate);
                      const currentDate = new Date(checkInDate);

                      while (currentDate < checkOutDate) {
                        const weekdayKey = getWeekdayKey(currentDate);
                        const dayPrice = getDayPrice(
                          base2MealPlanRule,
                          base2MealPlanSeasonalOverride,
                          weekdayKey
                        );

                        adultMealPlanPrice += dayPrice;
                        currentDate.setDate(currentDate.getDate() + 1);
                      }

                      adultsProcessedForMealPlan = 2;
                    }
                  }
                }

                // If we couldn't use BASE_2, try BASE_1 for 1 adult
                if (adultsProcessedForMealPlan === 0 && adults >= 1) {
                  const base1Config = occupancyConfigs.find(
                    (config) => config.type === "BASE_1"
                  );

                  if (base1Config) {
                    const base1MealPlanRule = basePriceRules.find(
                      (rule) =>
                        rule.occupancy_type_id === base1Config.id &&
                        (mealPlanId
                          ? rule.meal_plan_id === mealPlanId
                          : !rule.meal_plan_id)
                    );

                    if (base1MealPlanRule) {
                      console.log(`Using BASE_1 meal plan pricing for 1 adult`);

                      // Check for seasonal overrides for BASE_1 meal plan
                      const base1MealPlanSeasonalOverride =
                        await getSeasonalOverride(
                          hotelPricingService,
                          base1MealPlanRule.id,
                          formattedCheckInDate,
                          formattedCheckOutDate
                        );

                      const checkInDate = new Date(formattedCheckInDate);
                      const checkOutDate = new Date(formattedCheckOutDate);
                      const currentDate = new Date(checkInDate);

                      while (currentDate < checkOutDate) {
                        const weekdayKey = getWeekdayKey(currentDate);
                        const dayPrice = getDayPrice(
                          base1MealPlanRule,
                          base1MealPlanSeasonalOverride,
                          weekdayKey
                        );

                        adultMealPlanPrice += dayPrice;
                        currentDate.setDate(currentDate.getDate() + 1);
                      }

                      adultsProcessedForMealPlan = 1;
                    }
                  } else {
                    // If BASE_1 is not available, use EXTRA_ADULT for all adults
                    console.log(
                      `BASE_1 not found for meal plan, using EXTRA_ADULT for all ${adults} adults`
                    );
                    const extraAdultConfig = occupancyConfigs.find(
                      (config) =>
                        config.type === "EXTRA_ADULT" ||
                        config.name.toLowerCase().includes("adult")
                    );

                    if (extraAdultConfig) {
                      const extraAdultMealPlanRule = basePriceRules.find(
                        (rule) =>
                          rule.occupancy_type_id === extraAdultConfig.id &&
                          (mealPlanId
                            ? rule.meal_plan_id === mealPlanId
                            : !rule.meal_plan_id)
                      );

                      if (extraAdultMealPlanRule) {
                        // Check for seasonal overrides for EXTRA_ADULT meal plan
                        const extraAdultMealPlanSeasonalOverride =
                          await getSeasonalOverride(
                            hotelPricingService,
                            extraAdultMealPlanRule.id,
                            formattedCheckInDate,
                            formattedCheckOutDate
                          );

                        const checkInDate = new Date(formattedCheckInDate);
                        const checkOutDate = new Date(formattedCheckOutDate);
                        const currentDate = new Date(checkInDate);

                        while (currentDate < checkOutDate) {
                          const weekdayKey = getWeekdayKey(currentDate);
                          const dayPrice = getDayPrice(
                            extraAdultMealPlanRule,
                            extraAdultMealPlanSeasonalOverride,
                            weekdayKey
                          );

                          adultMealPlanPrice += dayPrice * adults;
                          console.log(
                            `EXTRA_ADULT meal plan day ${weekdayKey}: ${dayPrice} × ${adults} = ${
                              dayPrice * adults
                            }${
                              extraAdultMealPlanSeasonalOverride
                                ? " (seasonal)"
                                : ""
                            }`
                          );
                          currentDate.setDate(currentDate.getDate() + 1);
                        }

                        adultsProcessedForMealPlan = adults;
                      }
                    }
                  }
                }

                // Handle remaining adults with capacity-aware pricing (same logic as main calculation)
                const remainingAdultsForMealPlan =
                  adults - adultsProcessedForMealPlan;
                if (remainingAdultsForMealPlan > 0) {
                  // Get room capacity information
                  const maxAdults = Number(config.metadata?.max_adults) || 1;
                  const maxAdultsBeyondCapacity =
                    Number(config.metadata?.max_adults_beyond_capacity) || 0;

                  // Calculate how many adults are within capacity vs beyond capacity
                  const totalProcessedSoFar = adultsProcessedForMealPlan;
                  const adultsWithinCapacity = Math.max(
                    0,
                    Math.min(
                      remainingAdultsForMealPlan,
                      maxAdults - totalProcessedSoFar
                    )
                  );
                  const adultsBeyondCapacity = Math.max(
                    0,
                    remainingAdultsForMealPlan - adultsWithinCapacity
                  );

                  // Handle adults within capacity using EXTRA_ADULT pricing
                  if (adultsWithinCapacity > 0) {
                    const extraAdultConfig = occupancyConfigs.find(
                      (config) =>
                        config.type === "EXTRA_ADULT" ||
                        config.name.toLowerCase().includes("adult")
                    );

                    if (extraAdultConfig) {
                      const extraAdultMealPlanRule = basePriceRules.find(
                        (rule) =>
                          rule.occupancy_type_id === extraAdultConfig.id &&
                          (mealPlanId
                            ? rule.meal_plan_id === mealPlanId
                            : !rule.meal_plan_id)
                      );

                      if (extraAdultMealPlanRule) {
                        // Check for seasonal overrides for EXTRA_ADULT meal plan
                        const extraAdultMealPlanSeasonalOverride =
                          await getSeasonalOverride(
                            hotelPricingService,
                            extraAdultMealPlanRule.id,
                            formattedCheckInDate,
                            formattedCheckOutDate
                          );

                        const checkInDate = new Date(formattedCheckInDate);
                        const checkOutDate = new Date(formattedCheckOutDate);
                        const currentDate = new Date(checkInDate);

                        while (currentDate < checkOutDate) {
                          const weekdayKey = getWeekdayKey(currentDate);
                          const dayPrice = getDayPrice(
                            extraAdultMealPlanRule,
                            extraAdultMealPlanSeasonalOverride,
                            weekdayKey
                          );

                          adultMealPlanPrice += dayPrice * adultsWithinCapacity;
                          currentDate.setDate(currentDate.getDate() + 1);
                        }
                      }
                    }
                  }

                  // Handle adults beyond capacity using EXTRA_ADULT_BEYOND_CAPACITY pricing
                  if (adultsBeyondCapacity > 0) {
                    const extraAdultBeyondCapacityConfig =
                      occupancyConfigs.find(
                        (config) =>
                          config.type === "EXTRA_ADULT_BEYOND_CAPACITY"
                      );

                    if (extraAdultBeyondCapacityConfig) {
                      const extraAdultBeyondCapacityMealPlanRule =
                        basePriceRules.find(
                          (rule) =>
                            rule.occupancy_type_id ===
                              extraAdultBeyondCapacityConfig.id &&
                            (mealPlanId
                              ? rule.meal_plan_id === mealPlanId
                              : !rule.meal_plan_id)
                        );

                      if (extraAdultBeyondCapacityMealPlanRule) {
                        // Check for seasonal overrides for EXTRA_ADULT_BEYOND_CAPACITY meal plan
                        const extraAdultBeyondCapacityMealPlanSeasonalOverride =
                          await getSeasonalOverride(
                            hotelPricingService,
                            extraAdultBeyondCapacityMealPlanRule.id,
                            formattedCheckInDate,
                            formattedCheckOutDate
                          );

                        const checkInDate = new Date(formattedCheckInDate);
                        const checkOutDate = new Date(formattedCheckOutDate);
                        const currentDate = new Date(checkInDate);

                        while (currentDate < checkOutDate) {
                          const weekdayKey = getWeekdayKey(currentDate);
                          const dayPrice = getDayPrice(
                            extraAdultBeyondCapacityMealPlanRule,
                            extraAdultBeyondCapacityMealPlanSeasonalOverride,
                            weekdayKey
                          );

                          adultMealPlanPrice += dayPrice * adultsBeyondCapacity;
                          currentDate.setDate(currentDate.getDate() + 1);
                        }
                      } else {
                        // Fallback to regular EXTRA_ADULT pricing if beyond capacity pricing is not configured
                        const extraAdultConfig = occupancyConfigs.find(
                          (config) =>
                            config.type === "EXTRA_ADULT" ||
                            config.name.toLowerCase().includes("adult")
                        );

                        if (extraAdultConfig) {
                          const extraAdultMealPlanRule = basePriceRules.find(
                            (rule) =>
                              rule.occupancy_type_id === extraAdultConfig.id &&
                              (mealPlanId
                                ? rule.meal_plan_id === mealPlanId
                                : !rule.meal_plan_id)
                          );

                          if (extraAdultMealPlanRule) {
                            const extraAdultMealPlanSeasonalOverride =
                              await getSeasonalOverride(
                                hotelPricingService,
                                extraAdultMealPlanRule.id,
                                formattedCheckInDate,
                                formattedCheckOutDate
                              );

                            const checkInDate = new Date(formattedCheckInDate);
                            const checkOutDate = new Date(
                              formattedCheckOutDate
                            );
                            const currentDate = new Date(checkInDate);

                            while (currentDate < checkOutDate) {
                              const weekdayKey = getWeekdayKey(currentDate);
                              const dayPrice = getDayPrice(
                                extraAdultMealPlanRule,
                                extraAdultMealPlanSeasonalOverride,
                                weekdayKey
                              );

                              adultMealPlanPrice +=
                                dayPrice * adultsBeyondCapacity;
                              currentDate.setDate(currentDate.getDate() + 1);
                            }
                          }
                        }
                      }
                    } else {
                      // Fallback to regular EXTRA_ADULT pricing if beyond capacity config is not found
                      const extraAdultConfig = occupancyConfigs.find(
                        (config) =>
                          config.type === "EXTRA_ADULT" ||
                          config.name.toLowerCase().includes("adult")
                      );

                      if (extraAdultConfig) {
                        const extraAdultMealPlanRule = basePriceRules.find(
                          (rule) =>
                            rule.occupancy_type_id === extraAdultConfig.id &&
                            (mealPlanId
                              ? rule.meal_plan_id === mealPlanId
                              : !rule.meal_plan_id)
                        );

                        if (extraAdultMealPlanRule) {
                          const extraAdultMealPlanSeasonalOverride =
                            await getSeasonalOverride(
                              hotelPricingService,
                              extraAdultMealPlanRule.id,
                              formattedCheckInDate,
                              formattedCheckOutDate
                            );

                          const checkInDate = new Date(formattedCheckInDate);
                          const checkOutDate = new Date(formattedCheckOutDate);
                          const currentDate = new Date(checkInDate);

                          while (currentDate < checkOutDate) {
                            const weekdayKey = getWeekdayKey(currentDate);
                            const dayPrice = getDayPrice(
                              extraAdultMealPlanRule,
                              extraAdultMealPlanSeasonalOverride,
                              weekdayKey
                            );

                            adultMealPlanPrice +=
                              dayPrice * adultsBeyondCapacity;
                            currentDate.setDate(currentDate.getDate() + 1);
                          }
                        }
                      }
                    }
                  }
                }

                // If no adults were processed at all, fall back to EXTRA_ADULT for all
                if (adultsProcessedForMealPlan === 0) {
                  console.log(
                    `No base meal plan pricing found, using EXTRA_ADULT for all ${adults} adults`
                  );
                  const extraAdultConfig = occupancyConfigs.find(
                    (config) =>
                      config.type === "EXTRA_ADULT" ||
                      config.name.toLowerCase().includes("adult")
                  );

                  if (extraAdultConfig) {
                    const extraAdultMealPlanRule = basePriceRules.find(
                      (rule) =>
                        rule.occupancy_type_id === extraAdultConfig.id &&
                        (mealPlanId
                          ? rule.meal_plan_id === mealPlanId
                          : !rule.meal_plan_id)
                    );

                    if (extraAdultMealPlanRule) {
                      // Check for seasonal overrides for EXTRA_ADULT meal plan fallback
                      const extraAdultMealPlanSeasonalOverride =
                        await getSeasonalOverride(
                          hotelPricingService,
                          extraAdultMealPlanRule.id,
                          formattedCheckInDate,
                          formattedCheckOutDate
                        );

                      const checkInDate = new Date(formattedCheckInDate);
                      const checkOutDate = new Date(formattedCheckOutDate);
                      const currentDate = new Date(checkInDate);

                      while (currentDate < checkOutDate) {
                        const weekdayKey = getWeekdayKey(currentDate);
                        const dayPrice = getDayPrice(
                          extraAdultMealPlanRule,
                          extraAdultMealPlanSeasonalOverride,
                          weekdayKey
                        );

                        adultMealPlanPrice += dayPrice * adults;
                        console.log(
                          `EXTRA_ADULT fallback meal plan day ${weekdayKey}: ${dayPrice} × ${adults} = ${
                            dayPrice * adults
                          }${
                            extraAdultMealPlanSeasonalOverride
                              ? " (seasonal)"
                              : ""
                          }`
                        );
                        currentDate.setDate(currentDate.getDate() + 1);
                      }
                    }
                  }
                }

                mealPlanTotalPrice += adultMealPlanPrice;
                console.log(
                  `Adult meal plan ${mealPlan} price: ${adultMealPlanPrice} (processed ${adultsProcessedForMealPlan} + ${
                    adults - adultsProcessedForMealPlan
                  } extra)`
                );
              }

              // Calculate child meal plan pricing
              if (children > 0) {
                const childOccupancyConfig = occupancyConfigs.find(
                  (config) =>
                    config.type === "CHILD" ||
                    config.name.toLowerCase().includes("child")
                );

                if (childOccupancyConfig) {
                  // Find price rule for children with this meal plan
                  const childMealPlanRule = basePriceRules.find(
                    (rule) =>
                      rule.occupancy_type_id === childOccupancyConfig.id &&
                      (mealPlanId
                        ? rule.meal_plan_id === mealPlanId
                        : !rule.meal_plan_id)
                  );

                  if (childMealPlanRule) {
                    console.log(
                      `Found child meal plan rule for ${mealPlan}: ${childMealPlanRule.id}`
                    );

                    // Check for seasonal overrides for children meal plan
                    const childMealPlanSeasonalOverride =
                      await getSeasonalOverride(
                        hotelPricingService,
                        childMealPlanRule.id,
                        formattedCheckInDate,
                        formattedCheckOutDate
                      );

                    // Calculate day-wise pricing for children with this meal plan
                    let childMealPlanPrice = 0;
                    const checkInDate = new Date(formattedCheckInDate);
                    const checkOutDate = new Date(formattedCheckOutDate);

                    const currentDate = new Date(checkInDate);
                    while (currentDate < checkOutDate) {
                      const weekdayKey = getWeekdayKey(currentDate);
                      const dayPrice = getDayPrice(
                        childMealPlanRule,
                        childMealPlanSeasonalOverride,
                        weekdayKey
                      );

                      childMealPlanPrice += dayPrice * children;
                      console.log(
                        `Children meal plan day ${weekdayKey}: ${dayPrice} × ${children} = ${
                          dayPrice * children
                        }${childMealPlanSeasonalOverride ? " (seasonal)" : ""}`
                      );
                      currentDate.setDate(currentDate.getDate() + 1);
                    }

                    mealPlanTotalPrice += childMealPlanPrice;
                    console.log(
                      `Child meal plan ${mealPlan} price: ${childMealPlanPrice}`
                    );
                  }
                }
              }

              // Calculate infant meal plan pricing
              if (infants > 0) {
                const infantOccupancyConfig = occupancyConfigs.find(
                  (config) =>
                    config.type === "INFANT" ||
                    config.name.toLowerCase().includes("infant")
                );

                if (infantOccupancyConfig) {
                  // Find price rule for infants with this meal plan
                  const infantMealPlanRule = basePriceRules.find(
                    (rule) =>
                      rule.occupancy_type_id === infantOccupancyConfig.id &&
                      (mealPlanId
                        ? rule.meal_plan_id === mealPlanId
                        : !rule.meal_plan_id)
                  );

                  if (infantMealPlanRule) {
                    console.log(
                      `Found infant meal plan rule for ${mealPlan}: ${infantMealPlanRule.id}`
                    );

                    // Check for seasonal overrides for infants meal plan
                    const infantMealPlanSeasonalOverride =
                      await getSeasonalOverride(
                        hotelPricingService,
                        infantMealPlanRule.id,
                        formattedCheckInDate,
                        formattedCheckOutDate
                      );

                    // Calculate day-wise pricing for infants with this meal plan
                    let infantMealPlanPrice = 0;
                    const checkInDate = new Date(formattedCheckInDate);
                    const checkOutDate = new Date(formattedCheckOutDate);

                    const currentDate = new Date(checkInDate);
                    while (currentDate < checkOutDate) {
                      const weekdayKey = getWeekdayKey(currentDate);
                      const dayPrice = getDayPrice(
                        infantMealPlanRule,
                        infantMealPlanSeasonalOverride,
                        weekdayKey
                      );

                      infantMealPlanPrice += dayPrice * infants;
                      console.log(
                        `Infants meal plan day ${weekdayKey}: ${dayPrice} × ${infants} = ${
                          dayPrice * infants
                        }${infantMealPlanSeasonalOverride ? " (seasonal)" : ""}`
                      );
                      currentDate.setDate(currentDate.getDate() + 1);
                    }

                    mealPlanTotalPrice += infantMealPlanPrice;
                    console.log(
                      `Infant meal plan ${mealPlan} price: ${infantMealPlanPrice}`
                    );
                  }
                }
              }

              // Convert cents to decimal for meal plan pricing
              const mealPlanPricePerNight =
                Number(mealPlanTotalPrice) / Number(nights);
              const mealPlanPricePerNightDecimal =
                Number(mealPlanPricePerNight) / 100;
              const mealPlanTotalPriceDecimal =
                Number(mealPlanTotalPrice) / 100;

              console.log(
                `Meal plan ${mealPlan} price calculation: totalPrice=${mealPlanTotalPrice}, perNight=${mealPlanPricePerNight}`
              );
              console.log(
                `Converted to decimal: perNightDecimal=${mealPlanPricePerNightDecimal}, totalDecimal=${mealPlanTotalPriceDecimal}`
              );

              // Calculate extra adults beyond capacity information for this meal plan
              let extraAdultsBeyondCapacityAmount = 0;

              // Get room capacity information to determine extra adults beyond capacity
              const maxAdults = Number(config.metadata?.max_adults) || 1;
              const maxAdultsBeyondCapacity = Number(config.metadata?.max_adults_beyond_capacity) || 0;
              const totalMaxAdultsAllowed = maxAdults + maxAdultsBeyondCapacity;

              // Calculate how many adults are beyond capacity
              const adultsBeyondCapacity = Math.max(0, Math.min(adults - maxAdults, maxAdultsBeyondCapacity));

              if (adultsBeyondCapacity > 0) {
                // Find the extra adult beyond capacity pricing rule for this meal plan
                const extraAdultBeyondCapacityConfig = occupancyConfigs.find(
                  (config) => config.type === "EXTRA_ADULT_BEYOND_CAPACITY"
                );

                if (extraAdultBeyondCapacityConfig) {
                  const extraAdultBeyondCapacityMealPlanRule = basePriceRules.find(
                    (rule) =>
                      rule.occupancy_type_id === extraAdultBeyondCapacityConfig.id &&
                      (mealPlanId ? rule.meal_plan_id === mealPlanId : !rule.meal_plan_id)
                  );

                  if (extraAdultBeyondCapacityMealPlanRule) {
                    // Calculate the per-night amount for extra adults beyond capacity
                    const dayPrice = extraAdultBeyondCapacityMealPlanRule.amount || 0;
                    extraAdultsBeyondCapacityAmount = (dayPrice / 100) * adultsBeyondCapacity;
                  }
                }
              }

              // Add the meal plan price
              const mealPlanTotalAmount = mealPlanTotalPriceDecimal;
              const mealPlanPerNightAmount =
                mealPlanTotalAmount / Number(nights);

              mealPlanPrices[mealPlan] = {
                amount: mealPlanPerNightAmount,
                per_night_amount: mealPlanPerNightAmount,
                currency_code,
                formatted: `${currency_code} ${mealPlanPerNightAmount.toFixed(
                  2
                )}`,
                total_amount: mealPlanTotalAmount,
                original_amount: mealPlanPerNightAmount,
                nights: Number(nights),
                label: mealPlanLabel, // Add the meal plan label
                extra_adults_beyond_capacity_amount: extraAdultsBeyondCapacityAmount, // Add extra adults beyond capacity amount
              };

              console.log(
                `Added price for meal plan ${mealPlan}: ${mealPlanPricePerNightDecimal} per night`
              );
            } catch (mealPlanError) {
              console.error(
                `Error processing meal plan ${mealPlan}:`,
                mealPlanError
              );
              // Skip this meal plan on error
              continue;
            }
          }

          console.log(
            `Meal plan prices for variant ${variant.id}:`,
            JSON.stringify(mealPlanPrices)
          );

          // Add the variant with price information including meal plans
          const finalTotalAmount = totalPriceDecimal;
          const finalPerNightAmount = finalTotalAmount / Number(nights);

          const priceObject = {
            amount: finalPerNightAmount,
            currency_code,
            formatted: formattedTotalPrice,
            total_amount: finalTotalAmount,
            original_amount: finalPerNightAmount,
            per_night_amount: finalPerNightAmount,
            nights: Number(nights),
            meal_plans: mealPlanPrices,
            selected_meal_plan: "none",
          };

          const pricePerNightObject = {
            amount: finalPerNightAmount,
            currency_code,
            formatted: new Intl.NumberFormat("en-US", {
              style: "currency",
              currency: currency_code,
            }).format(finalPerNightAmount),
          };

          console.log("Final price object:", JSON.stringify(priceObject));
          console.log(
            "Final price per night object:",
            JSON.stringify(pricePerNightObject)
          );

          pricedVariants.push({
            ...variant,
            price: priceObject,
            price_per_night: pricePerNightObject,
          });
        } catch (error) {
          console.error(
            `Error calculating price for room ${variant.id}:`,
            error
          );
          // Add the variant without price information
          pricedVariants.push(variant);
        }
      }

      // Add the room configuration with its priced variants
      pricedResults.push({
        ...config,
        priced_variants: pricedVariants,
        unavailable_variants: config.unavailable_variants || [],
      });
    }

    return new StepResponse(pricedResults);
  }
);
