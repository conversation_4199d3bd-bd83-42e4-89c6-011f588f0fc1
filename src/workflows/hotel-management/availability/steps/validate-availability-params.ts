import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";
import { MedusaError } from "@camped-ai/framework/utils";

type ValidateAvailabilityParamsInput = {
  hotel_id?: string;
  check_in: string;
  check_out: string;
  adults?: number;
  children?: number;
  infants?: number;

  currency_code?: string;
  child_ages?: Array<{ age: number | string }>;
  sales_channel_id?: string;
};

type ValidatedParams = {
  hotel_id?: string;
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  infants: number;

  currency_code: string;
  nights: number;
  child_ages?: Array<{ age: number | string }>;
  sales_channel_id?: string;
};

export const validateAvailabilityParamsStep = createStep(
  "validate-availability-params",
  async (input: ValidateAvailabilityParamsInput) => {
    const {
      hotel_id,
      check_in,
      check_out,
      adults = 1,
      children = 0,
      infants = 0,

      currency_code = "USD",
      child_ages,
      sales_channel_id,
    } = input;

    // Validate required parameters
    if (!check_in || !check_out) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "check_in and check_out dates are required"
      );
    }

    // Parse dates
    const checkInDate = new Date(check_in);
    const checkOutDate = new Date(check_out);

    // Validate date format
    if (isNaN(checkInDate.getTime()) || isNaN(checkOutDate.getTime())) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "Invalid date format. Use ISO format (YYYY-MM-DD)"
      );
    }

    // Validate date range
    if (checkInDate >= checkOutDate) {
      throw new MedusaError(
        MedusaError.Types.INVALID_DATA,
        "check_in date must be before check_out date"
      );
    }

    // Calculate number of nights
    const nights = Math.ceil(
      (checkOutDate.getTime() - checkInDate.getTime()) / (1000 * 60 * 60 * 24)
    );

    return new StepResponse({
      hotel_id,
      check_in_date: checkInDate,
      check_out_date: checkOutDate,
      adults,
      children,
      infants,

      currency_code,
      nights,
      child_ages,
      sales_channel_id,
    });
  }
);
