import { createStep, StepResponse } from "@camped-ai/framework/workflows-sdk";

type CheckRoomAvailabilityInput = {
  room_configurations: any;
  check_in_date: Date;
  check_out_date: Date;
  adults: number;
  children: number;
  infants: number;
  currency_code: string;
  nights: number;
};

// Helper function to check variant availability - optimized date processing
function checkVariantAvailability(
  roomInventory: any[],
  checkInDate: Date,
  checkOutDate: Date
) {
  // Normalize dates to UTC for consistent comparison (do this once)
  const checkInDateUTC = new Date(
    Date.UTC(
      checkInDate.getFullYear(),
      checkInDate.getMonth(),
      checkInDate.getDate()
    )
  );

  const checkOutDateUTC = new Date(
    Date.UTC(
      checkOutDate.getFullYear(),
      checkOutDate.getMonth(),
      checkOutDate.getDate()
    )
  );

  // Pre-process inventory entries to avoid repeated date parsing
  const processedInventory = roomInventory.map(entry => {
    const entryFromDate = new Date(entry.from_date);
    const entryToDate = new Date(entry.to_date);

    return {
      ...entry,
      fromDateUTC: new Date(
        Date.UTC(
          entryFromDate.getFullYear(),
          entryFromDate.getMonth(),
          entryFromDate.getDate()
        )
      ),
      toDateUTC: new Date(
        Date.UTC(
          entryToDate.getFullYear(),
          entryToDate.getMonth(),
          entryToDate.getDate()
        )
      ),
      isAvailable: entry.status === "available" &&
                   (entry.available_quantity === undefined || entry.available_quantity > 0)
    };
  });

  // Create a list of days to check (do this once)
  const requestedDays = [];
  let currentDate = new Date(checkInDateUTC);
  while (currentDate < checkOutDateUTC) {
    requestedDays.push(new Date(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  let hasAvailableEntry = false;
  let hasConflict = false;
  const unavailableDates = [];
  const unavailableReasons = [];

  // Check each day against processed inventory
  for (const day of requestedDays) {
    let dayIsCovered = false;

    for (const entry of processedInventory) {
      // Check if this entry covers this day
      if (entry.fromDateUTC <= day && entry.toDateUTC > day) {
        if (entry.isAvailable) {
          dayIsCovered = true;
          break;
        } else {
          // This day has a conflicting entry
          hasConflict = true;
          const dateStr = day.toISOString().split("T")[0];
          if (!unavailableDates.includes(dateStr)) {
            unavailableDates.push(dateStr);
            unavailableReasons.push({
              date: dateStr,
              reason: `Room is ${entry.status} on this date`,
            });
          }
          break;
        }
      }
    }

    if (!dayIsCovered) {
      // If day is not covered by any inventory entry, it's unavailable
      const dateStr = day.toISOString().split("T")[0];
      if (!unavailableDates.includes(dateStr)) {
        unavailableDates.push(dateStr);
        unavailableReasons.push({
          date: dateStr,
          reason: "No inventory entry found for this date",
        });
      }
      hasAvailableEntry = false;
    } else if (!hasConflict) {
      hasAvailableEntry = true;
    }
  }

  const isAvailable = hasAvailableEntry && !hasConflict;

  return {
    available: isAvailable,
    unavailableDates,
    unavailableReasons,
    inventoryEntries: roomInventory,
  };
}

export const checkRoomAvailabilityStep = createStep(
  "check-room-availability",
  async (input: CheckRoomAvailabilityInput, { container }) => {
    console.log("Input to checkRoomAvailabilityStep:", input);

    const {
      room_configurations,
      check_in_date,
      check_out_date,
      adults,
      children,
      infants,
    } = input;

    // Extract the actual room configurations from the workflow data
    let actualRoomConfigs = room_configurations;
    if (room_configurations && room_configurations.roomConfigsWithVariants) {
      actualRoomConfigs = room_configurations.roomConfigsWithVariants;
    }

    console.log("Actual room configurations:", actualRoomConfigs);

    // Ensure dates are Date objects
    const checkInDate =
      check_in_date instanceof Date ? check_in_date : new Date(check_in_date);
    const checkOutDate =
      check_out_date instanceof Date
        ? check_out_date
        : new Date(check_out_date);

    console.log(
      `Converted dates: check_in=${checkInDate.toISOString()}, check_out=${checkOutDate.toISOString()}`
    );

    const query = container.resolve("query");

    // First, collect all variant IDs to batch the inventory query
    const allVariantIds = [];
    for (const config of actualRoomConfigs) {
      for (const variant of config.variants) {
        allVariantIds.push(variant.id);
      }
    }

    console.log(`Batching inventory query for ${allVariantIds.length} variants`);

    // Batch query all inventory entries at once - this is the main performance optimization
    const { data: allInventoryEntries } = await query.graph({
      entity: "room_inventory",
      filters: {
        inventory_item_id: allVariantIds,
      },
      fields: [
        "id",
        "inventory_item_id",
        "from_date",
        "to_date",
        "status",
        "available_quantity",
      ],
    });

    console.log(`Found ${allInventoryEntries?.length || 0} total inventory entries`);

    // Group inventory entries by variant ID for quick lookup
    const inventoryByVariant = {};
    if (allInventoryEntries) {
      for (const entry of allInventoryEntries) {
        if (!inventoryByVariant[entry.inventory_item_id]) {
          inventoryByVariant[entry.inventory_item_id] = [];
        }
        inventoryByVariant[entry.inventory_item_id].push(entry);
      }
    }

    // Check availability for each room configuration and its variants
    const availabilityResults = [];

    for (const config of actualRoomConfigs) {
      const availableVariants = [];
      const unavailableVariants = [];

      // Check availability for each variant (room)
      for (const variant of config.variants) {
        try {
          console.log(
            `Checking availability for room ${
              variant.id
            } from ${checkInDate.toISOString()} to ${checkOutDate.toISOString()}`
          );

          // Get inventory entries for this variant from our batched data
          const roomInventory = inventoryByVariant[variant.id] || [];

          console.log(
            `Found ${roomInventory.length} inventory entries for room ${variant.id}`
          );

          // Use optimized availability checking
          const availability = checkVariantAvailability(
            roomInventory,
            checkInDate,
            checkOutDate
          );

          if (availability.available) {
            availableVariants.push({
              ...variant,
              availability,
            });
          } else {
            unavailableVariants.push({
              ...variant,
              availability,
            });
          }
        } catch (error) {
          console.error(
            `Error checking availability for room ${variant.id}:`,
            error
          );
          unavailableVariants.push({
            ...variant,
            availability: {
              available: false,
              unavailableDates: [checkInDate.toISOString().split("T")[0]],
              unavailableReasons: [
                {
                  date: checkInDate.toISOString().split("T")[0],
                  reason: `Error checking availability: ${
                    error.message || "Unknown error"
                  }`,
                },
              ],
            },
          });
        }
      }

      // Add capacity validation for this room configuration
      const maxAdults = Number(config.metadata?.max_adults) || 1;
      const maxChildren = Number(config.metadata?.max_children) || 0;
      const maxInfants = Number(config.metadata?.max_infants) || 0;
      const maxAdultsBeyondCapacity =
        Number(config.metadata?.max_adults_beyond_capacity) || 0;
      const maxOccupancy = Number(config.metadata?.max_occupancy) || 1;
      const totalOccupancy = adults + children + infants;

      // Calculate capacity status
      const totalMaxAdultsAllowed = maxAdults + maxAdultsBeyondCapacity;
      const totalMaxOccupancyAllowed = totalMaxAdultsAllowed + maxChildren + maxInfants;
      const adultsExceedCapacity = adults > maxAdults;
      const adultsExceedMaxAllowed = adults > totalMaxAdultsAllowed;
      const childrenExceedCapacity = children > maxChildren;
      const infantsExceedCapacity = infants > maxInfants;
      const totalExceedsCapacity = totalOccupancy > totalMaxOccupancyAllowed;

      const capacityStatus = {
        max_adults: maxAdults,
        max_children: maxChildren,
        max_infants: maxInfants,
        max_adults_beyond_capacity: maxAdultsBeyondCapacity,
        total_max_adults_allowed: totalMaxAdultsAllowed,
        total_max_occupancy_allowed: totalMaxOccupancyAllowed,
        max_occupancy: maxOccupancy, // Keep for backward compatibility
        requested_adults: adults,
        requested_children: children,
        requested_infants: infants,
        requested_total_occupancy: totalOccupancy,
        adults_exceed_capacity: adultsExceedCapacity,
        adults_exceed_max_allowed: adultsExceedMaxAllowed,
        children_exceed_capacity: childrenExceedCapacity,
        infants_exceed_capacity: infantsExceedCapacity,
        total_exceeds_capacity: totalExceedsCapacity,
        adults_within_capacity: Math.min(adults, maxAdults),
        adults_beyond_capacity: Math.max(
          0,
          Math.min(adults - maxAdults, maxAdultsBeyondCapacity)
        ),
        adults_over_limit: Math.max(0, adults - totalMaxAdultsAllowed),
        capacity_warning: adultsExceedCapacity || childrenExceedCapacity || infantsExceedCapacity || totalExceedsCapacity,
        booking_allowed: !adultsExceedMaxAllowed && !childrenExceedCapacity && !infantsExceedCapacity && !totalExceedsCapacity,
      };

      // Add the room configuration with its available and unavailable variants plus capacity info
      availabilityResults.push({
        ...config,
        available_variants: availableVariants,
        unavailable_variants: unavailableVariants,
        available: availableVariants.length > 0,
        capacity_status: capacityStatus,
      });
    }

    return new StepResponse(availabilityResults);
  }
);
