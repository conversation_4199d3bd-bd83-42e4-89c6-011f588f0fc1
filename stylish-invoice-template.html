<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Booking Confirmation</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Baskervville:ital,wght@0,400;1,400&display=swap"
        rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Karla:wght@400;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Karla', sans-serif;
            font-weight: 400;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.4;
            font-size: 12px;
        }

        .invoice-container {
            max-width: 750px;
            margin: 0 auto;
            background: white;
            padding: 25px;
            border-radius: 8px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(40, 93, 166, 0.08);
        }

        .header {
            text-align: center;
            margin-bottom: 25px;
            border-bottom: 2px solid #285DA6;
            padding-bottom: 15px;
        }

        .header h1 {
            color: #285DA6;
            margin: 0;
            font-size: 2em;
            font-weight: 300;
        }

        .company-info {
            text-align: center;
            margin-bottom: 15px;
            color: #666;
        }

        .logo {
            text-align: center;
            margin-bottom: 10px;
        }

        .logo img {
            max-width: 80px;
            height: auto;
        }

        .booking-reference {
            margin-bottom: 20px;
            padding: 15px;
            background: rgba(40, 93, 166, 0.03);
            border-radius: 6px;
            border: 1px solid rgba(40, 93, 166, 0.1);
        }

        .booking-header {
            position: relative;
            margin-bottom: 0px;
            width: 100%;
            min-height: 45px;
        }

        .left-info {
            position: absolute;
            left: 0;
            top: 0;
            text-align: left;
        }

        .right-info {
            position: absolute;
            right: 0;
            top: 0;
            text-align: right;
        }

        .booking-reference p {
            margin: 5px 0;
            font-size: 0.9em;
            color: #555;
            letter-spacing: 0.2px;
        }

        .booking-reference strong {
            color: #285DA6;
            font-family: 'Karla', sans-serif;
            font-weight: 700;
        }

        .section-title {
            font-family: 'Karla', sans-serif;
            font-size: 0.95em;
            font-weight: 700;
            color: #285DA6;
            margin-bottom: 8px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .section {
            padding: 15px 0;
            margin-bottom: 15px;
            border-top: 1px solid rgba(40, 93, 166, 0.2);
            position: relative;
        }

        .section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 60px;
            height: 3px;
            background: #285DA6;
            border-radius: 2px;
        }

        .section h3 {
            margin-top: 0;
            margin-bottom: 12px;
            color: #285DA6;
            font-family: 'Baskervville', serif;
            font-size: 1.2em;
            font-weight: 400;
            font-style: italic;
            display: flex;
            align-items: center;
            gap: 8px;
            line-height: 1.2;
            min-height: 24px;
            letter-spacing: 0.3px;
        }

        .section-icon {
            width: 18px;
            height: 18px;
            fill: #285DA6;
            flex-shrink: 0;
            vertical-align: middle;
            display: inline-block;
            margin-right: 0;
            position: relative;
            top: 0;
            opacity: 1;
            stroke: #285DA6;
            stroke-width: 0.5px;
        }

        /* Alternative approach for PDF rendering */
        .section h3 svg,
        .add-ons-section h3 svg,
        .guest-details-section h3 svg,
        .policy-section h3 svg,
        .price-summary h3 svg {
            vertical-align: text-top;
            margin-top: 2px;
        }

        .section-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
            gap: 6px;
            margin-top: 6px;
        }

        .section-item {
            display: flex;
            justify-content: space-between;
            padding: 4px 0;
            font-size: 0.85em;
        }

        .section-item:hover {
            background-color: rgba(40, 93, 166, 0.02);
        }

        .section-label {
            font-family: 'Karla', sans-serif;
            font-weight: 700;
            color: #555;
            font-size: 0.95em;
        }

        .section-value {
            color: #333;
        }

        .add-ons-section {
            padding: 15px 0;
            margin-bottom: 20px;
            border-top: 2px solid #285DA6;
        }

        .add-ons-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #285DA6;
            font-family: 'Karla', sans-serif;
            font-size: 1.2em;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
            line-height: 1.2;
            min-height: 24px;
        }

        .add-ons-content {
            font-size: 0.8em;
            line-height: 1.4;
        }

        .add-on-dates {
            font-size: 0.75em;
            color: #666;
            font-weight: normal;
        }

        .add-on-item {
            margin-bottom: 5px;
            padding: 3px 0;
            border-bottom: 1px solid #e0e0e0;
        }

        .add-on-item strong {
            font-family: 'Karla', sans-serif;
            font-weight: 700;
            color: #333;
        }

        .add-on-item:last-child {
            border-bottom: none;
        }

        .guest-details-section {
            padding: 15px 0;
            margin-bottom: 20px;
            border-top: 2px solid #285DA6;
        }

        .guest-details-section h3 {
            margin-top: 0;
            margin-bottom: 15px;
            color: #285DA6;
            font-family: 'Karla', sans-serif;
            font-size: 1.2em;
            font-weight: 700;
            display: flex;
            align-items: center;
            gap: 8px;
            line-height: 1.2;
            min-height: 24px;
        }

        .guest-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
        }

        .guest-item {
            font-size: 0.9em;
            margin-bottom: 5px;
        }

        .guest-item strong {
            font-family: 'Karla', sans-serif;
            font-weight: 600;
            color: #333;
        }



        .policy-content {
            font-size: 0.8em;
            line-height: 1.4;
            color: #555;
        }

        .policy-content ul {
            margin: 0;
            padding-left: 15px;
        }

        .policy-content li {
            margin-bottom: 5px;
            line-height: 1.3;
        }


        .price-table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 10px;
            background: rgba(40, 93, 166, 0.02);
            border-radius: 6px;
            overflow: hidden;
        }

        .price-table th {
            padding: 12px 15px;
            background: rgba(40, 93, 166, 0.1);
            border-bottom: 2px solid rgba(40, 93, 166, 0.2);
            font-size: 0.9em;
            font-family: 'Karla', sans-serif;
            font-weight: 700;
            color: #285DA6;
            text-align: left;
        }

        .price-table th:nth-child(2),
        .price-table th:nth-child(3),
        .price-table th:nth-child(4) {
            text-align: right;
        }

        .price-table td {
            padding: 10px 15px;
            border-bottom: 1px solid rgba(40, 93, 166, 0.08);
            font-size: 0.85em;
            vertical-align: top;
        }

        .price-table td:first-child {
            text-align: left;
            color: #666;
            font-family: 'Karla', sans-serif;
            font-weight: 700;
        }

        .price-table td:nth-child(2),
        .price-table td:nth-child(3),
        .price-table td:nth-child(4) {
            text-align: right;
            font-family: 'Karla', sans-serif;
            font-weight: 700;
        }

        .price-table tr.subtotal td {
            color: #666;
        }

        .price-table tr.addon-main td:first-child {
            font-weight: 700;
            color: #333;
        }

        .price-table tr.addon-sub td {
            border-bottom: none !important;
            border-top: none !important;
            padding-top: 2px;
        }

        .price-table tr.addon-sub td:first-child {
            padding-left: 15px;
            font-weight: 400;
            color: #666;
            font-size: 0.8em;
            padding-top: 2px;
            text-align: left;
        }

        .price-table tr.addon-sub td:nth-child(2) {
            font-size: 0.8em;
            color: #666;
            text-align: center;
        }

        .price-table tr.addon-sub td:nth-child(3),
        .price-table tr.addon-sub td:nth-child(4) {
            font-size: 0.8em;
            color: #666;
            text-align: right;
        }

        .price-table tr.addon-main td {
            border-bottom: none !important;
        }

        .price-table tr.addon-main.has-subitems td {
            padding-bottom: 2px;
        }

        .price-table tr.addon-main td:first-child {
            text-align: left;
        }

        .price-table tr.addon-main td:nth-child(2) {
            text-align: center;
        }

        .price-table tr.addon-main td:nth-child(3),
        .price-table tr.addon-main td:nth-child(4) {
            text-align: right;
        }

        .price-table tr.addon-main:not(:first-child) td {
            border-top: 1px solid rgba(40, 93, 166, 0.15);
        }

        .price-table tr.final td {
            font-family: 'Karla', sans-serif;
            font-weight: 700;
            font-size: 1.1em;
            color: #285DA6;
            border-top: 2px solid #285DA6;
            border-bottom: none;
            padding-top: 12px;
        }

        .regards-section {
            text-align: center;
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #eee;
            color: #666;
            font-size: 0.75em;
            line-height: 1.4;
        }

        .regards-section strong {
            font-family: 'Karla', sans-serif;
            font-weight: 700;
            color: #333;
        }

        .thank-you-message {
            margin-top: 1px;
        }

        .thank-you-message .customer-name {
            font-family: 'Karla', sans-serif;
            font-weight: 700;
            color: #285DA6;
        }

        .empty-value {
            color: #999;
            font-style: italic;
        }

        .conditional-section {
            display: block;
        }

        .conditional-section.hidden {
            display: none;
        }

        @media (max-width: 600px) {
            body {
                padding: 6px;
            }

            .invoice-container {
                padding: 15px;
            }

            .booking-header {
                flex-direction: column;
            }

            .left-info,
            .right-info {
                text-align: left;
                margin-bottom: 1px;
            }

            .section-grid {
                grid-template-columns: 1fr;
            }

            .guest-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>

<body>
    <div class="invoice-container">
        <div class="company-info">
            <div class="logo">
                <img src="https://dev-assets.store.flinkk.io/powderbyrne/logo.png" alt="Perfect Piste Logo">
            </div>
        </div>

        <div class="booking-reference">
            <div class="booking-header">
                <div class="left-info">
                    <p><strong>Booking Reference:</strong></p>
                    <p>{{order.id}}</p>
                </div>
                <div class="right-info">
                    <p><strong>Date Issued:</strong></p>
                    <p>{{order.created_at_formatted}}</p>
                </div>
            </div>
            <div class="thank-you-message">
                <p>Dear <span class="customer-name">{{customer.first_name}} {{customer.last_name}}</span>, thank you for
                    booking with us! Your reservation at <strong>{{booking.hotel_name}}</strong> is confirmed. Below are
                    your booking details.</p>
            </div>
        </div>

        <!-- Outline Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path
                        d="M21 16V4a2 2 0 0 0-2-2H5a2 2 0 0 0-2 2v12a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2zM5 4h14v12H5V4zm2 2v2h2V6H7zm4 0v2h6V6h-6zm-4 4v2h2v-2H7zm4 0v2h6v-2h-6z" />
                </svg>
                Booking Outline
            </h3>
            <div class="section-grid">
                <div class="section-item">
                    <span class="section-label">Destination:</span>
                    <span class="section-value">{{booking.destination_name}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Accommodation:</span>
                    <span class="section-value">{{booking.hotel_name}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Room Type:</span>
                    <span class="section-value">{{booking.room_config_name}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Check-in:</span>
                    <span class="section-value">{{booking.check_in_date}} at {{booking.check_in_time}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Check-out:</span>
                    <span class="section-value">{{booking.check_out_date}} at {{booking.check_out_time}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Board Basis:</span>
                    <span class="section-value">{{booking.board_basis}}</span>
                </div>
            </div>
        </div>

        <!-- Property Details Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path d="M10 20v-6h4v6h5v-8h3L12 3 2 12h3v8z" />
                </svg>
                Property Details
            </h3>
            <div class="section-grid">
                <div class="section-item">
                    <span class="section-label">Address:</span>
                    <span class="section-value">{{booking.hotel_address}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Contact:</span>
                    <span class="section-value">{{booking.hotel_phone}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Email:</span>
                    <span class="section-value">{{booking.hotel_email}}</span>
                </div>
            </div>
        </div>

        <!-- Accommodation Details Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path
                        d="M7 13c1.66 0 3-1.34 3-3S8.66 7 7 7s-3 1.34-3 3 1.34 3 3 3zm12-6h-8v7H3V6H1v15h2v-3h18v3h2v-9c0-2.21-1.79-4-4-4z" />
                </svg>
                Accommodation Details
            </h3>
            <div class="section-grid">
                <div class="section-item">
                    <span class="section-label">Room Type:</span>
                    <span class="section-value">{{booking.room_config_name}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Board Basis:</span>
                    <span class="section-value">{{booking.board_basis}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">No. of Guests:</span>
                    <span class="section-value">{{booking.number_of_guests}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">No. of Nights:</span>
                    <span class="section-value">{{booking.number_of_nights}}</span>
                </div>
            </div>
        </div>

        <!-- Add-ons Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path d="M19 13h-6v6h-2v-6H5v-2h6V5h2v6h6v2z" />
                </svg>
                Add-on Services
            </h3>
            <div class="add-ons-content">
                {{add_ons_detailed}}
            </div>
        </div>

        <!-- Special Requests Section - Only show if there are special requests -->
        {{special_requests_section}}

        <!-- Primary Guest Details Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path
                        d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z" />
                </svg>
                Primary Guest Details
            </h3>
            <div class="section-grid">
                <div class="section-item">
                    <span class="section-label">Name:</span>
                    <span class="section-value">{{customer.first_name}} {{customer.last_name}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Email:</span>
                    <span class="section-value">{{customer.email}}</span>
                </div>
                <div class="section-item">
                    <span class="section-label">Contact:</span>
                    <span class="section-value">{{customer.guest_phone}}</span>
                </div>
            </div>
        </div>

        <!-- Guest Details Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path
                        d="M16 4c0-1.11.89-2 2-2s2 .89 2 2-.89 2-2 2-2-.89-2-2zM4 18v-4h3v-3c0-1.1.9-2 2-2h2c1.1 0 2 .9 2 2v3h3v4H4zm12-5.5c0 .83-.67 1.5-1.5 1.5S13 13.33 13 12.5s.67-1.5 1.5-1.5 1.5.67 1.5 1.5zM22 22H2v-2h20v2z" />
                </svg>
                All Guest Details
            </h3>
            <div class="guest-list">
                {{travelers_list}}
            </div>
        </div>

        <!-- Cancellation Policy Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm4 18H6V4h7v5h5v11z" />
                </svg>
                Cancellation Policy
            </h3>
            <div class="policy-content">
                {{booking.cancellation_policy}}
            </div>
        </div>

        <!-- Property Rules Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path
                        d="M3 5v14c0 1.1.89 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2H5c-1.11 0-2 .9-2 2zm12 4c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1s.45-1 1-1h7c.55 0 1 .45 1 1zm0 4c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1s.45-1 1-1h7c.55 0 1 .45 1 1zm0 4c0 .55-.45 1-1 1H7c-.55 0-1-.45-1-1s.45-1 1-1h7c.55 0 1 .45 1 1z" />
                </svg>
                Property Rules
            </h3>
            <div class="policy-content">
                {{booking.property_rules}}
            </div>
        </div>

        <!-- Price Summary Section -->
        <div class="section">
            <h3>
                <svg class="section-icon" viewBox="0 0 24 24">
                    <path
                        d="M11.8 10.9c-2.27-.59-3-1.2-3-2.15 0-1.09 1.01-1.85 2.7-1.85 1.78 0 2.44.85 2.5 2.1h2.21c-.07-1.72-1.12-3.3-3.21-3.81V3h-3v2.16c-1.94.42-3.5 1.68-3.5 3.61 0 2.31 1.91 3.46 4.7 4.13 2.5.6 3 1.48 3 2.41 0 .69-.49 1.79-2.7 1.79-2.06 0-2.87-.92-2.98-2.1h-2.2c.12 2.19 1.76 3.42 3.68 3.83V21h3v-2.15c1.95-.37 3.5-1.5 3.5-3.55 0-2.84-2.43-3.81-4.7-4.4z" />
                </svg>
                Price Summary
            </h3>
            <table class="price-table">
                <thead>
                    <tr>
                        <th>Items</th>
                        <th>Quantity</th>
                        <th>Rate</th>
                        <th>Total</th>
                    </tr>
                </thead>
                <tbody>
                    {{items_html_block}}
                    {{add_ons_pricing}}
                    <tr class="final">
                        <td>Total Amount</td>
                        <td></td>
                        <td></td>
                        <td>{{booking.total}}</td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- Regards Section -->
        <div class="regards-section">
            <p>Thank you for choosing {{booking.hotel_name}}. We are honored to be your hosts and are dedicated to
                curating a seamless experience for you. Should you require any personalized arrangements ahead of your
                arrival, please don't hesitate to contact
                our concierge team.</p>
            <p><strong>Warm regards,<br>The Perfect Piste Team</strong></p>
            <p><EMAIL></p>
        </div>
    </div>
</body>

</html>